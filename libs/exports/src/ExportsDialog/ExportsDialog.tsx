/* eslint-disable jsx-a11y/iframe-has-title */
import ArticleIcon from '@mui/icons-material/Article';
import LinkIcon from '@mui/icons-material/Link';
import PrintIcon from '@mui/icons-material/Print';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import ListItemText from '@mui/material/ListItemText';
import { MutableRefObject, useLayoutEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

export interface ExportDocument {
    path: string;
    params: Record<string, string>;
}

interface Props {
    documents: Array<ExportDocument>;
    open: DialogProps['open'];
    onClose: DialogProps['onClose'];
}

export function ExportsDialog({ documents, open, onClose }: Props) {
    const { t } = useTranslation('common');
    const iframes = useRef<Record<string, HTMLIFrameElement>>({});

    return (
        <Dialog open={open} onClose={onClose} fullWidth maxWidth={'md'}>
            <DialogContent>
                <List>
                    {documents.map((doc, index) => (
                        <Item key={`${doc.path}-${index}`} innerRef={iframes} {...doc} />
                    ))}
                </List>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose as any}>{t('cancel')}</Button>
            </DialogActions>
        </Dialog>
    );
}

function Item({
    path,
    params,
    innerRef,
}: ExportDocument & { innerRef: MutableRefObject<Record<string, HTMLIFrameElement>> }) {
    const ref = useRef<HTMLIFrameElement>(null);
    const [text, setText] = useState('Loading...');
    const url = new URL(`${path.replace(/^\//, '')}?${new URLSearchParams(params)}`, window.location.origin).toString();

    const handleLoad = () => {
        const interval = setInterval(() => {
            if (ref.current) {
                const title = ref.current?.contentDocument?.title ?? '';
                if (title.endsWith('.pdf')) {
                    setText(title);
                    clearInterval(interval);
                }
            }
        }, 100);
    };

    const handlePrint = () => {
        ref.current?.contentWindow?.print();
    };

    useLayoutEffect(() => {
        if (ref.current) {
            innerRef.current[url] = ref.current;
        }
    }, [innerRef, url]);

    return (
        <ListItem>
            <ListItemIcon sx={{ minWidth: 32 }}>
                <ArticleIcon />
            </ListItemIcon>
            <ListItemText primary={text} />
            <ListItemSecondaryAction>
                {text === 'Loading...' ? (
                    <CircularProgress size={24} color="primary" />
                ) : (
                    <>
                        <IconButton href={url} target="_blank">
                            <LinkIcon />
                        </IconButton>
                        <IconButton onClick={handlePrint}>
                            <PrintIcon />
                        </IconButton>
                    </>
                )}
            </ListItemSecondaryAction>

            <iframe ref={ref} src={url} style={{ display: 'none' }} onLoad={handleLoad} />
        </ListItem>
    );
}
