import PrintIcon from '@mui/icons-material/Print';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { DateLike, formatDate } from '@bookr-technologies/core/datetime';
import { ReactComponent as Logo } from '@bookr-technologies/ui/assets/darkBookrLogo.svg';

interface Props {
    date: DateLike;
    appointments: number;
    staffMember: string;
}

export function AppointmentsDocumentHeader({ date, appointments, staffMember }: Props) {
    const { t } = useTranslation('common');

    const handlePrint = useCallback(() => {
        window.print();
    }, []);

    return (
        <Grid container mb={5} sx={{ mt: 3, '@media print': { mt: 0 } }}>
            <Grid item xs>
                <table>
                    <tbody>
                        <Typography variant="subtitle1" component="tr">
                            <td>{t('date')}: </td>
                            <td>
                                <Box component={'b'} px={2}>
                                    {formatDate(date, 'LL')}
                                </Box>
                            </td>
                        </Typography>
                        <Typography variant="subtitle1" component="tr">
                            <td>{t('numberOfAppointments')}: </td>
                            <td>
                                <Box component={'b'} px={2}>
                                    {appointments}
                                </Box>
                            </td>
                        </Typography>
                        <Typography variant="subtitle1" component="tr">
                            <td>{t('staffMemberName')}: </td>
                            <td>
                                <Box component={'b'} px={2}>
                                    {staffMember}
                                </Box>
                            </td>
                        </Typography>
                    </tbody>
                </table>
            </Grid>

            <Grid
                item
                xs
                container
                alignItems={'flex-end'}
                displayPrint={'none'}
                justifyContent={'center'}
                direction={'column'}
            >
                <Button variant="contained" size="small" startIcon={<PrintIcon />} onClick={handlePrint}>
                    {t('print')}
                </Button>
                <Typography variant="caption" textAlign={'right'} maxWidth={300} mt={2} lineHeight={'14px'}>
                    {t('printRecommendation')}
                </Typography>
            </Grid>

            <Grid
                item
                xs
                container
                direction={'column'}
                alignItems={'flex-end'}
                display={'none'}
                displayPrint={'flex'}
                justifyContent={'center'}
            >
                <Box display={'flex'} flexDirection={'column'}>
                    <Logo />
                    <Typography variant="caption">{t('poweredByBookr')}</Typography>
                </Box>
            </Grid>
        </Grid>
    );
}
