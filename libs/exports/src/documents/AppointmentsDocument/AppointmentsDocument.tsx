import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, useTheme } from '@mui/material/styles';
import { countBy, groupBy, snakeCase } from 'lodash';
import moment from 'moment';
import { readableColor } from 'polished';
import { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import { Trans, useTranslation } from 'react-i18next';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { formatDate } from '@bookr-technologies/core/datetime';
import { AppointmentEvent } from './AppointmentEvent';
import { AppointmentsDocumentHeader } from './AppointmentsDocumentHeader';
import { AppointmentsDocumentRowTime } from './AppointmentsDocumentRowTime';

export type AppointmentDocumentItem = (AppointmentModel & { type: 'appointment' }) | (BreakModel & { type: 'break' });

interface Props {
    staffMember: UserModel | null;
    items: AppointmentDocumentItem[];
    date?: Date;
    print?: boolean;
}

export function AppointmentsDocument({ items, date, staffMember, print }: Props) {
    const theme = useTheme();
    const { t } = useTranslation('common');

    const documentDate = date ?? new Date();
    const events: AppointmentEvent[] = items
        .map((item, index) => {
            const appointment = item.type === 'appointment' ? item : null;
            const breakModel = item.type === 'break' ? item : null;

            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-ignore
            const color = appointment?.service?.color ?? theme.palette.accent.main;

            return {
                id: appointment?.id ?? breakModel?.id ?? index,
                start: moment(appointment?.dateTime ?? breakModel?.fromDateTime),
                end: appointment
                    ? moment(appointment.dateTime).add(appointment.service.duration, 'minutes')
                    : moment(breakModel?.toDateTime),

                title: appointment?.client?.displayName ?? breakModel?.title ?? '',
                summary: appointment?.service?.name ?? 'Break',
                color,
                readableColor: readableColor(color),
            };
        })
        .sort((a, b) => a.start.unix() - b.start.unix());

    const groupedEvents = groupBy(events, ({ start }) => moment(start).startOf('hour').format('HH:mm'));
    const numberOfAppointments = countBy(items, 'type')['appointment'] ?? 0;

    useEffect(() => {
        if (print) {
            window.print();
        }
    }, [print]);

    return (
        <Root>
            <Helmet>
                <title>{`${snakeCase(staffMember?.displayName ?? 'unknown')}_${formatDate(
                    documentDate,
                    'YYYY-MM-DD',
                )}.pdf`}</title>
            </Helmet>
            <AppointmentsDocumentHeader
                appointments={numberOfAppointments}
                staffMember={staffMember?.displayName || ''}
                date={documentDate}
            />

            {Object.entries(groupedEvents).map(([key, events]) => (
                <Box display={'block'} key={key} width={'100%'} sx={{ breakInside: 'avoid-page' }}>
                    <Grid
                        container
                        alignItems={'center'}
                        justifyContent={'space-between'}
                        sx={{ breakInside: 'avoid-page' }}
                        pt={5}
                    >
                        <Typography variant="h5" fontFamily={''} mb={1} fontWeight={500}>
                            {key}
                        </Typography>
                        <Typography variant="caption">
                            <Trans
                                t={t}
                                i18nKey={'appointmentsInThisHour'}
                                tOptions={{ value: events.length }}
                                components={{ b: <b /> }}
                            />
                        </Typography>
                    </Grid>

                    <Grid
                        container
                        borderRadius={2}
                        border={'1px solid #666'}
                        display={'block'}
                        sx={{ breakInside: 'avoid-page' }}
                    >
                        {events.map((event) => (
                            <Grid
                                key={event.id}
                                container
                                direction={'row'}
                                px={2}
                                py={1}
                                alignItems={'center'}
                                justifyContent={'space-between'}
                                flexWrap={'nowrap'}
                                borderBottom={'1px solid #666'}
                                sx={{
                                    breakInside: 'avoid-page',
                                    '&:last-child': { borderBottom: 'none' },
                                }}
                            >
                                <AppointmentsDocumentRowTime event={event} />

                                <Avatar sx={{ backgroundColor: event.color }}>
                                    <Box
                                        sx={{
                                            backgroundColor: '#fff',
                                            width: 18,
                                            height: 18,
                                            borderRadius: 1,
                                        }}
                                    />
                                </Avatar>
                            </Grid>
                        ))}
                    </Grid>
                </Box>
            ))}
        </Root>
    );
}

const Root = styled(Container)({
    minHeight: '100vh',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    paddingBottom: 40,
});
