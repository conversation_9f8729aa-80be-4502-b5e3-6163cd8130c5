import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import moment from 'moment/moment';
import { useTranslation } from 'react-i18next';
import { AppointmentEvent } from './AppointmentEvent';

interface Props {
    event: AppointmentEvent;
}

export function AppointmentsDocumentRowTime({ event }: Props) {
    const { t } = useTranslation('common');

    return (
        <Grid container alignItems={'center'}>
            <Box
                display={'flex'}
                alignItems={'flex-start'}
                flexDirection={'column'}
                pr={2}
                width={136}
                boxSizing={'border-box'}
            >
                <table>
                    <colgroup>
                        <col width="80" />
                        <col width="40" />
                    </colgroup>
                    <tbody>
                        <Typography variant="subtitle1" component={'tr'} lineHeight={'18px'}>
                            <Box display={'inline-block'} component={'td'}>
                                {t('from')}
                            </Box>{' '}
                            <td style={{ textAlign: 'right' }}>
                                <b>{moment(event.start).format('HH:mm')}</b>
                            </td>
                        </Typography>
                        <Typography variant="subtitle1" component={'tr'} lineHeight={'18px'}>
                            <Box display={'inline-block'} component={'td'}>
                                {t('to')}
                            </Box>{' '}
                            <td style={{ textAlign: 'right' }}>
                                <b>{moment(event.end).format('HH:mm')}</b>
                            </td>
                        </Typography>
                    </tbody>
                </table>
            </Box>
            <Divider variant="middle" orientation={'vertical'} flexItem color={'#aaa'} />
            <Grid container item xs direction={'column'} pl={2}>
                <Typography variant="h6" fontWeight={500}>
                    {event.title}
                </Typography>
                <Typography variant="body1" sx={{ opacity: 0.9 }}>
                    {event.summary}
                </Typography>
            </Grid>
        </Grid>
    );
}
