{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/exports/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/exports/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/exports"], "options": {"jestConfig": "libs/exports/jest.config.ts", "passWithNoTests": true}}}}