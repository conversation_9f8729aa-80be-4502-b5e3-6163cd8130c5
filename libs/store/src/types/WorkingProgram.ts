import { WorkingHourModel } from '@bookr-technologies/api/models/WorkingHourModel';

export const WorkingProgramDays = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday',
] as const;

export interface WorkingProgramDayType {
    enabled: boolean;
    values: Omit<WorkingHourModel, 'id' | 'day'>[];
}

export type WorkingProgramRecord = Record<typeof WorkingProgramDays[number], WorkingProgramDayType>;
