import { AxiosError } from 'axios';
import { ConfirmationResult, User } from 'firebase/auth';
import i18next from 'i18next';
import moment from 'moment';
import { create } from 'zustand';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { SubscriptionModel } from '@bookr-technologies/api/models/SubscriptionModel';
import { UserMetadataModel } from '@bookr-technologies/api/models/UserMetadataModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { delay } from '@bookr-technologies/core/promises';
import { FirebaseService } from '@bookr-technologies/core/services';
import { queryClient } from './queryClient';

export interface AuthStoreType {
    authenticated: boolean | null;
    user: UserModel | null;
    firebaseUser: User | null;
    token: string | null;
    confirmationResult: ConfirmationResult | null;
    metadata: UserMetadataModel | null;
    subscription: SubscriptionModel | null;
    setAuthenticated(authenticated: boolean | null): void;
    setUser(user: UserModel | null): void;
    setToken(token: string | null): void;
    setMetadata(metadata: UserMetadataModel | null): void;
    setConfirmationResult(confirmationResult: ConfirmationResult | null): void;
    resolveUser(): Promise<void>;
    logout(): Promise<void>;
}

export const useAuthStore = create<AuthStoreType>((set, get) => ({
    authenticated: null,
    user: null,
    firebaseUser: null,
    token: null,
    confirmationResult: null,
    metadata: null,
    subscription: null,
    setAuthenticated(authenticated) {
        return set({ authenticated });
    },
    setUser(user) {
        return set({ user });
    },
    setMetadata(metadata) {
        return set({ metadata });
    },
    setToken(token) {
        return set({ token });
    },
    setConfirmationResult(confirmationResult) {
        return set({ confirmationResult });
    },
    async resolveUser() {
        try {
            const firebaseUser = FirebaseService.auth().currentUser;
            const token = (await firebaseUser?.getIdToken()) ?? null;
            set({ token, firebaseUser });

            const uid = get().user?.uid || firebaseUser?.uid;
            if (!uid) {
                set({ user: null, authenticated: true });
                return;
            }

            let retries = 0;

            const retry = async (id: string): Promise<{ user: UserModel; metadata: UserMetadataModel }> => {
                retries++;
                await delay(300);

                try {
                    const user = await usersEndpoint.show(id);
                    const metadata = await usersEndpoint.getMetadata(user.email);

                    user.services = (user.services || []).filter(Boolean).sort(ServiceModel.sort);

                    set({ authenticated: true, user, metadata });

                    return { user, metadata };
                } catch (e) {
                    if ((e as AxiosError).response?.status === 404 && retries < 5) {
                        return retry(id);
                    } else {
                        throw e;
                    }
                }
            };

            const { user, metadata } = await retry(uid);
            if (user.business && metadata.verified) {
                const subscription = await businessEndpoint.getSubscription();
                set({ subscription });
            }

            // Locale#week.dow should be an integer representing the first day of the week, 0 is Sunday, 1 is Monday, ..., 6 is Saturday.
            const language = user.language.split('-')[0];
            moment.updateLocale(language, { week: { dow: 1 } });
            await i18next.changeLanguage(language);
        } catch (e) {
            set({ user: null, authenticated: true });
        }
    },
    async logout() {
        try {
            await FirebaseService.auth().signOut();
        } catch (e) {
            // Nothing to do
        }
        set({ user: null, metadata: null, authenticated: false });
        queryClient.clear();
    },
}));
