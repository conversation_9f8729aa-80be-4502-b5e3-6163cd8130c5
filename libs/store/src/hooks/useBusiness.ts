import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { useAuthStore } from '../authStore';
import { useBusinessStore } from '../businessStore';
import { queryClient } from '../queryClient';

export const useBusiness = (businessId?: string) => {
    const userBusinessId = useAuthStore((state) => state.user?.business.id);
    const setBusiness = useBusinessStore((state) => state.setBusiness);
    const id = businessId || userBusinessId;
    const queryKey = ['business', id];

    return useQuery(
        queryKey,
        async () => {
            if (!id) {
                return;
            }

            const business = await businessEndpoint.show(id);
            setBusiness(business);
            return business;
        },
        {
            enabled: !!id,
            initialData: () => queryClient.getQueryData(queryKey),
        },
    );
};
