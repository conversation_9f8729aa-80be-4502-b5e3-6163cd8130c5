import { User } from 'firebase/auth';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useAuthStore } from '../authStore';

/**
 * Hook for getting the current user
 */
export function useCurrentUser(): UserModel | null {
    return useAuthStore((state) => state.user);
}

/**
 * Keep in mind that this hook depends on the auth session,
 * we force infer the user type, but if you are using outside or before
 * auth session will result in a null return, even if the hook return UserModel type.
 *
 * So in case you are not sure if the component will be rendered within the auth middleware,
 * use the useCurrentUser() hook instead, which has optional user type,
 */
export function useUser(): UserModel {
    const user = useAuthStore((state) => state.user);

    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return user!;
}

/**
 * Hook for getting the firebase user.
 */
export function useFirebaseUser(): User | null {
    return useAuthStore((state) => state.firebaseUser);
}
