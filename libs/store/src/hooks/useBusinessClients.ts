import { useQuery, UseQueryOptions } from 'react-query';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { businessClientEndpoint } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { ClientDetailsModel } from '@bookr-technologies/api/models/ClientDetailsModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { PageableResponse } from '@bookr-technologies/api/types/PageableResponse';
import { useAuthStore } from '../authStore';

export const useBusinessClients = (
    textSearch = '',
    page = 0,
    size = 20,
    options?: UseQueryOptions<PageableResponse<UserModel>>,
) => {
    const user = useAuthStore((state) => state.user);
    const businessId = user?.business.id;

    return useQuery<PageableResponse<UserModel>>({
        queryFn: () =>
            businessClientsEndpoint.getClients({
                businessId,
                textSearch,
                page,
                size,
            }),
        queryKey: `business:clients:${businessId}:${textSearch}:${page}`,
        ...options,
    });
};

export const useBusinessClient = (clientId?: string) => {
    return useQuery<ClientDetailsModel>(`business:client:${clientId}`, () => {
        if (!clientId) {
            return Promise.reject();
        }
        return businessClientEndpoint.show(clientId);
    });
};
