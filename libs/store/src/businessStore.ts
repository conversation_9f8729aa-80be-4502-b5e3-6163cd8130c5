import { Coords } from 'google-map-react';
import moment from 'moment-timezone';
import { create } from 'zustand';
import { FetchBusinessDTO } from '@bookr-technologies/api/dto/FetchBusinessDTO';
import { businessEndpoint } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { BusinessModel } from '@bookr-technologies/api/models/BusinessModel';
import { ID } from '@bookr-technologies/api/types/id';
import { DEFAULT_CALLING_CODE } from '@bookr-technologies/core/constants';
import { useAuthStore } from './authStore';
import { WorkingProgramRecord } from './types/WorkingProgram';
import { initialWorkingProgram, workingProgramToWorkingHours } from './utils/workingProgram';

export interface BusinessSignUpForm {
    businessName: string;
    email: string;
    callingCode: string;
    phoneNumber: string;
    categories: string[];
    locationName: string;
    location: Coords | null;
    locationDelta: Coords;
    workingProgram: WorkingProgramRecord;
}

const initialSignUpForm: BusinessSignUpForm = {
    businessName: '',
    email: '',
    callingCode: DEFAULT_CALLING_CODE,
    phoneNumber: '',
    categories: [],
    locationName: '',
    location: null,
    locationDelta: {
        lat: 0.0025,
        lng: 0.0025,
    },
    workingProgram: initialWorkingProgram(),
};

export interface BusinessStoreType {
    entities: Record<ID, BusinessModel>;
    business: BusinessModel | null;
    signUpForm: BusinessSignUpForm;
    commitOperationsCounter: number;
    setBusiness(business: BusinessModel | null): void;
    commitSignUpForm(values: Partial<BusinessSignUpForm>): void;
    createBusiness(): Promise<BusinessModel>;
    attachProfilePicture(file: File): Promise<BusinessModel>;
    fetchBusiness(id: ID, params?: FetchBusinessDTO): Promise<BusinessModel | null>;
    fetchCurrentUserBusiness(params?: FetchBusinessDTO): Promise<BusinessModel | null>;
}
export const useBusinessStore = create<BusinessStoreType>((set, get) => ({
    entities: {},
    business: null,
    signUpForm: initialSignUpForm,
    commitOperationsCounter: 0,
    setBusiness(business: BusinessModel | null) {
        set({ business });
    },
    commitSignUpForm(values) {
        set((state) => {
            state.commitOperationsCounter += 1;
            state.signUpForm = {
                ...state.signUpForm,
                ...values,
            };
            return state;
        });
    },
    async createBusiness(): Promise<BusinessModel> {
        const form = get().signUpForm;

        const business = await businessEndpoint.create({
            name: form.businessName,
            phoneNumber: `${form.callingCode}${form.phoneNumber}`,
            categories: form.categories.map((name) => ({ name })),
            zoneId: moment.tz.guess(),
            latitude: form.location?.lat,
            latitudeDelta: form.locationDelta.lat,
            longitude: form.location?.lng,
            longitudeDelta: form.locationDelta.lng,
            formattedAddress: form.locationName,
            workingHours: workingProgramToWorkingHours(form.workingProgram),
        });

        set({ business, commitOperationsCounter: 0, signUpForm: initialSignUpForm });

        return business;
    },
    async attachProfilePicture(file: File): Promise<BusinessModel> {
        const business = await businessEndpoint.attachProfilePicture(file);

        set({ business });

        return business;
    },
    async fetchBusiness(id, params): Promise<BusinessModel | null> {
        const business = await businessEndpoint.show(id, { params });
        const entities = {
            ...get().entities,
            [business.id]: business,
        };

        set({ entities });
        return business;
    },
    async fetchCurrentUserBusiness(params): Promise<BusinessModel | null> {
        const user = useAuthStore.getState().user;
        if (!user) {
            return null;
        }

        const { fetchBusiness } = get();
        const business = await fetchBusiness(user.business.id, params);
        set({ business });
        return business;
    },
}));
