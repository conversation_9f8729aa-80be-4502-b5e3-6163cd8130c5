import { create } from 'zustand';

export interface PageStoreType {
    title: string | null;
    subtitle: string | null;
    backToHref: string | null;
    backToLabel: string | null;
    fullscreen: boolean;
    setTitle(title: PageStoreType['title']): void;
    setSubtitle(subtitle: PageStoreType['subtitle']): void;
    setBackToHref(backToHref: PageStoreType['backToHref']): void;
    setBackToLabel(backToLabel: PageStoreType['backToLabel']): void;
    setFullscreen(fullscreen: PageStoreType['fullscreen']): void;
}

export const usePageStore = create<PageStoreType>((set) => ({
    title: null,
    subtitle: null,
    backToHref: null,
    backToLabel: null,
    fullscreen: false,
    setTitle: (title: string) => set({ title }),
    setSubtitle: (subtitle: string) => set({ subtitle }),
    setBackToHref: (backToHref: string) => set({ backToHref }),
    setBackToLabel: (backToLabel: string) => set({ backToLabel }),
    setFullscreen: (fullscreen: boolean) => set({ fullscreen }),
}));
