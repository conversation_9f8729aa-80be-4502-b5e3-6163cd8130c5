import moment from 'moment';
import { WorkingDay } from '@bookr-technologies/api/constants/WorkingDay';
import { WorkingHourModel } from '@bookr-technologies/api/models/WorkingHourModel';
import { WorkingProgramRecord } from '../types/WorkingProgram';

export function initialWorkingProgram(): WorkingProgramRecord {
    return {
        friday: { enabled: true, values: [{ start: '09:00', end: '17:00', lastUpdatedAt: '' }] },
        monday: { enabled: true, values: [{ start: '09:00', end: '17:00', lastUpdatedAt: '' }] },
        saturday: { enabled: true, values: [{ start: '09:00', end: '17:00', lastUpdatedAt: '' }] },
        sunday: { enabled: true, values: [{ start: '09:00', end: '17:00', lastUpdatedAt: '' }] },
        thursday: { enabled: true, values: [{ start: '09:00', end: '17:00', lastUpdatedAt: '' }] },
        tuesday: { enabled: true, values: [{ start: '09:00', end: '17:00', lastUpdatedAt: '' }] },
        wednesday: { enabled: true, values: [{ start: '09:00', end: '17:00', lastUpdatedAt: '' }] },
    };
}

export function workingHoursToWorkingProgram(workingHours: WorkingHourModel[]): WorkingProgramRecord {
    workingHours = WorkingHourModel.convertHoursToLocalTimezone(workingHours);

    return Object.values(WorkingDay).reduce((acc, day) => {
        const values = workingHours.filter((hour) => hour.day.toLowerCase() === day.toLowerCase());

        return {
            ...acc,
            [day.toLowerCase()]: {
                enabled: values.length > 0,
                values,
            },
        };
    }, {} as WorkingProgramRecord);
}

export function workingProgramToWorkingHours(
    workingProgram: WorkingProgramRecord,
): Omit<WorkingHourModel, 'id' | 'lastUpdatedAt'>[] {
    const workingHours = Object.entries(workingProgram)
        .map(([day, { enabled, values }]) => {
            if (!enabled) {
                return [];
            }

            return values.map(({ start, end }) => ({
                day: day.toUpperCase() as WorkingDay,
                start: moment(typeof start === 'string' ? start : start.toISOString(), 'HH:mm').format('HH:mm'),
                end: moment(typeof end === 'string' ? end : end.toISOString(), 'HH:mm').format('HH:mm'),
            }));
        })
        .flat(2);

    return WorkingHourModel.convertHoursToUTC(workingHours);
}
