{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/api/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nrwl/web:rollup", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/api", "tsConfig": "libs/api/tsconfig.lib.json", "project": "libs/api/package.json", "entryFile": "libs/api/src", "external": [], "rollupConfig": "tools/rollup/rollup.lib.js", "format": ["esm", "cjs"], "compiler": "swc", "assets": [{"glob": "libs/api/README.md", "input": ".", "output": "."}]}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/api"], "options": {"jestConfig": "libs/api/jest.config.ts", "passWithNoTests": true}}}}