import { AxiosInstance, AxiosRequestConfig } from 'axios';
import { ID } from './id';

export interface APIResource<R> {
    api: AxiosInstance;
    list<T = R>(config?: AxiosRequestConfig): Promise<T[]>;
    show<T = R>(resourceId: ID, config?: AxiosRequestConfig): Promise<T>;
    update<T = R, D = T>(resourceId: ID, data: D, config?: AxiosRequestConfig): Promise<T>;
    destroy<T = R>(resourceId: ID, config?: AxiosRequestConfig): Promise<T>;
    create<T = R, D = T>(data: D, config?: AxiosRequestConfig): Promise<T>;
}
