import { AxiosRequestConfig } from 'axios';
import { createResource } from '../createResource';
import { IntegrationMap, IntegrationTypeEnum } from './integration.enum';
import { IntegrationModel } from './integration.model';

interface Methods {
    authorizeUrl(integrationType: IntegrationTypeEnum, config?: AxiosRequestConfig): Promise<string>;
}

export const IntegrationEndpoint = createResource<IntegrationModel, Methods>(
    'integrations',
    ({ api }) =>
        ({
            async authorizeUrl(integrationType, config) {
                const { data } = await api.get<{ url: string }>(`/${IntegrationMap[integrationType]}/authorize`, {
                    ...config,
                    params: {
                        ...config?.params,
                        redirect_uri: window.location.href,
                    },
                });

                return data.url;
            },
        } as const),
);
