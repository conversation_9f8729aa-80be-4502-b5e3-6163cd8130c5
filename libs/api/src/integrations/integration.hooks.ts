import { useMutation, useQuery } from 'react-query';
import { queryClient } from '@bookr-technologies/store/queryClient';
import { IntegrationEndpoint } from './integration.endpoint';
import { IntegrationTypeEnum } from './integration.enum';

function integrationsQueryKey() {
    return ['integrations'];
}

export function useIntegrationsQuery() {
    function queryFn() {
        return IntegrationEndpoint.list();
    }

    return useQuery({
        queryKey: integrationsQueryKey(),
        queryFn,
        initialData: () => queryClient.getQueryData(integrationsQueryKey()),
    });
}

export function useGetAuthorizationUrlMutation() {
    return useMutation((integrationType: IntegrationTypeEnum) => IntegrationEndpoint.authorizeUrl(integrationType));
}
