/* eslint-disable @typescript-eslint/no-explicit-any */
import { AxiosRequestConfig } from 'axios';
import { createEndpoint } from './createEndpoint';
import { APIResource } from './types/APIResource';
import { ID } from './types/id';

// eslint-disable-next-line @typescript-eslint/ban-types
export function createResource<R, T extends Record<string, any> = object>(
    url: string,
    extraMethods?: T | ((resource: APIResource<R>) => T),
    config: AxiosRequestConfig = {},
): APIResource<R> & T {
    const api = createEndpoint(url, config);

    const resource: APIResource<R> = {
        api,
        list: async (config) => {
            const { data } = await api.get('', config);
            return data;
        },
        show: async (resourceId: ID, config) => {
            const { data } = await api.get(`/${resourceId}`, config);
            return data;
        },
        update: async (resourceId: ID, data, config) => {
            const { data: result } = await api.put(`/${resourceId}`, data, config);
            return result;
        },
        destroy: async (resourceId: ID, config) => {
            const { data } = await api.delete(`/${resourceId}`, config);
            return data;
        },
        create: async (data, config) => {
            const { data: result } = await api.post('', data, config);
            return result;
        },
    };

    if (extraMethods) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const extra = typeof extraMethods === 'function' ? extraMethods(resource) : extraMethods;
        return {
            ...resource,
            ...extra,
        };
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return resource as any;
}
