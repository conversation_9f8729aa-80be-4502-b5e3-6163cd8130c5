import { createResource } from '../createResource';
import { ActivityModel } from '../models/ActivityModel';
import { FetchParams } from '../types/FetchParams';
import { PageableResponse } from '../types/PageableResponse';

interface ActivityExtraMethods {
    fetchData(params: FetchParams): Promise<PageableResponse<ActivityModel>>;
}

export const activityEndpoint = createResource<ActivityModel, ActivityExtraMethods>('activity', ({ api }) => ({
    async fetchData(params: FetchParams): Promise<PageableResponse<ActivityModel>> {
        const { data } = await api.get('', { params });
        return data;
    },
}));
