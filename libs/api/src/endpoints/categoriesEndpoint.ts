import { createResource } from '../createResource';
import { CategoryModel } from '../models/CategoryModel';

export const categoriesEndpoint = createResource<
    CategoryModel,
    {
        fetchAvailableCategories(): Promise<CategoryModel[]>;
    }
>('categories', () => ({
    fetchAvailableCategories: async () => {
        return [
            { id: 1, name: 'Hairsty<PERSON>', code: 'HAIRSTYLING' },
            { id: 2, name: '<PERSON>', code: 'BARB<PERSON>' },
            { id: 3, name: 'Make-up', code: 'MAKEUP' },
            { id: 4, name: 'Cosmetics', code: 'COSMETICS' },
            { id: 5, name: 'Manicure', code: 'MANICURE' },
            { id: 6, name: 'Massage', code: 'MA<PERSON>AG<PERSON>' },
            { id: 10, name: 'Dentistry', code: 'DENTISTRY' },
            { id: 11, name: 'Ophthalmology', code: 'OPHTHALMOLOGY' },
            { id: 12, name: 'Dermatology', code: 'DERMATOLOGY' },
            { id: 13, name: '<PERSON>', code: '<PERSON>UR<PERSON>R<PERSON>' },
            { id: 14, name: 'Psychologist', code: '<PERSON>YCH<PERSON>OGI<PERSON>' },
            { id: 15, name: 'Nutrition_Dietetics', code: 'NUTRITION_DIETETICS' },
            { id: 16, name: 'Body_Remodeling', code: 'BODY_REMODELING' },
            { id: 16, name: 'Implant', code: 'IMPLANT' },
            { id: 17, name: 'Podiatry', code: 'PODIATRY' },
            { id: 18, name: 'Gynecology', code: 'GYNECOLOGY' },
            { id: 19, name: 'Plastic_Surgery', code: 'PLASTIC_SURGERY' },
            { id: 20, name: 'Permanent_Hair_Removal', code: 'PERMANENT_HAIR_REMOVAL' },
            { id: 21, name: 'ORL', code: 'ORL' },
            { id: 22, name: 'Mentoring', code: 'MENTORING' },
            { id: 23, name: 'Coaching', code: 'COACHING' },
            { id: 24, name: 'Consultancy', code: 'Consultancy' },
            { id: 25, name: 'Other', code: 'OTHER' },
        ];
    },
}));
