import { createResource } from '../createResource';

interface ReportContentParams {
    reason: string;
    businessId: string;
}

interface ReportContentResponse {
    id: number;
    reason: string;
    businessId: string;
}

export const reportContentEndpoint = createResource('reportContent', ({ api }) => ({
    async reportContent(params: ReportContentParams): Promise<ReportContentResponse> {
        const { data } = await api.post('', params);
        return data;
    },
}));
