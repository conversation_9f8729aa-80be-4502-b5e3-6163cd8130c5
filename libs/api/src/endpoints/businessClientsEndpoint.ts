import { AxiosRequestConfig } from 'axios';
import { PageableResponse } from '@bookr-technologies/api/types/PageableResponse';
import { createResource } from '../createResource';
import { NoteModel } from '../models/NoteModel';
import { UserModel } from '../models/UserModel';

interface ImportBulkResponse {
    error: boolean;
    clients: {
        client: Pick<UserModel, 'uid' | 'displayName' | 'phoneNumber' | 'email' | 'photoURL'>;
        error: string | null;
    }[];
}

interface FetchClientsParams {
    businessId?: string;
    size?: number;
    page?: number;
    textSearch?: string;
    withPushNotificationsEnabled?: boolean;
}

interface ExtraMethods {
    getClients(params: FetchClientsParams): Promise<PageableResponse<UserModel>>;
    importBulk(requestData: Pick<UserModel, 'displayName' | 'phoneNumber'>[]): Promise<ImportBulkResponse>;
    getDocuments(clientId: string): Promise<string[]>;
    getClientNotes(clientId: string): Promise<NoteModel[]>;
    createClientNote(clientId: string, note: NoteModel): Promise<NoteModel>;
    updateClientNote(clientId: string, note: NoteModel): Promise<NoteModel>;
    deleteClientNote(clientId: string, noteId: number): Promise<void>;
    downloadDocument(clientId: string, fileName: string): Promise<string>;
    uploadDocument(clientId: string, document: File | FormData, config?: AxiosRequestConfig): Promise<string>;
    deleteDocument(clientId: string, fileName: string): Promise<string>;
}

export const businessClientsEndpoint = createResource<UserModel, ExtraMethods>('business/clients', ({ api }) => ({
    async getClients(params): Promise<PageableResponse<UserModel>> {
        const { data } = await api.get('', { params });
        return data;
    },

    async importBulk(requestData: Pick<UserModel, 'displayName' | 'phoneNumber'>[]): Promise<ImportBulkResponse> {
        const filteredData = requestData.filter(({ displayName, phoneNumber }) => displayName && phoneNumber);
        const { data } = await api.post('', filteredData);
        return data;
    },

    async getDocuments(clientId: string): Promise<string[]> {
        const { data } = await api.get(`/${clientId}/documents`);
        return data;
    },

    async downloadDocument(clientId: string, fileName: string): Promise<string> {
        const { data } = await api.get(`/${clientId}/documents/${fileName}`, { responseType: 'blob' });
        return data;
    },

    async uploadDocument(clientId: string, document: File | FormData, config = {}): Promise<string> {
        let formData: FormData;
        if (document instanceof File) {
            formData = new FormData();
            formData.append('file', document);
        } else {
            formData = document;
        }
        const { data } = await api.post(`/${clientId}/documents`, formData, config);

        return data;
    },

    async deleteDocument(clientId: string, fileName: string): Promise<string> {
        const { data } = await api.delete(`/${clientId}/documents/${fileName}`);
        return data;
    },

    async getClientNotes(clientId: string): Promise<NoteModel[]> {
        const { data } = await api.get(`/${clientId}/notes`);
        return data;
    },

    async createClientNote(clientId: string, note: NoteModel): Promise<NoteModel> {
        const { data } = await api.post(`/${clientId}/notes`, note);
        return data;
    },

    async updateClientNote(clientId: string, note: NoteModel): Promise<NoteModel> {
        const { data } = await api.put(`/${clientId}/notes/${note.id}`, note);
        return data;
    },

    async deleteClientNote(clientId: string, noteId: number): Promise<void> {
        await api.delete(`/${clientId}/notes/${noteId}`);
    },
}));
