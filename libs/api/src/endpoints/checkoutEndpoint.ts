import { PeriodType } from '../constants/PeriodType';
import { createResource } from '../createResource';
import { SubscriptionPlanModel } from '../models/SubscriptionPlanModel';

interface GetSubscriptionPlansParams {
    withCustom?: boolean;
    currency?: 'ron' | 'eur' | 'usd';
}

interface VirtualTourEndpointMethods {
    getSubscriptionPlans(params?: GetSubscriptionPlansParams): Promise<Record<string, SubscriptionPlanModel[]>>;
}

export const checkoutEndpoint = createResource<SubscriptionPlanModel, VirtualTourEndpointMethods>(
    'stripe',
    ({ api }) => ({
        async getSubscriptionPlans(params: GetSubscriptionPlansParams = {}) {
            const { data } = await api.get<Record<string, SubscriptionPlanModel[]>>(`subscriptionPlans`, { params });

            if (params.withCustom) {
                data.CUSTOM = [
                    new SubscriptionPlanModel(PeriodType.Monthly, 'custom', 'custom'),
                    new SubscriptionPlanModel(PeriodType.Yearly, 'custom', 'custom'),
                ];
            }

            return data;
        },
    }),
);
