import { AxiosRequestConfig } from 'axios';
import { createResource } from '../createResource';
import { AppointmentModel } from '../models/AppointmentModel';
import { BreakModel } from '../models/BreakModel';
import { BusinessModel } from '../models/BusinessModel';
import { UserMetadataModel } from '../models/UserMetadataModel';
import { UserModel } from '../models/UserModel';
import { UserSettingsModel } from '../models/UserSettingsModel';
import { WorkingHourModel } from '../models/WorkingHourModel';
import { PageableResponse } from '../types/PageableResponse';
import { UploadFile } from '../types/UploadFile';
import { UpsertUserDto } from '../types/UpsertUserDto';

export interface BreaksRequest {
    startDate?: string;
    endDate?: string;
    page: number;
    size: number;
    staffIds?: string[];
}

export interface UserExtraMethods {
    upsert(uid?: string, data?: UpsertUserDto): Promise<UserModel>;
    attachProfilePicture(file: File | UploadFile, config?: AxiosRequestConfig): Promise<UserModel>;
    updateWorkingHours(hours: Partial<WorkingHourModel>[]): Promise<UserModel>;
    deleteAccount(): Promise<boolean>;
    getMetadata(email: string): Promise<UserMetadataModel>;
    addFavourite(businessId: string): Promise<BusinessModel[]>;
    removeFavourite(businessId: string): Promise<BusinessModel[]>;
    getFavourites(): Promise<BusinessModel[]>;
    updateSettings(
        uid: string,
        data: Partial<UserSettingsModel>,
        config?: AxiosRequestConfig,
    ): Promise<UserSettingsModel>;
    getBreaks(uid: string, params: BreaksRequest): Promise<PageableResponse<BreakModel>>;
    getInterests(): Promise<string[]>;
    countMyAppointmentsAsStaff(): Promise<number>;
    getNextAppointment(): Promise<AppointmentModel | null>;
}

export const usersEndpoint = createResource<UserModel, UserExtraMethods>('users', ({ api, update, create }) => ({
    upsert: (uid, data) => {
        if (uid) {
            return update(uid, data);
        } else {
            return create({ ...data, uid });
        }
    },
    async attachProfilePicture(file, config): Promise<UserModel> {
        const formData = new FormData();

        // Add support for both react and react-native uploads.
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formData.append('file', file as any);

        const { data } = await api.post('/photoURL', formData, config);
        return data;
    },
    async updateWorkingHours(hours: Partial<WorkingHourModel>[]): Promise<UserModel> {
        const { data } = await api.post('/workingHours', hours);
        return data;
    },

    async deleteAccount(): Promise<boolean> {
        const { data } = await api.delete('');
        return data;
    },

    async getMetadata(email: string): Promise<UserMetadataModel> {
        const { data } = await api.get(`/metadata`, { params: { email } });
        return data;
    },

    async addFavourite(businessId: string): Promise<BusinessModel[]> {
        const { data } = await api.post(`/favourites`, { businessId });
        return data;
    },

    async removeFavourite(businessId: string): Promise<BusinessModel[]> {
        const { data } = await api.delete(`/favourites/${businessId}`);
        return data;
    },

    async getFavourites(): Promise<BusinessModel[]> {
        const { data } = await api.get(`/favourites`);
        return data;
    },

    async updateSettings(uid, partialData, config): Promise<UserSettingsModel> {
        const { data } = await api.put<UserSettingsModel>(`/${uid}/settings`, partialData, config);

        return data;
    },

    async getBreaks(uid, params): Promise<PageableResponse<BreakModel>> {
        const { data } = await api.get(`/${uid}/breaks`, { params });

        return data;
    },

    async getInterests(): Promise<string[]> {
        const { data } = await api.get(`/interests`);

        return data;
    },

    async countMyAppointmentsAsStaff(): Promise<number> {
        const { data } = await api.get(`/appointments/count`);

        return data;
    },

    async getNextAppointment(): Promise<AppointmentModel | null> {
        const { data } = await api.get('/next-appointment');

        return data;
    },
}));
