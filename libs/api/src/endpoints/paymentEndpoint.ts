import { createResource } from '../createResource';
import { BusinessModel } from '../models/BusinessModel';

interface CreateCheckoutSessionInput {
    priceId: string;
    successUrl?: 'auto' | string;
    cancelUrl?: 'auto' | string;
    origin?: string;
    pathname?: string;
    currency?: 'ron' | 'eur' | 'usd';
}

interface CreateCheckoutSessionCommissionInput {
    successUrl?: string;
    cancelUrl?: string;
}

interface CheckoutSessionResponse {
    sessionId?: string;
    sessionUrl?: string;
}

export interface BuySMSInput {
    amount: string;
    successUrl?: string;
    cancelUrl?: string;
}

interface CreateConnectAccountInput {
    returnUrl: string;
}

interface CreateCheckoutSessionAppointmentInput {
    appointmentId: string;
    successUrl?: string;
    cancelUrl?: string;
}

interface PaymentExtraMethods {
    createCheckoutSession(input: CreateCheckoutSessionInput): Promise<CheckoutSessionResponse>;

    createCheckoutSessionCommission(input?: CreateCheckoutSessionCommissionInput): Promise<CheckoutSessionResponse>;

    getSmsOptions(): Promise<[{ smsAmount: string; smsPrice: string }]>;

    paySMS(input: BuySMSInput): Promise<{ sessionId: string; sessionUrl: string }>;

    createConnectAccount(input: CreateConnectAccountInput): Promise<{ url: string }>;

    reauthorizeConnectAccount(accountId: string): Promise<{ url: string }>;

    createCheckoutSessionAppointment(input: CreateCheckoutSessionAppointmentInput): Promise<CheckoutSessionResponse>;
}

export const paymentEndpoint = createResource<BusinessModel, PaymentExtraMethods>('stripe', ({ api }) => ({
    async createCheckoutSession(input: CreateCheckoutSessionInput) {
        const window$ = typeof window !== 'undefined' ? window : undefined;
        const origin = window$?.location?.origin || input.origin || 'https://bookr.ro';

        if (input.successUrl === 'auto') {
            input.successUrl = new URL('/settings/application/subscription?action=success', origin).toString();
        }

        if (input.cancelUrl === 'auto') {
            const pathname = window$?.location?.pathname || input.pathname || '/';
            input.cancelUrl = new URL(`${pathname}?action=cancel`, origin).toString();
        }

        const { data } = await api.post('/checkoutSession', input);

        return data;
    },

    async createCheckoutSessionCommission(input?: CreateCheckoutSessionCommissionInput) {
        const body: CreateCheckoutSessionCommissionInput = {};

        if (input?.successUrl) {
            body.successUrl = input.successUrl;
        }

        if (input?.cancelUrl) {
            body.cancelUrl = input.cancelUrl;
        }

        const { data } = await api.post('/checkout/commission', body);

        return data;
    },

    async getSmsOptions() {
        const { data } = await api.get('/smsOptions');

        return data;
    },

    async paySMS(input: BuySMSInput) {
        const { data } = await api.post('/paySMS', input);

        return data;
    },

    async createConnectAccount(input: CreateConnectAccountInput) {
        const { data } = await api.post('/connectAccount', input);

        return data;
    },

    async reauthorizeConnectAccount(accountId: string) {
        const { data } = await api.get(`/connectAccount/${accountId}`);

        return data;
    },

    async createCheckoutSessionAppointment(input: CreateCheckoutSessionAppointmentInput) {
        const { data } = await api.post('/checkout/appointment', input);

        return data;
    },
}));
