import { createResource } from '../createResource';
import { CreateReviewDTO } from '../dto/CreateReviewDTO';
import { ReviewModel } from '../models/ReviewModel';
import { FetchParams } from '../types/FetchParams';
import { PageableResponse } from '../types/PageableResponse';

interface FetchReviewsParams extends FetchParams {
    businessId: string;
    filterRating?: number;
}

interface RatingsChart {
    rating: number;
    noOfRatings: number;
}
interface ReviewsExtraMethods {
    createReview(review: CreateReviewDTO): Promise<ReviewModel>;
    fetchData(params: FetchReviewsParams): Promise<PageableResponse<ReviewModel>>;
    fetchReviewsCount(businessId: string): Promise<RatingsChart[]>;
}

export const reviewsEndpoint = createResource<ReviewModel, ReviewsExtraMethods>('reviews', ({ create, api }) => ({
    createReview(review): Promise<ReviewModel> {
        return create(review);
    },
    async fetchData(params): Promise<PageableResponse<ReviewModel>> {
        const { data } = await api.get('', { params });
        return data;
    },
    async fetchReviewsCount(businessId: string): Promise<RatingsChart[]> {
        const { data } = await api.get('/chart', { params: { businessId } });
        return data.ratings;
    },
}));
