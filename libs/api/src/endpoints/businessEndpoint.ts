import { AxiosRequestConfig } from 'axios';
import { DateLike, formatDate } from '@bookr-technologies/core/datetime';
import { createResource } from '../createResource';
import { BillingInfoModel } from '../models/BillingInfoModel';
import { BreakModel } from '../models/BreakModel';
import { BusinessModel } from '../models/BusinessModel';
import { BusinessStaffInvitationModel } from '../models/BusinessStaffInvitationModel';
import { CategoryModel } from '../models/CategoryModel';
import { ClientDetailsModel } from '../models/ClientDetailsModel';
import { SearchBusinessModel } from '../models/SearchBusinessModel';
import { ServiceModel } from '../models/ServiceModel';
import { SmsConfiguration } from '../models/SmsConfiguration';
import { SubscriptionBillingModel } from '../models/SubscriptionBillingModel';
import { SubscriptionModel } from '../models/SubscriptionModel';
import { UserModel } from '../models/UserModel';
import { WorkingHourModel } from '../models/WorkingHourModel';
import { PageableResponse } from '../types/PageableResponse';
import { SearchResult } from '../types/SearchResult';
import { UploadFile } from '../types/UploadFile';
import { paramsSerializer } from '../utils/paramsSerializer';
import { BreaksRequest } from './usersEndpoint';

type TotalSales = Record<string, number>;

export interface TotalEarning {
    date: string;
    amount: number;
    currency: string;
}

export interface ServiceSalesActivity {
    service: ServiceModel;
    percentage: number;
}

export interface SalesStats {
    appointments: number;
    finished: number;
    cancelled: number;
    noShows: number;
    clients: number;
    bookingStats: BookingStats;
}

export interface BookingStats {
    totalWorkingMinutes: number;
    totalBookedMinutes: number;
    percentage: number;
}

export interface StaffStat extends SalesStats {
    totalSales: TotalSales;
    totalEarnings: TotalEarning[];
    serviceSalesActivity: ServiceSalesActivity[];
    staff: UserModel;
}

export interface BusinessStats extends SalesStats {
    totalSales: TotalSales;
    visits: number;
}

export interface SalesResponse {
    staffStats: StaffStat[];
    businessStats: BusinessStats;
}

export interface GetSubscriptionBillingsParams {
    limit?: number;
    starting_after?: string;
    ending_before?: string;
}

export interface GetSubscriptionBillingsResult {
    items: SubscriptionBillingModel[];
    nextPage: string | null;
    previousPage: string | null;
}

export interface SearchParams {
    text?: string;
    category?: string;
    latLng?: string;
    radius?: number;
    size?: number;
    page?: number;
    minPrice?: number;
    maxPrice?: number;
    sort?: string;
    instantBooking?: boolean;
    date?: string;
    fromTime?: string;
    toTime?: string;
}

export interface SearchBusinessesParams {
    latLng?: string;
    radius?: number;
}

export interface SearchRecentBusinessesParams extends SearchBusinessesParams {
    days?: number;
}

interface BusinessExtraMethods {
    attachProfilePicture(file: File | UploadFile, config?: AxiosRequestConfig): Promise<BusinessModel>;

    attachWorkplacePicture(file: File | UploadFile, config?: AxiosRequestConfig): Promise<BusinessModel>;

    removeWorkplacePicture(url: string): Promise<boolean>;

    removeStaffMember(uid: string): Promise<boolean>;

    inviteUser(user: Partial<UserModel>): Promise<boolean>;

    staffRank(staffMembers: Pick<UserModel, 'uid'>[]): Promise<boolean>;

    updateCategories(categories: Partial<CategoryModel>[]): Promise<BusinessModel>;

    updateWorkingHours(hours: Partial<WorkingHourModel>[]): Promise<BusinessModel>;

    removeProfilePicture(url: string): Promise<BusinessModel>;

    getBillingInfo(): Promise<BillingInfoModel>;

    updateBillingInfo(billingInfo: BillingInfoModel): Promise<boolean>;

    getSubscription(): Promise<SubscriptionModel>;

    cancelSubscription(subscriptionId: string): Promise<void>;

    getSubscriptionBillings(
        params?: GetSubscriptionBillingsParams,
        config?: AxiosRequestConfig,
    ): Promise<GetSubscriptionBillingsResult>;

    amIAnEmployee(): Promise<BusinessStaffInvitationModel>;

    joinBusiness(): Promise<boolean>;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    getSales(from?: DateLike, to?: DateLike): Promise<SalesResponse>;

    visitBusiness(businessId: string): Promise<void>;

    search(params?: SearchParams): Promise<SearchResult<SearchBusinessModel>>;

    getMyBookedBusinesses(): Promise<BusinessModel[]>;

    getBestBusinesses(params: SearchBusinessesParams): Promise<BusinessModel[]>;

    getNearbyBusinesses(params: SearchBusinessesParams): Promise<BusinessModel[]>;

    getRecentBusinesses(params: SearchRecentBusinessesParams): Promise<BusinessModel[]>;

    getBreaks(uid: string, params: BreaksRequest, config?: AxiosRequestConfig): Promise<PageableResponse<BreakModel>>;

    getSmsConfiguration(businessId: string): Promise<SmsConfiguration>;

    updateSmsConfiguration(businessId: string, smsConfiguration: SmsConfiguration): Promise<SmsConfiguration>;
}

interface BusinessClientExtraMethods {
    blockClient(uid: string, value: boolean): Promise<boolean>;
}

export const businessEndpoint = createResource<BusinessModel, BusinessExtraMethods>('business', ({ api, update }) => ({
    async attachProfilePicture(file, config): Promise<BusinessModel> {
        const formData = new FormData();

        // Add support for both react and react-native uploads.
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formData.append('file', file as any);

        const { data } = await api.post('/profilePicture', formData, config);

        return data;
    },
    async attachWorkplacePicture(file, config): Promise<BusinessModel> {
        const formData = new FormData();

        // Add support for both react and react-native uploads.
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        formData.append('file', file as any);

        const { data } = await api.post('/addWorkplacePhoto', formData, config);

        return data;
    },
    async removeWorkplacePicture(data: string): Promise<boolean> {
        const res = await api.delete('/removeWorkplacePhoto', { data });

        return res.data;
    },
    async removeStaffMember(staffId: string): Promise<boolean> {
        const { data } = await api.post('/removeStaffMember', {}, { params: { staffId } });

        return data;
    },
    async inviteUser({ email }: Partial<UserModel>): Promise<boolean> {
        const { data } = await api.post('/invite', { email });

        return data;
    },
    async staffRank(staffMembers: Pick<UserModel, 'uid'>[]): Promise<boolean> {
        const { data } = await api.put('/staffRank', staffMembers);

        return data;
    },
    async updateCategories(categories: Partial<CategoryModel>[]): Promise<BusinessModel> {
        const { data } = await api.put('/categories', categories);

        return data;
    },
    async updateWorkingHours(hours: Partial<WorkingHourModel>[]): Promise<BusinessModel> {
        const { data } = await api.post('/workingHours', hours);

        return data;
    },
    async removeProfilePicture(businessId: string): Promise<BusinessModel> {
        return update(businessId, { profilePicture: '' });
    },

    async getBillingInfo(): Promise<BillingInfoModel> {
        const { data } = await api.get('/billingInfo');

        return data;
    },

    async updateBillingInfo(billingInfo: BillingInfoModel): Promise<boolean> {
        const { data } = await api.post('/billingInfo', billingInfo);

        return data;
    },

    async getSubscription(): Promise<SubscriptionModel> {
        const { data } = await api.get('/subscription');

        return data;
    },

    async cancelSubscription(subscriptionId: string): Promise<void> {
        await api.delete('/subscription', {
            data: {
                subscriptionId,
            },
        });
    },

    async getSubscriptionBillings(
        params?: GetSubscriptionBillingsParams,
        config?: AxiosRequestConfig,
    ): Promise<GetSubscriptionBillingsResult> {
        const { data } = await api.get('/subscription/billings', {
            ...(config || {}),
            params: {
                ...(config?.params ?? {}),
                ...(params ?? {}),
            },
        });

        return data;
    },

    async amIAnEmployee(): Promise<BusinessStaffInvitationModel> {
        const { data } = await api.get(`/amIAnEmployee`);
        return data;
    },

    async joinBusiness(): Promise<boolean> {
        const { data } = await api.post('/join');
        return data;
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    async getSales(from?: DateLike, to?: DateLike): Promise<any> {
        const { data } = await api.get('/sales', {
            params: {
                fromDate: formatDate(from || '', 'YYYY-MM-DD'),
                toDate: formatDate(to || '', 'YYYY-MM-DD'),
            },
        });
        return data;
    },

    async visitBusiness(businessId: string): Promise<void> {
        return api.post(`${businessId}/visit`);
    },

    async search(params?: SearchParams): Promise<SearchResult<SearchBusinessModel>> {
        const { data } = await api.get('/search', { params });
        return data;
    },

    async getMyBookedBusinesses(): Promise<BusinessModel[]> {
        const { data } = await api.get('/booked');
        return data;
    },

    async getBestBusinesses(params: SearchBusinessesParams): Promise<BusinessModel[]> {
        const { data } = await api.get('/best', { params });
        return data;
    },

    async getRecentBusinesses(params: SearchRecentBusinessesParams): Promise<BusinessModel[]> {
        const { data } = await api.get('/recent', { params });
        return data;
    },

    async getNearbyBusinesses(params: SearchBusinessesParams): Promise<BusinessModel[]> {
        const { data } = await api.get('/nearby', { params });
        return data;
    },

    async getBreaks(uid, params, config): Promise<PageableResponse<BreakModel>> {
        const { data } = await api.get(`/${uid}/breaks`, {
            ...(config || {}),
            params: {
                ...(config?.params ?? {}),
                ...(params ?? {}),
            },
            paramsSerializer,
        });

        return data;
    },

    async getSmsConfiguration(businessId): Promise<SmsConfiguration> {
        const { data } = await api.get(`/${businessId}/sms-configuration`);

        return data;
    },

    async updateSmsConfiguration(businessId, smsConfiguration): Promise<SmsConfiguration> {
        const { data } = await api.put(`/${businessId}/sms-configuration`, smsConfiguration);

        return data;
    },
}));

export const businessClientEndpoint = createResource<ClientDetailsModel>('business/clients');

export const businessBlockEndpoint = createResource<ClientDetailsModel, BusinessClientExtraMethods>(
    'business/block',
    ({ api }) => ({
        async blockClient(uid: string, value: boolean): Promise<boolean> {
            const { data } = await api.put(`/${uid}?value=${value}`);
            return data;
        },
    }),
);
