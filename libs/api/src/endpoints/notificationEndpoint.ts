import { createResource } from '../createResource';

export const notificationsEndpoint = createResource<
    // eslint-disable-next-line
    any,
    {
        getTemplatesVariables: () => Promise<string[]>;
        previewTemplate: (template: string) => Promise<string>;
        sendTestMessage: (template: string) => Promise<void>;
    }
>('notifications', ({ api }) => ({
    async getTemplatesVariables() {
        const { data } = await api.get<string[]>('/templates/variables');
        return data;
    },
    async previewTemplate(template: string) {
        const { data } = await api.post<string>('/preview', { template });
        return data;
    },
    sendTestMessage(template: string) {
        return api.post('/preview/sendTestMessage', { template });
    },
}));
