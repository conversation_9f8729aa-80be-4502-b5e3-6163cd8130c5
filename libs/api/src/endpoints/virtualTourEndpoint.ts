import { createResource } from '../createResource';

interface VirtualTourEndpointMethods {
    requestVirtualTour(businessId: string): Promise<boolean>;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const virtualTourEndpoint = createResource<any, VirtualTourEndpointMethods>('virtualTour', ({ api }) => ({
    async requestVirtualTour(businessId: string): Promise<boolean> {
        const { data } = await api.post(`?businessId=${businessId}`, {}, {});
        return data;
    },
}));
