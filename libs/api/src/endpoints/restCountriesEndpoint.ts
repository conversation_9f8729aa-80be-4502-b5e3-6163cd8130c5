import { createResource } from '../createResource';
import { RestCountryModel } from '../models/RestCountryModel';

const cache: Record<string, RestCountryModel[]> = {};

export const restCountriesEndpoint = createResource('https://restcountries.com/v3.1', (resource) => ({
    getAllCountries: async () => {
        const params: Record<string, string> = {
            fields: 'name,currencies,flag,flags,idd,cca2',
        };

        const key = 'all';

        if (!cache[key]) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            const { data } = await resource.api.get<Record<string, any>[]>('/all', { params });
            cache[key] = data
                .map((country, id) => {
                    const suffix = (country.idd?.suffixes ?? [])[0];
                    return {
                        id,
                        name: country.name.common,
                        currencies: country.currencies,
                        flag: country.flags.svg,
                        unicodeFlag: country.flags.svg,
                        idd: country.idd,
                        callingCode: `${country.idd?.root ?? ''}${suffix ?? ''}`.trim(),
                        cca2: country.cca2,
                    } as RestCountryModel;
                })
                .sort((a, b) => a.name.localeCompare(b.name));
        }

        return cache[key];
    },
}));
