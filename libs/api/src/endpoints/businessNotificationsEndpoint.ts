import { createResource } from '../createResource';

interface SendNotificationToClients {
    message: string;
    allSelected: boolean;
    selectedUserIds: string[];
    excludedUsersIds: string[];
}

interface ExtraMethods {
    sendNotificationToClients(payload: SendNotificationToClients): Promise<void>;
    countSentNotificationsForToday(): Promise<number>;
}

export const businessNotificationsEndpoint = createResource<unknown, ExtraMethods>(
    'business/notifications',
    ({ api }) => ({
        async sendNotificationToClients(payload): Promise<void> {
            await api.post('/send/clients', payload);
        },

        async countSentNotificationsForToday(): Promise<number> {
            const { data } = await api.get('/send/clients/count-sent');
            return data;
        },
    }),
);
