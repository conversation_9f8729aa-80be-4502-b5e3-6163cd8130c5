import { createResource } from '../createResource';
import { CreateFeedbackDTO } from '../dto/CreateFeedbackDTO';
import { FeedbackModel } from '../models/FeedbackModel';

interface FeedbackExtraMethods {
    createFeedback(feedback: CreateFeedbackDTO): Promise<void>;
}

export const feedbackEndpoint = createResource<FeedbackModel, FeedbackExtraMethods>('feedback', ({ create }) => ({
    async createFeedback(feedback) {
        await create(feedback);
    },
}));
