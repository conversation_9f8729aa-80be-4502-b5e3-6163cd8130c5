import { createResource } from '../createResource';
import { WaitingListDTO } from '../dto/WaitingListDTO';

export const waitingListEndpoint = createResource('waitingList', ({ api }) => ({
    async subscribeToWaitingList(staffId: string, date: string): Promise<boolean> {
        const { data } = await api.post('', null, {
            params: {
                staffId,
                date,
            },
        });
        return data;
    },

    async getWaitingList(staffId: string, date: string): Promise<WaitingListDTO> {
        const { data } = await api.get('', {
            params: {
                staffId,
                date,
            },
        });
        return data;
    },
}));
