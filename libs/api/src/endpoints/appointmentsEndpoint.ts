import { createResource } from '../createResource';
import { IsTimeslotFreeDTO } from '../dto/IsTimeslotFreeDTO';
import { TimeslotDTO } from '../dto/TimeslotDTO';
import { NoteModel } from '../models/NoteModel';

export const appointmentsEndpoint = createResource('appointments', (resource) => ({
    async cancelAppointment(appointmentId: number, everyRecurrence = false): Promise<boolean> {
        const { data } = await resource.api.put(`cancel/${appointmentId}`, null, {
            params: {
                everyRecurrence,
            },
        });
        return data;
    },

    async createNote(appointmentId?: number, note?: Pick<NoteModel, 'text'>): Promise<NoteModel> {
        const { data } = await resource.api.post(`${appointmentId}/notes`, note);
        return data;
    },

    async deleteNote(appointmentId?: number, noteId?: number) {
        return await resource.destroy(`${appointmentId}/notes/${noteId}`);
    },

    async updateNote(appointmentId?: number, note?: Partial<NoteModel>) {
        return await resource.api.put(`${appointmentId}/notes/${note?.id}`, note);
    },

    async noShow(appointmentId: number): Promise<boolean> {
        const { data } = await resource.api.put(`noShow/${appointmentId}`);
        return data;
    },

    async cancel(appointmentId: number, everyRecurrence = false): Promise<boolean> {
        const { data } = await resource.api.put(`cancel/${appointmentId}`, null, {
            params: {
                everyRecurrence,
            },
        });
        return data;
    },

    async clientArrivedAtLocation(appointmentId: number, clientArrivedAtLocation: boolean) {
        return await resource.api.put(`clientArrivedAtLocation/${appointmentId}`, { clientArrivedAtLocation });
    },

    async imReady(appointmentId: number): Promise<boolean> {
        const { data } = await resource.api.post(`imReady/${appointmentId}`);
        return data;
    },

    async isTimeslotFree(
        staffId: string | number,
        timestamp: number,
        serviceId: string | number,
    ): Promise<IsTimeslotFreeDTO> {
        const { data } = await resource.api.get(`/isTimeslotFree`, {
            params: {
                staffId,
                timestamp,
                serviceId,
            },
        });

        return data;
    },

    async isRecurrentTimeslotFree(
        serviceId: string | number,
        startTimestamp: number,
        endTimestamp: number,
        recurrence: string,
    ): Promise<IsTimeslotFreeDTO[]> {
        const { data } = await resource.api.get(`/isRecurrentTimeslotFree`, {
            params: {
                serviceId,
                startTimestamp,
                endTimestamp,
                recurrence,
            },
        });

        return data;
    },

    async getDatesOfAppointments(staffId: string): Promise<string[]> {
        const { data } = await resource.api.get(`/datesThatHaveAppointments`, {
            params: {
                staffId,
            },
        });

        return data;
    },

    async generateTimeslots(date: string, staffId: string, serviceId: string): Promise<TimeslotDTO[]> {
        const { data } = await resource.api.get(`/generateTimeslots`, {
            params: {
                date,
                staffId,
                serviceId,
            },
        });
        return data;
    },

    async daysThatCanBeBooked(staffId: string, serviceId: string): Promise<Record<string, boolean>> {
        const { data } = await resource.api.get(`/daysThatCanBeBooked`, {
            params: {
                staffId,
                serviceId,
            },
        });
        return data;
    },
}));
