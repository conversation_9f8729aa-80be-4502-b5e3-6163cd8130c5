import { createResource } from '../createResource';
import { ServiceModel } from '../models/ServiceModel';

interface ServicesEndpointMethods {
    upsert(id?: number, data?: ServiceModel): Promise<ServiceModel>;
    updateRank(services: Pick<ServiceModel, 'id'>[]): Promise<boolean>;
}

export const servicesEndpoint = createResource<ServiceModel, ServicesEndpointMethods>(
    'services',
    ({ api, update, create }) => ({
        async upsert(id, data) {
            if (id) {
                return update(id, data);
            } else {
                return create({ ...data });
            }
        },
        async updateRank(services) {
            const { data } = await api.put('rank', services);
            return data;
        },
    }),
);
