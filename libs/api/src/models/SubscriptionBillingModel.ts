import { Nullable } from '@bookr-technologies/core/types';
import { formatNumber } from '@bookr-technologies/core/utils/formatters';

export class SubscriptionBillingModel {
    public currency!: string;
    public endsAt!: string;
    public hostedInvoiceUrl!: Nullable<string>;
    public invoiceUrl!: Nullable<string>;
    public startsAt!: string;
    public subscription!: string;
    public subscriptionPlan!: string;
    public total!: number;

    public static getPrice(billingModel: SubscriptionBillingModel) {
        return `${formatNumber(billingModel.total)} ${billingModel.currency}`;
    }

    constructor(public id: string) {}
}
