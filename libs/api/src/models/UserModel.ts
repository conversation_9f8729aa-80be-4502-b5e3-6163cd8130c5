import { AccountType } from '../constants/AccountType';
import { PlatformType } from '../constants/PlatformType';
import { UserPrivilegeType } from '../constants/UserPrivilegeType';
import { AppointmentModel } from './AppointmentModel';
import { BreakModel } from './BreakModel';
import { BusinessModel } from './BusinessModel';
import { ServiceModel } from './ServiceModel';
import { UserSettingsModel } from './UserSettingsModel';
import { WorkingHourModel } from './WorkingHourModel';

export class UserModel {
    public uid!: string;
    public displayName!: string;
    public phoneNumber!: string;
    public email!: string;
    public photoURL!: string;
    public language!: string;
    public staffRank!: number;
    public maxFutureDaysAppointment!: number;
    public position!: string | null;
    public platform!: PlatformType;
    public accountType!: AccountType;
    public business!: BusinessModel;
    public appointmentsAsStaff!: AppointmentModel[];
    public appointmentsAsClient!: AppointmentModel[];
    public breaks!: BreakModel[];
    public services!: ServiceModel[];
    public workingHours!: WorkingHourModel[];
    public privileges!: UserPrivilegeType[];
    public pushNotificationToken!: string;
    public userSettings!: UserSettingsModel;
    public stripeConnectedAccountId!: string;
    public dateOfBirth!: string;
    public country!: string;
    public city!: string;
    public gender!: string;
    public interests!: string[];

    public static sort(left: UserModel, right: UserModel) {
        if (left.staffRank >= 0 && right.staffRank >= 0) {
            return left.staffRank - right.staffRank;
        }

        return 1;
    }

    public static hasPrivileges(user: UserModel, ...privileges: UserPrivilegeType[]) {
        const list = privileges.map((privileges) => String(privileges).toLowerCase());
        return !!user.privileges.find((item) => list.includes(String(item).toLowerCase()));
    }
}
