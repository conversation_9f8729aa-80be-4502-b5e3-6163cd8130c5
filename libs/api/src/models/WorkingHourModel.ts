import moment from 'moment';
import { getTimezoneOffset } from '@bookr-technologies/core/datetime';
import { WorkingDay } from '../constants/WorkingDay';

export class WorkingHourModel {
    public id!: string;
    public day!: WorkingDay;
    public start!: string | Date;
    public end!: string | Date;
    public lastUpdatedAt!: string;

    public static convertHoursToLocalTimezone(hours: WorkingHourModel[]): WorkingHourModel[] {
        return hours.map((hour) => {
            const toLocalTimezone = { ...hour };
            const offset = moment(hour.lastUpdatedAt || new Date()).utcOffset();

            toLocalTimezone.start = moment(hour.start, 'HH:mm').add(offset, 'minutes').format('HH:mm');
            toLocalTimezone.end = moment(hour.end, 'HH:mm').add(offset, 'minutes').format('HH:mm');

            return toLocalTimezone;
        });
    }

    public static convertHoursToUTC(hours: WorkingHourModel[] | Omit<WorkingHourModel, 'id' | 'lastUpdatedAt'>[]) {
        return hours.map((hour) => {
            const toUtc = { ...hour };
            const timezoneOffset = getTimezoneOffset();

            toUtc.start = moment(hour.start, 'HH:mm').add(timezoneOffset, 'minutes').format('HH:mm');
            toUtc.end = moment(hour.end, 'HH:mm').add(timezoneOffset, 'minutes').format('HH:mm');

            return toUtc;
        });
    }
}
