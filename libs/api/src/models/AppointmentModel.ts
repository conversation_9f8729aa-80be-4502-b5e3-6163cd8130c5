import { AppointmentPaymentDetailsModel } from './AppointmentPaymentDetailsModel';
import { NoteModel } from './NoteModel';
import { ServiceModel } from './ServiceModel';
import { ServiceOnlineEventModel } from './ServiceOnlineEventModel';
import { UserModel } from './UserModel';

export class AppointmentModel {
    public id!: number;
    public cancelled!: boolean;
    public noShow!: boolean;
    public dateTime!: string;
    public createdAt!: string;
    public client!: UserModel;
    public staff!: UserModel;
    public service!: ServiceModel;
    public onlineEvent!: ServiceOnlineEventModel;
    public notes!: NoteModel[];
    public clientArrivedAtLocation!: boolean;
    // public activities!: ActivityModel[];
    public appointmentPaymentDetails!: AppointmentPaymentDetailsModel;
    public recurrent!: boolean;
    public paidOnline!: boolean;
}
