import { Nullable } from '@bookr-technologies/core/types';
import { IntegrationTypeEnum } from '../integrations';

export class ServiceModel {
    public id!: number;
    public name!: string;
    public duration!: number;
    public description!: string;
    public color!: string;
    public price!: number;
    public currency!: string;
    public serviceRank!: number;
    public breakBetweenServices!: number;
    public numberOfSessions!: number;
    public daysInAdvanceSessionsCanBeBooked!: number;
    public inactive!: boolean;
    public hiddenFromClients!: boolean;
    public onlineEvent!: Nullable<boolean>;
    public onlineEventIntegrationType!: Nullable<IntegrationTypeEnum>;
    public acceptsOnlinePayments!: boolean;

    public static sort(left: ServiceModel, right: ServiceModel) {
        if (left.serviceRank >= 0 && right.serviceRank >= 0) {
            return left.serviceRank - right.serviceRank;
        }

        return 1;
    }
}
