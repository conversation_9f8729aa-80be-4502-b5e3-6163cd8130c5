import { t, TFunction } from 'i18next';
import { Optional } from '@bookr-technologies/core/types';
import { PeriodType } from '../constants/PeriodType';

const enum SubscriptionPlan {
    Free = 'FREE',
    Standard = 'STANDARD',
    Professional = 'PROFESSIONAL',
    Custom = 'CUSTOM',
}

export class SubscriptionPlanModel {
    constructor(public period: PeriodType, public priceId: string, public price: string) {}

    public static getPlanName(planName: Optional<string>, tFunc?: TFunction) {
        if (!planName) {
            return null;
        }

        return (tFunc || t)(`${planName.toLowerCase()}.name`, { ns: 'subscriptionPlans' });
    }

    public static getPlanDescription(planName: Optional<string>, tFunc?: TFunction) {
        if (!planName) {
            return null;
        }

        return (tFunc || t)(`${planName.toLowerCase()}.description`, { ns: 'subscriptionPlans' });
    }

    public static getPlanFeatures(planName: Optional<string>, tFunc?: TFunction) {
        if (!planName) {
            return [];
        }

        return (tFunc || t)(`${planName.toLowerCase()}.features`, { ns: 'subscriptionPlans' })
            .split('\n')
            .map((item) => item.trim())
            .filter(Boolean);
    }

    public static isRecommended(planName: Optional<string>) {
        return String(planName).toUpperCase() === 'PROFESSIONAL';
    }

    public static isCustom(plan: Optional<SubscriptionPlanModel | string>) {
        if (typeof plan === 'string') {
            return String(plan).toLowerCase() === 'custom';
        }

        return String(plan?.price).toLowerCase() === 'custom' && String(plan?.priceId).toLowerCase() === 'custom';
    }

    public static selectByPeriod(plans: SubscriptionPlanModel[], period: PeriodType): Optional<SubscriptionPlanModel> {
        return plans?.find((plan) => String(plan.period).toLowerCase() === String(period).toLowerCase());
    }

    public static getPlanPrice(plan: SubscriptionPlanModel, period: PeriodType, tFunc?: TFunction) {
        if (SubscriptionPlanModel.isCustom(plan)) {
            return (tFunc || t)('customPrice', { ns: 'pricingPlans' });
        }

        return (tFunc || t)(period === PeriodType.Monthly ? 'pricePerMonth' : 'pricePerYear', {
            ns: 'planSelect',
            price: plan.price,
        });
    }

    public static getSortedPlans(plansMap: Record<string, SubscriptionPlanModel[]>): {
        planName: string;
        prices: SubscriptionPlanModel[];
        previousPlanName: string;
    }[] {
        const getPlan = (
            planName: SubscriptionPlan,
            prices: SubscriptionPlanModel[],
            previousPlanName?: SubscriptionPlan,
        ) => (prices?.length > 0 ? { planName, prices, previousPlanName } : null);

        return [
            getPlan(SubscriptionPlan.Free, plansMap.FREE),
            getPlan(SubscriptionPlan.Standard, plansMap.STANDARD, SubscriptionPlan.Free),
            getPlan(SubscriptionPlan.Professional, plansMap.PROFESSIONAL, SubscriptionPlan.Standard),
            getPlan(SubscriptionPlan.Custom, plansMap.CUSTOM, SubscriptionPlan.Professional),
        ].filter(Boolean) as never;
    }

    public static getSortedPlansByPeriod(
        plansMap: Record<string, SubscriptionPlanModel[]>,
        period: PeriodType,
    ): {
        planName: string;
        price: Optional<SubscriptionPlanModel>;
        previousPlanName: string;
    }[] {
        return SubscriptionPlanModel.getSortedPlans(plansMap).map(({ planName, prices, previousPlanName }) => ({
            price: SubscriptionPlanModel.selectByPeriod(prices, period),
            planName,
            previousPlanName,
        }));
    }
}
