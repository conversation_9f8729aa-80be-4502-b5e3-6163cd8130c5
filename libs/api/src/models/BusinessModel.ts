import { CategoryModel } from './CategoryModel';
import { SubscriptionModel } from './SubscriptionModel';
import { UserModel } from './UserModel';
import { WorkingHourModel } from './WorkingHourModel';

export class BusinessModel {
    public id!: string;
    public name!: string;
    public staffMembers!: UserModel[];
    public blocked!: string[];
    public categories!: CategoryModel[];
    public createdAt!: string;
    public description!: string;
    public facebookURL!: string;
    public formattedAddress!: string;
    public hidden!: boolean;
    public instagramURL!: string;
    public latitude!: number;
    public latitudeDelta!: number;
    public longitude!: number;
    public longitudeDelta!: number;
    public phoneNumber!: string;
    public photos!: string[];
    public profilePicture!: string;
    public slug!: string;
    public smsRemindersEnabled!: boolean;
    public startingDate!: string;
    public subscription!: SubscriptionModel;
    public totalSMS!: number;
    public virtualTourURL!: string;
    public websiteURL!: string | null;
    public workingHours!: WorkingHourModel[];
    public zoneId!: string;
    public templateAppointmentReminder!: string;
    public templateAppointmentCreated!: string;
    public templateAppointmentCancelled!: string;
    public templateAppointmentRescheduled!: string;
    public reviewInfo!: { averageRating: number; noOfReviews: number };
    public commissionPaymentPastDue!: boolean;
    public multipleStripeConnectedAccountsEnabled!: boolean;
}
