export enum SubscriptionStatus {
    Trial = 'TRIAL',
    Active = 'ACTIVE',
    PastDue = 'PAST_DUE',
    Canceled = 'CANCELED',
}

export class SubscriptionModel {
    public id!: number;
    public stripeSubscriptionId!: string;
    public status!: SubscriptionStatus;
    public trialEnd!: string;
    public cancelAtPeriodEnd!: boolean;
    public latestInvoiceId!: string;
    public latestInvoiceUrl!: string;
    public total!: string;
    public priceId!: null;
    public shouldSelectSubscriptionPlan!: true;
    public subscriptionPlan!: string;
    public nextPaymentDate!: string;
    public commission!: boolean;
    public commissionPastDue!: boolean;

    static isTrial(subscription?: SubscriptionModel | null): boolean {
        return subscription?.status === SubscriptionStatus.Trial;
    }
}
