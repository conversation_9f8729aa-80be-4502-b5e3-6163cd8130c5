import axios, { AxiosRequestConfig } from 'axios';
import { FirebaseService } from '@bookr-technologies/core/services';
import { env } from '@bookr-technologies/env';

export function createEndpoint(url: string, config: AxiosRequestConfig = {}) {
    const isExternal = url.startsWith('http');
    const api = axios.create({
        ...config,
        baseURL: isExternal ? url : `${env('apiUrl')}/${url}`,
    });

    api.interceptors.request.use(async (request) => {
        request.headers ??= {};
        if (!request.headers.Authorization && !isExternal) {
            try {
                const token = (await FirebaseService.auth().currentUser?.getIdToken()) ?? null;
                if (token) {
                    request.headers.Authorization = `Bearer ${token}`;
                }
            } catch (e) {
                console.error(e);
                // Nothing to do
            }
        }

        return request;
    });

    api.interceptors.response.use((response) => {
        if (Object.prototype.hasOwnProperty.call(response.data, 'data')) {
            response.data = response.data.data;
        }

        return response;
    });

    return api;
}
