import { AppointmentModel } from '../models/AppointmentModel';
import { PaymentMethod } from '../models/PaymentMethod';
import { UserModel } from '../models/UserModel';

export interface BookNowRequest {
    timestamp: number;
    staffId: string;
    serviceId: string;
    client: UserModel;
    inviteClient: boolean;
    paymentMethod: PaymentMethod;
    recurrenceStrategy: string;
    timestampEnd: number;
}

export interface BookNowResponse {
    appointment: AppointmentModel;
    success: boolean;
    error: string;
    webRedirectUrl: string;
}
