import Axios from 'axios';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function getErrorMessage(error: any, defaultMessage = 'Something went wrong.'): string {
    if (Axios.isAxiosError(error)) {
        const { message, error: errorMessage } = (error.response?.data ?? {}) as { message?: string; error?: string };

        return message || errorMessage || defaultMessage;
    }

    if (error?.message) {
        return error.message;
    }

    return defaultMessage;
}
