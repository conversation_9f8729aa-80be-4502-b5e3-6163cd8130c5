/* eslint-disable @typescript-eslint/no-explicit-any */
import { EventHandler, RefObject, useEffect, useRef } from 'react';
import { useEvent } from './use-event';

interface Options {
    disableFireOnMount?: boolean;
}

export function useEventListener<
    T extends EventTarget,
    A extends Parameters<T['addEventListener']> = Parameters<T['addEventListener']>,
>(target: RefObject<T>, event: A[0], listener: A[1], options: Options = {}) {
    const handler = useEvent((event: Event) => {
        if (typeof listener === 'function') {
            return listener(event);
        }

        return listener?.handleEvent(event);
    });

    useEffect(() => {
        target.current?.addEventListener(event, handler);

        return () => {
            target.current?.removeEventListener(event, handler);
        };
    }, [target, event, handler]);

    useEffect(() => {
        if (!options.disableFireOnMount && target.current) {
            target.current.dispatchEvent(new Event(event));
        }
    }, []);
}

export function useWindowListener<K extends keyof WindowEventMap>(
    event: K,
    listener: (event: WindowEventMap[K]) => any,
    options?: Options,
) {
    const ref = useRef<Window>(window);

    useEventListener(ref, event, listener as any, options);
}
