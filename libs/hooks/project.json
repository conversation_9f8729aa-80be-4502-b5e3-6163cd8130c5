{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/hooks/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nrwl/web:rollup", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/hooks", "tsConfig": "libs/hooks/tsconfig.lib.json", "project": "libs/hooks/package.json", "entryFile": "libs/hooks/src", "external": ["react/jsx-runtime"], "rollupConfig": "tools/rollup/rollup.lib.js", "format": ["esm", "cjs"], "compiler": "swc", "assets": [{"glob": "libs/hooks/README.md", "input": ".", "output": "."}]}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/hooks/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/hooks"], "options": {"jestConfig": "libs/hooks/jest.config.ts", "passWithNoTests": true}}}}