{"extends": "../tsconfig.json", "compilerOptions": {"emitDecoratorMetadata": true, "outDir": ""}, "files": ["../../../node_modules/@nrwl/react/typings/styled-jsx.d.ts", "../../../node_modules/@nrwl/react/typings/cssmodule.d.ts", "../../../node_modules/@nrwl/react/typings/image.d.ts"], "exclude": ["../**/*.spec.ts", "../**/*.spec.js", "../**/*.spec.tsx", "../**/*.spec.jsx", "jest.config.ts"], "include": ["../src/**/*", "*.js"]}