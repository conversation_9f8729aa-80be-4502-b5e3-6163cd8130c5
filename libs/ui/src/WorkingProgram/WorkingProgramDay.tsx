import AddCircleIcon from '@mui/icons-material/AddCircleOutline';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { useFormikContext } from 'formik';
import moment from 'moment';
import React, { useCallback } from 'react';
import type { WorkingProgramDayType } from '@bookr-technologies/store/types/WorkingProgram';
import { WorkingProgramDayInterval } from './WorkingProgramDayInterval';

interface WorkingProgramDayProps {
    label: string;
    name: string;
}

export function WorkingProgramDay({ name, label }: WorkingProgramDayProps) {
    const formik = useFormikContext();
    const valuesKey = `${name}.values`;
    const enabledKey = `${name}.enabled`;
    const values = formik.getFieldMeta<WorkingProgramDayType['values']>(valuesKey);
    const enabled = formik.getFieldMeta<WorkingProgramDayType['enabled']>(enabledKey);

    const handleEnableChange = useCallback(
        (event: React.ChangeEvent<HTMLInputElement>, checked: boolean): void => {
            formik.setFieldValue(enabledKey, checked, true);
            setTimeout(
                () =>
                    formik.setFieldValue(
                        valuesKey,
                        [
                            {
                                start: '',
                                end: '',
                            },
                        ],
                        true,
                    ),
                50,
            );
        },
        [formik, enabledKey, valuesKey],
    );

    const handleAddNewInterval = useCallback(() => {
        const values = formik.getFieldMeta<WorkingProgramDayType['values']>(valuesKey).value;
        const lastValue = values[values.length - 1];

        formik.setFieldValue(valuesKey, [
            ...values,
            {
                ...lastValue,
                start: lastValue?.end ?? '',
                end: lastValue?.end
                    ? moment(lastValue?.end ?? '', 'HH:mm')
                          .add(1, 'hour')
                          .format('HH:mm')
                    : '',
            },
        ]);
    }, [formik, valuesKey]);

    return (
        <Grid container py={2.5} className={'working-program__row'}>
            <Grid item xs maxWidth={'180px !important'} paddingLeft={1.25}>
                <FormControlLabel
                    control={<Checkbox color={'accent'} checked={!!enabled.value} onChange={handleEnableChange} />}
                    label={
                        <Typography variant={'subtitle1'} sx={{ fontWeight: 600 }}>
                            {label}
                        </Typography>
                    }
                />
            </Grid>
            <Grid item xs container direction={'column'} alignItems={'center'} justifyContent={'center'}>
                {(values.value ?? [{ start: '', end: '' }]).map((value, index) => (
                    <WorkingProgramDayInterval key={index} parent={valuesKey} name={`${valuesKey}.${index}`} />
                ))}
            </Grid>
            <Grid
                item
                xs
                maxWidth={'40px !important'}
                paddingBottom={'3px'}
                container
                alignItems={'flex-end'}
                justifyContent={'center'}
            >
                <IconButton onClick={handleAddNewInterval}>
                    <AddCircleIcon />
                </IconButton>
            </Grid>
        </Grid>
    );
}
