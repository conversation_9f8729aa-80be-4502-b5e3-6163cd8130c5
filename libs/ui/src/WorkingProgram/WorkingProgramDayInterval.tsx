import DeleteIcon from '@mui/icons-material/DeleteOutline';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { useFormikContext } from 'formik';
import moment from 'moment';
import { FocusEvent, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { WorkingHourModel } from '@bookr-technologies/api/models/WorkingHourModel';
import type { WorkingProgramDayType } from '@bookr-technologies/store/types/WorkingProgram';

interface WorkingProgramDayIntervalProps {
    name: string;
    parent: string;
}

export function WorkingProgramDayInterval({ name, parent }: WorkingProgramDayIntervalProps) {
    const { t } = useTranslation('workingProgram');

    const formik = useFormikContext();
    const startKey = `${name}.start`;
    const endKey = `${name}.end`;
    const start = formik.getFieldMeta<WorkingHourModel>(startKey);
    const end = formik.getFieldMeta<WorkingHourModel>(endKey);

    const handleDelete = useCallback(() => {
        const nameSegments = name.split('.');
        const index = Number(nameSegments[nameSegments.length - 1]);
        const values = formik.getFieldMeta<WorkingProgramDayType['values']>(parent).value ?? [];
        values.splice(index, 1);
        if (values.length === 0) {
            values.push({ start: '', end: '', lastUpdatedAt: '' });
        }
        formik.setFieldValue(parent, [...values]);
    }, [formik, name, parent]);

    const handleBlur = useCallback(
        (event: FocusEvent<HTMLInputElement>) => {
            let value = event.target.value;
            if (value) {
                value = moment(value, 'HH:mm').format('HH:mm');
                if (/[a-z]/i.test(value)) {
                    console.log(value);
                    value = '';
                }

                formik.setFieldValue(event.target.name, value, true);
            }

            formik.handleBlur(event);
        },
        [formik],
    );

    return (
        <Grid container direction={'column'} className={'working-day__interval'}>
            <Grid container alignItems={'center'} justifyContent={'center'} mb={1}>
                <Box width={128} px={1.5}>
                    <TextField
                        fullWidth
                        size={'small'}
                        name={startKey}
                        value={start.value}
                        error={!!start.error}
                        onChange={formik.handleChange}
                        onBlur={handleBlur}
                        placeholder={t('start')}
                        title={t('start')}
                    />
                </Box>
                <Typography variant={'body1'} sx={{ fontWeight: 500 }}>
                    -
                </Typography>
                <Box width={128} px={1.5}>
                    <TextField
                        fullWidth
                        size={'small'}
                        name={endKey}
                        value={end.value}
                        error={!!end.error}
                        onChange={formik.handleChange}
                        onBlur={handleBlur}
                        placeholder={t('end')}
                        title={t('end')}
                    />
                </Box>
                <IconButton onClick={handleDelete}>
                    <DeleteIcon />
                </IconButton>
            </Grid>
            <Grid container alignItems={'center'} justifyContent={'center'}>
                {end.error || start.error ? (
                    <Typography variant={'body1'} color={'error'} mr={6.75}>
                        {end.error || start.error}
                    </Typography>
                ) : null}
            </Grid>
        </Grid>
    );
}
