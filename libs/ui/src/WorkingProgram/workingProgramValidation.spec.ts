import { hourSchema } from './workingProgramValidation';

it('should correctly validate time', async function () {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let result: any;
    const schema = hourSchema();

    expect(() => {
        result = schema.validateSync({
            start: '09:00',
            end: '17:00',
        });
    }).not.toThrow();

    expect(result.start).toBe('09:00');
    expect(result.end).toBe('17:00');

    expect(() => schema.validateSync({ start: '1', end: '7:00' })).not.toThrow();
    expect(() => schema.validateSync({ start: '1:0', end: '7:00' })).not.toThrow();
    expect(schema.validateSync({ start: '1:0', end: '7:00' })).toMatchObject({
        start: '01:00',
        end: '07:00',
    });

    expect(() => schema.validateSync({ start: '02:50', end: 's3:50' })).toThrow();
    expect(() => schema.validateSync({ start: '19:00', end: '17:00' })).toThrow();
    expect(() => schema.validateSync({ start: '10:00', end: '27:00' })).toThrow();
    expect(() => schema.validateSync({ start: 'SS:00', end: '66:99' })).toThrow();
    expect(() => schema.validateSync({ start: '44d0:0', end: '337:0' })).toThrow();
});
