import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { WorkingProgramDays } from '@bookr-technologies/store/types/WorkingProgram';
import { WorkingProgramDay } from './WorkingProgramDay';

interface WorkingProgramProps {
    name: string;
}

const Root = styled(Stack)({
    width: '100%',

    '.working-program__row': {
        borderBottom: '1px solid #e2e2e2',
        '&:last-child': {
            borderBottom: 'none',
        },
    },

    '.working-day__interval': {
        marginBottom: 20,
        '&:last-child': {
            marginBottom: 0,
        },
    },
});

export function WorkingProgram({ name }: WorkingProgramProps) {
    const { t } = useTranslation('workingProgram');
    return (
        <Root>
            {WorkingProgramDays.map((day) => (
                <WorkingProgramDay key={day} label={t(`days.${day}`)} name={`${name}.${day}`} />
            ))}
        </Root>
    );
}
