/* eslint-disable no-template-curly-in-string */
import { t } from 'i18next';
import moment from 'moment';
import * as Yup from 'yup';

const isBefore = (startTime: string, endTime: string) => {
    return moment(startTime, 'HH:mm').isBefore(moment(endTime, 'HH:mm'));
};

const isAfter = (startTime: string, endTime: string) => {
    return moment(endTime, 'HH:mm').isAfter(moment(startTime, 'HH:mm'));
};

const time = (required: boolean) => {
    let rule = Yup.string();
    if (required) {
        rule = rule.required(t('errors.requiredField', { ns: 'common' }));
    }

    return rule
        .transform((value) => {
            const [hour, mins] = value.split(':');
            const validHours = hour && /^([0-1]?[0-9]|2[0-3])$/.test(hour);
            const validMins = mins && /^([0-9]|[0-5][0-9])$/.test(mins);
            if (value && validHours && validMins) {
                return moment(value, 'HH:mm').format('HH:mm');
            }
            return value;
        })
        .matches(/^([0-9]|[0-1]?[0-9]|2[0-3])(:[0-5][0-9])?$/, t('errors.invalidTime', { ns: 'common' }));
};

export const hourSchema = (required: boolean) =>
    Yup.object().shape({
        start: time(required).test({
            name: 'is-before',
            message: (params) => {
                console.log('before', params);
                return t('errors.timeBefore', { ns: 'common', value: params.value });
            },
            test(startTime?: string) {
                if (!required && !startTime) {
                    return true;
                }

                return isBefore(startTime ?? '', this.parent.end ?? '');
            },
            params: {
                start: Yup.ref('start', { map: (value) => value ?? '' }),
                end: Yup.ref('end', { map: (value) => value ?? '' }),
            },
        }),
        end: time(required).test({
            name: 'is-after',
            message: (params) => {
                console.log('after', params);
                return t('errors.timeAfter', { ns: 'common', value: params.value });
            },
            test(endTime?: string) {
                if (!required && !endTime) {
                    return true;
                }

                return isAfter(this.parent.start ?? '', endTime ?? '');
            },
            params: {
                start: Yup.ref('start', { map: (value) => value ?? '' }),
                end: Yup.ref('end', { map: (value) => value ?? '' }),
            },
        }),
    });

export const daySchema = () =>
    Yup.object().shape({
        enabled: Yup.boolean(),
        values: Yup.mixed().when('enabled', {
            is: (value: boolean) => value,
            then: Yup.array().of(hourSchema(true)),
            otherwise: Yup.array().of(hourSchema(false)),
        }),
    });

export const workingProgramValidationSchema = Yup.object().shape({
    monday: daySchema(),
    tuesday: daySchema(),
    wednesday: daySchema(),
    thursday: daySchema(),
    friday: daySchema(),
    saturday: daySchema(),
    sunday: daySchema(),
});

export const workingProgramValidation = (name: string) =>
    Yup.object().shape({
        [name]: workingProgramValidationSchema,
    });
