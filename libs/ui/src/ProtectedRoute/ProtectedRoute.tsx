/* eslint-disable @typescript-eslint/no-explicit-any */
import moment from 'moment';
import React, { ElementType } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { SubscriptionStatus } from '@bookr-technologies/api/models/SubscriptionModel';
import { useAuthStore } from '@bookr-technologies/store/authStore';
import RootPageLoader from '../PageLoader/RootPageLoader';
import { detectBusinessSignUpPath } from './utils';

export interface ProtectedRouteProps<P extends Record<string, any>> {
    redirectTo?: string;
    LoadingComponent?: ElementType;
    props?: P;
}

export function ProtectedRoute<P extends Record<string, any>>(
    Component: ElementType,
    { redirectTo, LoadingComponent = RootPageLoader, props }: ProtectedRouteProps<P> = {},
) {
    function WrapperComponent() {
        const authenticated = useAuthStore((state) => state.authenticated);
        const user = useAuthStore((state) => state.user);
        const metadata = useAuthStore((state) => state.metadata);
        const subscription = useAuthStore((state) => state.subscription);
        const firebaseUser = useAuthStore((state) => state.firebaseUser);
        const location = useLocation();
        const postSignInUrl = location.pathname;

        if (authenticated === null) {
            return <LoadingComponent />;
        }

        if (!authenticated) {
            return <Navigate to={redirectTo ?? '/auth/sign-in'} state={{ postSignInUrl }} replace />;
        }

        if (!user || user.accountType === AccountType.Invalid) {
            return <Navigate to={'/auth/sign-up'} state={{ postSignInUrl }} replace />;
        }

        if (!user.business?.id) {
            const nextPath = detectBusinessSignUpPath();
            return <Navigate to={nextPath} state={{ postSignInUrl }} replace />;
        }

        if (firebaseUser && !metadata?.verified) {
            return <Navigate to={'/auth/sign-up/verification'} state={{ email: firebaseUser.email }} replace />;
        }

        const skipCheck =
            subscription?.status === SubscriptionStatus.Active ||
            (subscription?.status === SubscriptionStatus.Trial && moment(subscription?.trialEnd).isAfter(moment()));

        if (
            !skipCheck &&
            subscription?.shouldSelectSubscriptionPlan &&
            location.pathname !== '/subscription/plans/select'
        ) {
            return (
                <Navigate
                    to={'/subscription/plans/select'}
                    state={{ postSubscriptionUrl: window.location.href }}
                    replace
                />
            );
        }

        return <Component {...(props || {})} />;
    }

    return <WrapperComponent />;
}
