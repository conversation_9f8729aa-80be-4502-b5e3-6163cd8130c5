import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import React from 'react';
import { useTranslation } from 'react-i18next';

const StyledCard = styled('aside')`
    width: 300px;
    height: 250px;
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 0;
    padding-left: 16px;
    margin-left: auto;
    margin-right: auto;
    background-color: rgb(255, 255, 255, 0.6);
    backdrop-filter: blur(8px) saturate(280%);

    @supports not (backdrop-filter: blur(8px) staturate(280%)) {
        background-color: #fff;
    }

    .title {
        font-size: 33px !important;
        margin-top: 50px !important;
        margin-bottom: 6px !important;
        align-self: center;
    }

    .recommendedTitle {
        margin-top: 50px !important;
    }

    .price {
        font-size: 24px !important;
        align-self: center;
    }

    .rowsContainer {
        gap: 10px;
        margin-top: 24px;
    }

    .row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        margin-left: 30px;
        height: 24px;
        width: 250px;
        gap: 10px;
    }

    .rowIcon {
        width: 20px;
        height: 20px;
        color: #2f80fb;
    }

    .rowText {
        font-size: 14px;
        color: #757575;
    }

    .button {
        width: 200px;
        height: 56px;
        align-self: center;
        margin-top: 60px;
        border-radius: 20px;
        color: #111111;
        background-color: ${({ theme }) => theme.palette.grey['100']};
        text-transform: none;
    }

    .activeButton {
        color: white;
        background-color: #2f80fb;
    }

    .recommended {
        padding: 6px 18px;
        font-size: 14px;
        border-radius: 9px;
        color: #fff;
        background-color: #2f80fb;
        margin-top: -32px;
    }

    .MuiLink-root {
        color: inherit;
        text-decoration: none;
        display: contents;
    }

    @media screen and (max-width: 1550px) {
        width: calc(100% + 24px);
        height: auto;
        margin: 0 -12px;
        flex-direction: row;
        align-items: flex-end;
        justify-content: space-between;
        padding: 32px 8px;

        .button {
            margin-top: 0;
            width: auto;
            height: auto;
            padding: 4px 18px;
            font-size: 14px;
        }

        .title {
            font-size: 24px !important;
            margin-top: 0 !important;
        }

        .recommended {
            margin: 0 0 16px;
        }

        .price {
            font-size: 18px !important;
        }
    }
`;

interface ExpandedPricingCard {
    title: string;
    price: string;
    buttonText: string;
    isRecommended?: boolean;
    isCustom?: boolean;
}

export function ExpandedPricingCard({ title, price, buttonText, isRecommended, isCustom }: ExpandedPricingCard) {
    const { t } = useTranslation('pricingPlans');

    const handleContactUsButtonClicked = () => {
        window.open('mailto:<EMAIL>?subject=Price%20offer&');
    };

    return (
        <StyledCard className={'StyledCard-root'}>
            <div className={'StyledCard-information'}>
                {isRecommended && (
                    <Typography
                        className={'recommended'}
                        variant={'h4'}
                        fontWeight={'600'}
                        display={'inline-block'}
                        align={'center'}
                    >
                        {t('recommended')}
                    </Typography>
                )}
                <Typography
                    className={classNames('title', { recommendedTitle: isRecommended })}
                    variant={'h4'}
                    fontWeight={'bold'}
                >
                    {title}
                </Typography>
                <Typography className={'price'} variant={'h3'} fontWeight={'500'}>
                    {price}
                </Typography>
            </div>
            <div className={'StyledCard-action'}>
                {isCustom ? (
                    <Button
                        className={classNames('button', { activeButton: isRecommended })}
                        variant={'contained'}
                        onClick={handleContactUsButtonClicked}
                    >
                        {buttonText}
                    </Button>
                ) : (
                    <Button
                        className={classNames('button', { activeButton: isRecommended })}
                        variant={'contained'}
                        href="/"
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        {buttonText}
                    </Button>
                )}
            </div>
        </StyledCard>
    );
}
