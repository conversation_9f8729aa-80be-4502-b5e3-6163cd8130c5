import AddIcon from '@mui/icons-material/Add';
import CheckIcon from '@mui/icons-material/Check';
import Button from '@mui/material/Button';
import Link from '@mui/material/Link';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import React from 'react';
import { useTranslation } from 'react-i18next';

const StyledCard = styled(Paper)`
    width: 300px;
    height: 400px;
    display: flex;
    flex-direction: column;
    border-radius: 20px;
    margin-top: 80px;

    .title {
        font-size: 33px !important;
        margin-top: 32px !important;
        margin-bottom: 6px !important;
        margin-left: 35px !important;
    }

    .recommendedTitle {
        margin-top: 16px !important;
    }

    .price {
        font-size: 24px !important;
        margin-left: 35px !important;
    }

    .rowsContainer {
        gap: 10px;
        margin-top: 24px;
    }

    .row {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: flex-start;
        margin-left: 30px;
        height: 24px;
        width: 250px;
        gap: 10px;
    }

    .rowIcon {
        width: 20px;
        height: 20px;
        color: #2f80fb;
    }

    .rowText {
        font-size: 14px;
        font-weight: 500;
        color: #757575;
    }

    .headerItemRowText {
        font-weight: 600;
    }

    .button {
        width: 240px;
        height: 40px;
        align-self: center;
        margin-top: auto;
        margin-bottom: 32px;
        border-radius: 10px;
        color: #111111;
        background-color: ${({ theme }) => theme.palette.grey['100']};
        text-transform: none;
    }

    .activeButton {
        color: white;
        background-color: #2f80fb;
    }

    .recommended {
        width: 129px;
        height: 32px;
        display: flex;
        align-items: center;
        align-self: center;
        justify-content: center;
        font-size: 14px;
        border-radius: 9px;
        color: white;
        background-color: #2f80fb;
        margin-top: -16px;
    }

    .MuiLink-root {
        color: inherit;
        text-decoration: none;
        display: contents;
    }
`;

interface PricingCardProps {
    title: string;
    price: string;
    itemsText: string[];
    buttonText: string;
    isRecommended?: boolean;
    isCustom?: boolean;
    hasHeaderItem?: boolean;
}

export function PricingCard({
    title,
    price,
    itemsText,
    buttonText,
    isRecommended,
    isCustom,
    hasHeaderItem,
}: PricingCardProps) {
    const { t } = useTranslation('pricingPlans');

    const handleContactUsButtonClicked = () => {
        window.open('mailto:<EMAIL>?subject=Price%20offer&');
    };

    return (
        <StyledCard className={isRecommended ? 'recommendedCard' : ''}>
            {isRecommended && (
                <Typography className={'recommended'} variant={'h4'} fontWeight={'600'}>
                    {t('recommended')}
                </Typography>
            )}
            <Typography
                className={classNames('title', { recommendedTitle: isRecommended })}
                variant={'h4'}
                fontWeight={'bold'}
            >
                {title}
            </Typography>
            <Typography className={'price'} variant={'h3'} fontWeight={'500'}>
                {price}
            </Typography>
            <Stack className={'rowsContainer'} direction={'column'}>
                <Stack className={'row'} direction={'row'}>
                    <CheckIcon className={'rowIcon'} />
                    <Typography
                        className={classNames('rowText', { headerItemRowText: hasHeaderItem })}
                        variant={'h3'}
                        fontWeight={'bold'}
                    >
                        {itemsText[0]}
                    </Typography>
                </Stack>
                <Stack className={'row'} direction={'row'}>
                    {!hasHeaderItem && !isCustom && <CheckIcon className={'rowIcon'} />}
                    {hasHeaderItem && !isCustom && <AddIcon className={'rowIcon'} />}
                    <Typography className={'rowText'} variant={'h3'} fontWeight={'bold'}>
                        {itemsText[1]}
                    </Typography>
                </Stack>
                {!isCustom && (
                    <>
                        <Stack className={'row'} direction={'row'}>
                            {hasHeaderItem ? <AddIcon className={'rowIcon'} /> : <CheckIcon className={'rowIcon'} />}
                            <Typography className={'rowText'} variant={'h3'} fontWeight={'bold'}>
                                {itemsText[2]}
                            </Typography>
                        </Stack>
                        <Stack className={'row'} direction={'row'}>
                            {hasHeaderItem ? <AddIcon className={'rowIcon'} /> : <CheckIcon className={'rowIcon'} />}
                            <Typography className={'rowText'} variant={'h3'} fontWeight={'bold'}>
                                {itemsText[3]}
                            </Typography>
                        </Stack>
                        <Stack className={'row'} direction={'row'}>
                            {hasHeaderItem ? <AddIcon className={'rowIcon'} /> : <CheckIcon className={'rowIcon'} />}
                            <Typography className={'rowText'} variant={'h3'} fontWeight={'bold'}>
                                {itemsText[4]}
                            </Typography>
                        </Stack>
                    </>
                )}
            </Stack>
            {isCustom ? (
                <Button
                    className={classNames('button', { activeButton: isRecommended })}
                    variant={'contained'}
                    onClick={handleContactUsButtonClicked}
                >
                    {buttonText}
                </Button>
            ) : (
                <Link href="https://dashboard.bookr.ro/" target="_blank" rel="noopener noreferrer">
                    <Button className={classNames('button', { activeButton: isRecommended })} variant={'contained'}>
                        {buttonText}
                    </Button>
                </Link>
            )}
        </StyledCard>
    );
}
