import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import classNames from 'classnames';
import React from 'react';
import { Link, LinkProps } from 'react-router-dom';
import classes from './BackButton.module.css';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Props = LinkProps & IconButtonProps & { component?: any };

export function BackButton({ component, className, ...rest }: Props) {
    const buttonComponent = rest.to ? Link : component || 'a';

    return (
        <IconButton component={buttonComponent} className={classNames(className, classes.root)} {...rest}>
            <ArrowBackIcon />
        </IconButton>
    );
}
