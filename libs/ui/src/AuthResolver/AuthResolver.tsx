import { useEffect } from 'react';
import { FirebaseService } from '@bookr-technologies/core/services';
import { useAuthStore } from '@bookr-technologies/store';

export function AuthResolver() {
    useEffect(
        () =>
            FirebaseService.auth().onAuthStateChanged((user) => {
                if (user) {
                    useAuthStore.getState().resolveUser();
                } else {
                    useAuthStore.getState().logout();
                }
            }),
        [],
    );

    return null;
}
