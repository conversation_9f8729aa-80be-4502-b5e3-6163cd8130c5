import { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { PropsWithChildren, useMemo } from 'react';
import { defaultTheme } from './defaultTheme';

const jakartaFont = "'Plus Jakarta Display', 'Poppins', 'sans-serif'";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function ThemeProvider(props: PropsWithChildren<any>) {
    const theme = useMemo(
        () =>
            createTheme(defaultTheme, {
                typography: {
                    h1: {
                        fontFamily: jakartaFont,
                    },
                    h2: {
                        fontFamily: jakartaFont,
                    },
                    h3: {
                        fontFamily: jakartaFont,
                    },
                    h4: {
                        fontFamily: jakartaFont,
                    },
                    h5: {
                        fontFamily: jakartaFont,
                    },
                },
                palette: {
                    error: {
                        main: '#F05253',
                        contrastText: defaultTheme.palette.getContrastText('#F05253'),
                    },
                    accent: {
                        main: '#2f80fb',
                        contrastText: defaultTheme.palette.getContrastText('#2f80fb'),
                    },
                    primary: {
                        main: '#111',
                        contrastText: defaultTheme.palette.getContrastText('#111'),
                    },
                    secondary: {
                        main: '#2f80fb',
                        contrastText: defaultTheme.palette.getContrastText('#2f80fb'),
                    },
                    info: {
                        main: '#2f80fb',
                        contrastText: defaultTheme.palette.getContrastText('#fff'),
                    },
                    success: {
                        main: '#05944F',
                        contrastText: defaultTheme.palette.getContrastText('#05944F'),
                    },
                    grey: {
                        100: '#eeeeee',
                        500: '#757575',
                    },
                    text: {
                        secondary: '#757575',
                    },
                },
            }),
        [],
    );

    return <MuiThemeProvider theme={theme} {...props} />;
}
