/* eslint-disable @typescript-eslint/no-explicit-any */
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { Meta } from '@storybook/react';
import { ThemeProvider } from './ThemeProvider';

export default {
    component: ThemeProvider,
    title: 'ThemeProvider',
} as Meta;

const StyledStack = styled(Stack)({
    alignItems: 'center',
    padding: '14px 0',
});

export const Forms = () => (
    <ThemeProvider>
        <Container>
            <Stack>
                <Typography variant={'h4'}>Forms</Typography>
                {['outlined', 'standard', 'filled'].map((variant: any) => (
                    <Stack key={variant} sx={{ marginBottom: 4 }}>
                        <Typography variant={'caption'} style={{ textTransform: 'capitalize' }}>
                            {variant}
                        </Typography>
                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Initial</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField variant={variant} label={'Phone Number'} />
                            </Grid>
                        </StyledStack>

                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Placeholder</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField variant={variant} placeholder={'Phone Number'} />
                            </Grid>
                        </StyledStack>

                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Typing</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField variant={variant} focused value={'999-999-99'} label={'Phone Number'} />
                            </Grid>
                        </StyledStack>

                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Filled</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField variant={variant} value={'999-999-99'} label={'Phone Number'} />
                            </Grid>
                        </StyledStack>

                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Disabled</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField variant={variant} disabled label={'Phone Number'} />
                            </Grid>
                        </StyledStack>

                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Caption</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField
                                    variant={variant}
                                    label={'Phone Number'}
                                    helperText={"Here's a hint that might help you."}
                                />
                            </Grid>
                        </StyledStack>

                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Success Message</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField
                                    variant={variant}
                                    value={'999-999-999'}
                                    label={'Phone Number'}
                                    helperText={'Success message'}
                                />
                            </Grid>
                        </StyledStack>

                        <StyledStack direction={'row'}>
                            <Grid xs={3}>
                                <Typography>Error Message</Typography>
                            </Grid>
                            <Grid xs={9}>
                                <TextField
                                    variant={variant}
                                    value={'999-999-999'}
                                    label={'Phone Number'}
                                    helperText={'Error message'}
                                    error
                                />
                            </Grid>
                        </StyledStack>
                    </Stack>
                ))}
            </Stack>
        </Container>
    </ThemeProvider>
);
