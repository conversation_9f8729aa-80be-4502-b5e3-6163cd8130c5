import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { createTheme } from '@mui/material/styles';

declare module '@mui/material/styles' {
    interface Palette {
        accent: Palette['primary'];
    }

    interface PaletteOptions {
        accent: PaletteOptions['primary'];
    }
}

declare module '@mui/material/Button' {
    interface ButtonPropsColorOverrides {
        accent: true;
    }
}

declare module '@mui/material/Checkbox' {
    interface CheckboxPropsColorOverrides {
        accent: true;
    }
}

declare module '@mui/material/SvgIcon' {
    interface SvgIconPropsColorOverrides {
        accent: true;
    }
}

export const defaultTheme = createTheme({
    typography: {
        fontFamily: '"Poppins", "Roboto", "Helvetica", "Arial", sans-serif',
    },
    components: {
        MuiAutocomplete: {
            styleOverrides: {
                input: {
                    padding: '1px 0 1px 15px !important',
                },
            },
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    borderRadius: 16,
                    backgroundColor: '#f6f6f6',
                    padding: '10px 24px',
                },
                input: {
                    minHeight: 44,
                    padding: 0,
                    fontSize: 16,
                    fontWeight: 500,
                    color: '#545454',
                },
                notchedOutline: {
                    border: '1px solid #757575',
                    top: 0,
                    '& legend': {
                        display: 'none',
                    },
                },
                sizeSmall: {
                    padding: '1px 24px',
                    borderRadius: 8,
                },
                multiline: {
                    paddingTop: 14,
                },
            },
        },
        MuiInputLabel: {
            styleOverrides: {
                outlined: {
                    height: 24,
                    transform: 'translate3d(24px, 20px, 0) scale(1)',
                    color: '#545454',
                    fontSize: '16px',
                    fontWeight: 500,
                    lineHeight: '24px',
                },
                shrink: {
                    '&.MuiInputLabel-outlined': {
                        fontSize: '14px',
                        transform: 'translate3d(24px, 10px, 0) scale(0.75) !important',
                    },
                    '&.MuiInputLabel-outlined.MuiInputLabel-sizeSmall': {
                        fontSize: '14px',
                        transform: 'translate3d(24px, 3px, 0) scale(0.75) !important',
                    },
                    '& + .MuiOutlinedInput-root': {
                        paddingTop: 18,
                        paddingBottom: 2,
                    },

                    '& + .MuiOutlinedInput-root .MuiInputBase-inputSizeSmall': {
                        minHeight: 26,
                    },
                },
                sizeSmall: {
                    transform: 'translate3d(24px, 11px, 0) scale(1) !important',
                },
            },
        },
        MuiFormHelperText: {
            styleOverrides: {
                root: {
                    color: '#757575',
                    fontSize: 14,
                    fontWeight: 400,
                    lineHeight: '24px',
                    margin: '0 4px',
                },
            },
        },
        MuiButton: {
            styleOverrides: {
                root: {
                    fontWeight: 500,
                    textTransform: 'unset',
                    borderRadius: 20,
                    fontSize: 18,
                },
                outlined: {
                    borderWidth: '2px !important',
                    boxSizing: 'border-box',
                },
                containedPrimary: {
                    backgroundColor: '#111',
                    color: '#fff',

                    '&:hover': {
                        backgroundColor: '#333',
                    },
                },

                sizeLarge: {
                    minWidth: 200,
                    paddingTop: 18,
                    paddingBottom: 18,
                    '&.MuiButton-outlined': {
                        paddingTop: 16,
                        paddingBottom: 16,
                    },
                },

                sizeMedium: {
                    minWidth: 120,
                    padding: '14px 24px',
                    fontSize: 16,
                    '&.MuiButton-outlined': {
                        paddingTop: 12,
                        paddingBottom: 12,
                    },
                },

                sizeSmall: {
                    paddingTop: 8,
                    paddingBottom: 8,
                    fontSize: 14,
                    minWidth: 100,
                    borderRadius: 10,
                    '&.MuiButton-outlined': {
                        paddingTop: 6,
                        paddingBottom: 6,
                    },
                },
            },
            defaultProps: {
                disableElevation: true,
            },
        },
        MuiSelect: {
            defaultProps: {
                IconComponent: KeyboardArrowDownIcon,
            },
            styleOverrides: {
                outlined: {},

                select: {
                    minHeight: 44,
                    lineHeight: '44px',
                    padding: 0,
                    fontSize: 16,
                    fontWeight: 500,
                    color: '#545454',
                },
                filled: {
                    padding: '10px 56px 10px 24px !important',
                    '&.MuiInputBase-sizeSmall': {
                        padding: '0 56px 0 24px !important',
                    },
                },
                icon: {
                    right: 14,
                },
            },
        },
        MuiFilledInput: {
            styleOverrides: {
                underline: {
                    '&:before, &:after': {
                        display: 'none',
                    },
                },
                root: {
                    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
                    borderRadius: 8,
                    padding: 0,
                    overflow: 'hidden',
                },
                input: {
                    backgroundColor: '#fff',
                },
            },
        },
        MuiMenu: {
            styleOverrides: {
                paper: {
                    borderRadius: 14,
                    boxShadow: '0 11px 20px rgba(5, 16, 55, 0.1)',
                },
                list: {
                    '.MuiMenuItem-root': {
                        margin: '8px 16px',
                        padding: '10px 16px',
                        borderRadius: 14,
                        minHeight: 40,
                        minWidth: 140,
                        fontWeight: 500,
                    },
                },
            },
        },
        MuiPaper: {
            styleOverrides: {
                root: {
                    borderRadius: 14,
                    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
                },
                outlined: {
                    padding: '16px',
                    backgroundColor: '#f6f6f6',
                    borderColor: '#cbcbcb',
                    boxShadow: 'none',
                },
            },
        },
    },
});
