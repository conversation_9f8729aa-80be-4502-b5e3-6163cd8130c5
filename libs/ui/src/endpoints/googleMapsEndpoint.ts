import { Loader } from '@googlemaps/js-api-loader';
import { Coords } from 'google-map-react';
import { GoogleMapsGeocodingKey } from '@bookr-technologies/api/constants/GoogleMaps';
import { createResource } from '@bookr-technologies/api/createResource';
import { GeocodingResult } from '@bookr-technologies/api/models/GeocodingResult';
import { Defer } from '@bookr-technologies/core/promises/defer';

let loader: ReturnType<Loader['load']>;

function loadGoogleMaps() {
    if (window.google) {
        return window.google;
    }

    if (!loader) {
        loader = new Loader({
            apiKey: GoogleMapsGeocodingKey,
            libraries: ['places'],
        }).load();
    }

    return loader;
}

export const googleMapsEndpoint = createResource(
    `https://maps.googleapis.com/maps/api`,
    (resource) => ({
        async getPlacesByAddress(address: string): Promise<GeocodingResult[]> {
            const google = await loadGoogleMaps();
            const places = new google.maps.places.PlacesService(document.createElement('div'));
            const defer = new Defer();

            places.textSearch(
                {
                    query: address,
                },
                (results, status) => {
                    if (status === google.maps.places.PlacesServiceStatus.OK) {
                        defer.resolve(results);
                    } else {
                        defer.resolve([]);
                    }
                },
            );

            return defer.wait();
        },
        async getPlacesByCoordinates(coords: Coords): Promise<GeocodingResult[]> {
            const { lat, lng } = JSON.parse(JSON.stringify(coords));
            const { data } = await resource.api.get('geocode/json', {
                params: { latlng: `${lat},${lng}` },
            });

            return data.results;
        },
    }),
    {
        params: {
            key: GoogleMapsGeocodingKey,
        },
    },
);
