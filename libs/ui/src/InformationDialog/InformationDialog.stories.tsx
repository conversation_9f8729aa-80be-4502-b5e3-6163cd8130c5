import { <PERSON>a, <PERSON> } from '@storybook/react';
import { InformationDialog, InformationDialogProps } from './InformationDialog';

export default {
    component: InformationDialog,
    title: 'InformationDialog',
} as Meta;

const Template: Story<InformationDialogProps> = (args) => <InformationDialog {...args} />;

export const Default = Template.bind({});
Default.args = {
    isOpen: true,
    title: 'Payment success!',
    message: 'Your payment was completed successfully! You will receive an email with the payment details soon.',
};

export const WithIcon = Template.bind({});
WithIcon.args = {
    title: 'Congratulations!',
    message: 'Custom message of the modal goes here. A short description about what happened.',
    icon: 'https://placeimg.com/256/256/success',
};
