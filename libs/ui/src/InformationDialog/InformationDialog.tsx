import CloseIcon from '@mui/icons-material/Close';
import Dialog from '@mui/material/Dialog';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import blueCheckmark from '../assets/blueCheckmark.png';

export interface InformationDialogProps {
    isOpen: boolean;
    title: string;
    message: string;
    onClose: () => void;
    icon?: string;
}

const StyledDialog = styled(Dialog)`
    padding: 36px;

    .MuiDialog-paper {
        display: flex;
        max-width: 680px;
        box-shadow: 0 11px 20px rgba(5, 16, 55, 0.1);
        border-radius: 14px;
        padding: 16px;
    }

    .title {
        color: #1f1f1f;
    }

    img {
        width: 70px;
        height: 70px;
    }
`;

export function InformationDialog({ isOpen, title, message, onClose, icon }: InformationDialogProps) {
    return (
        <StyledDialog open={isOpen} onClose={onClose}>
            <Grid container alignItems={'center'} justifyContent={'space-between'} flexDirection={'row-reverse'}>
                <IconButton onClick={onClose}>
                    <CloseIcon />
                </IconButton>
            </Grid>

            <Stack px={6} pt={6} pb={10} alignItems={'center'} justifyContent={'center'}>
                <img src={icon || blueCheckmark} alt={'Confirmation'} />
                <Typography variant={'h5'} fontWeight={700} textAlign={'center'} mt={4} mb={1}>
                    {title}
                </Typography>
                <Typography variant={'body2'} fontWeight={500} color={'textSecondary'} textAlign={'center'}>
                    {message}
                </Typography>
            </Stack>
        </StyledDialog>
    );
}
