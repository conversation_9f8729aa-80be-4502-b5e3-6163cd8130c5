export function rgba2hex(rgba: string) {
    const match = rgba.match(/^rgba?[\s+]?\([\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?,[\s+]?(\d+)[\s+]?/i);
    return match && match.length === 4
        ? '#' +
              ('0' + parseInt(match[1], 10).toString(16)).slice(-2) +
              ('0' + parseInt(match[2], 10).toString(16)).slice(-2) +
              ('0' + parseInt(match[3], 10).toString(16)).slice(-2)
        : '';
}
