import type { Breakpoints, Theme } from '@mui/material/styles';
import { Breakpoint } from '@mui/system/createTheme/createBreakpoints';

type AllowedMethods = 'up' | 'down';

export function breakpoint<T extends Theme, B extends Breakpoints, K extends keyof Pick<B, AllowedMethods>>(
    method: K,
    value: Breakpoint | number,
) {
    return ({ theme }: { theme: T }) => {
        return theme.breakpoints[method](value);
    };
}
