/* eslint-disable @typescript-eslint/no-explicit-any */
import LocationOn from '@mui/icons-material/LocationOn';
import CircularProgress from '@mui/material/CircularProgress';
import { styled } from '@mui/material/styles';
import GoogleMapReact, { Coords, Props } from 'google-map-react';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { GoogleMapsApiKey } from '@bookr-technologies/api/constants/GoogleMaps';
import { DEFAULT_LOCATION, DEFAULT_ZOOM_VIEW, ZOOMED_VIEW } from '../LocationDialog';

const Root = styled('div')({
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
});

const Marker = styled(LocationOn)({
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -100%)',
    pointerEvents: 'none',
});

export interface LocationMapProps extends Omit<Props, 'bootstrapURLKeys'> {
    center: Coords;
    autoDetect?: boolean;
}

export function LocationMap({ center, zoom: defaultZoom, autoDetect, ...rest }: LocationMapProps) {
    const mapRef = useRef<GoogleMapReact>(null);
    const [loadingLocation, setLoadingLocation] = useState<boolean>(false);
    const [zoom, setZoom] = useState<number>(center ? ZOOMED_VIEW : DEFAULT_ZOOM_VIEW);
    const centerValue = useMemo(() => {
        const value = JSON.parse(JSON.stringify(center ?? {}));

        if (Object.keys(value).length === 0) {
            return DEFAULT_LOCATION;
        }

        return value;
    }, [center]);

    useEffect(
        () => {
            if (!center && autoDetect) {
                try {
                    setLoadingLocation(true);
                    navigator.geolocation.getCurrentPosition(
                        ({ coords }) => {
                            setZoom(ZOOMED_VIEW);
                            if (rest.onChange) {
                                rest.onChange({
                                    center: {
                                        lat: coords.latitude,
                                        lng: coords.longitude,
                                    },
                                } as any);
                            }

                            setLoadingLocation(false);
                        },
                        () => setLoadingLocation(false),
                        {
                            maximumAge: 86400000,
                            timeout: 5000,
                        },
                    );
                    return;
                } catch (e) {
                    // Nothing
                } finally {
                    setLoadingLocation(false);
                }
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [center, autoDetect, rest.onChange],
    );

    useEffect(() => {
        if (defaultZoom && defaultZoom > 0) {
            setZoom(defaultZoom);
        }
    }, [defaultZoom]);

    useEffect(() => {
        if (!center || center === DEFAULT_LOCATION) {
            setZoom(Math.ceil(DEFAULT_ZOOM_VIEW / 2));
        }
    }, [center]);

    return (
        <Root>
            {loadingLocation ? (
                <CircularProgress />
            ) : (
                <>
                    <GoogleMapReact
                        bootstrapURLKeys={{ key: GoogleMapsApiKey, libraries: ['places'] }}
                        defaultCenter={DEFAULT_LOCATION}
                        center={centerValue}
                        zoom={zoom}
                        ref={mapRef}
                        debounced
                        {...rest}
                    />
                    <Marker color={'primary'} fontSize={'large'} />
                </>
            )}
        </Root>
    );
}
