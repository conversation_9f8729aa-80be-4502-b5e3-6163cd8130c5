import React from 'react';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';

interface SeoProps {
    title?: string;
    seoDescription?: string;
    seoKeywords?: string;
    socialTitle?: string;
    socialDescription?: string;
    socialImageUrl?: string;
}

export function Seo({ title, seoDescription, seoKeywords, socialTitle, socialDescription, socialImageUrl }: SeoProps) {
    const { t } = useTranslation('seo');

    return (
        <Helmet>
            <title>{title || t('title')}</title>
            <meta name="title" content={title || t('seo_title')} />
            <meta name="description" content={seoDescription || t('seo_description')} />
            <meta name="keywords" content={seoKeywords || t('seo_keywords')} />
            <meta property="og:url" content={window.location.href} />
            <meta property="og:type" content="website" />
            <meta property="og:title" content={socialTitle || t('social_title')} />
            <meta property="og:description" content={socialDescription || t('social_description')} />
            <meta property="og:image" content={socialImageUrl || t('social_image_url')} />
            <link href={socialImageUrl || t('social_image_url')} rel="image_src" />
        </Helmet>
    );
}
