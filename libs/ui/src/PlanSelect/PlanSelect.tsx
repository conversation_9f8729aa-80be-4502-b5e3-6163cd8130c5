import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { alpha, styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React, { useCallback, useMemo, useState } from 'react';
import { useQuery } from 'react-query';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { checkoutEndpoint } from '@bookr-technologies/api/endpoints/checkoutEndpoint';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { Plan } from './Plan';

interface Props {
    period: PeriodType;
    withCustom?: boolean;
    landings?: boolean;
}

const Root = styled('div')(({ theme }) => ({
    '.MuiTabs-indicator': {
        display: 'none',
    },
    '.MuiTabs-root': {
        width: '100%',
    },
    '.MuiTab-root': {
        padding: theme.spacing(1.5, 2.5),
        margin: theme.spacing(1, 1, 1, 0),
        minHeight: 0,
        fontWeight: 600,
    },
    '.MuiTab-root.Mui-selected': {
        backgroundColor: alpha(theme.palette.accent.main, 0.1),
        color: theme.palette.accent.main,
        borderRadius: 14,
    },
}));

export function PlanSelect({ period, landings, withCustom }: Props) {
    const [planName, setPlanName] = useState('professional');
    const isDownLg = useMediaQuery<Theme>((theme) => theme.breakpoints.down('lg'));

    const handleTabChange = useCallback((event: React.SyntheticEvent, newValue: string) => setPlanName(newValue), []);

    const plans = useQuery(`checkout/subscriptions/plans/withCustom=${withCustom}`, () =>
        checkoutEndpoint.getSubscriptionPlans({ withCustom }),
    );

    const list = useMemo(
        () => SubscriptionPlanModel.getSortedPlansByPeriod(plans.data ?? {}, period),
        [plans.data, period],
    );

    const activePlan = useMemo(
        () => list.find((plan) => String(plan.planName).toLowerCase() === String(planName).toLowerCase()),
        [planName, list],
    );

    const plans$ = useMemo(() => {
        if (isDownLg) {
            return (
                <>
                    <Tabs variant={'scrollable'} value={planName} onChange={handleTabChange}>
                        <Tab disableRipple value={'free'} label={SubscriptionPlanModel.getPlanName('free')} />
                        <Tab disableRipple value={'standard'} label={SubscriptionPlanModel.getPlanName('standard')} />
                        <Tab
                            disableRipple
                            value={'professional'}
                            label={SubscriptionPlanModel.getPlanName('professional')}
                        />
                        <Tab disableRipple value={'custom'} label={SubscriptionPlanModel.getPlanName('custom')} />
                    </Tabs>
                    {activePlan?.price ? (
                        <Grid container mt={isDownLg ? 4 : 0}>
                            <Plan
                                planName={planName}
                                plan={activePlan.price}
                                period={period}
                                landings={landings}
                                previousPlanName={activePlan.previousPlanName}
                                recommended={SubscriptionPlanModel.isRecommended(planName)}
                                custom={SubscriptionPlanModel.isCustom(activePlan.price)}
                            />
                        </Grid>
                    ) : null}
                </>
            );
        }

        return list.map(({ planName, price, previousPlanName }) => {
            if (!price) {
                return null;
            }

            return (
                <Grid item xs={withCustom ? 3 : 4} key={planName} container>
                    <Plan
                        planName={planName}
                        plan={price}
                        period={period}
                        landings={landings}
                        previousPlanName={previousPlanName}
                        recommended={SubscriptionPlanModel.isRecommended(planName)}
                        custom={SubscriptionPlanModel.isCustom(price)}
                    />
                </Grid>
            );
        });
    }, [isDownLg, list, planName, handleTabChange, activePlan, period, landings, withCustom]);

    if (plans.isLoading) {
        return (
            <Grid container alignItems={'center'} justifyContent={'center'}>
                <CircularProgress />
            </Grid>
        );
    }

    return (
        <Root>
            <Grid container direction={isDownLg ? 'column' : 'row'} spacing={isDownLg ? 0 : 2.5}>
                {plans$}
            </Grid>
        </Root>
    );
}
