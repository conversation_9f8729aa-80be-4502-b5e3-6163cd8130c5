import CheckIcon from '@mui/icons-material/Check';
import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import { useSnackbar } from 'notistack';
import { useCallback, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { useLocation, useNavigate } from 'react-router-dom';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useLocationState } from '../hooks/useLocationState';
import { useUrlQueryParams } from '../hooks/useUrlQueryParams';

interface Props {
    planName: string;
    plan: SubscriptionPlanModel;
    period: PeriodType;
    previousPlanName?: string | null;
    recommended?: boolean;
    custom?: boolean;
    landings?: boolean;
}

const Root = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(4),
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    flex: '1 1 auto',
    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    '&.Plan-recommended': {
        boxShadow: '0 11px 20px rgba(5, 16, 55, 0.1)',
    },
    '.MuiListItem-root': {
        padding: theme.spacing(0.75, 0),
    },
    '.MuiListItemIcon-root': {
        minWidth: 34,
    },
    '.MuiButton-root': {
        fontSize: 14,
        fontWeight: 500,
    },
    '.Plan-recommendedBadge': {
        height: theme.spacing(4),
        lineHeight: theme.spacing(4),
        padding: theme.spacing(0, 2),
        display: 'inline-flex',
        backgroundColor: theme.palette.accent.main,
        color: theme.palette.accent.contrastText,
        borderRadius: 9,
        position: 'absolute',
        top: 0,
        left: '50%',
        transform: 'translate(-50%, -50%)',
        boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    },
}));

export function Plan({ planName, period, plan, previousPlanName, recommended, custom, landings }: Props) {
    const { t } = useTranslation('planSelect');
    const { enqueueSnackbar } = useSnackbar();
    const navigate = useNavigate();
    const location = useLocation();
    const from = useLocationState('from');
    const postSubscriptionUrl = useLocationState('postSubscriptionUrl');
    const action = useUrlQueryParams('action');
    const queryFrom = useUrlQueryParams('from');

    const href = useMemo(() => {
        if (custom) {
            return 'mailto:<EMAIL>?subject=Price%20offer&';
        }

        return `${window.location.protocol}//dashboard.${window.location.hostname}`;
    }, [custom]);

    const session = useMutation(
        'createCheckout/' + plan.priceId,
        () => {
            return paymentEndpoint.createCheckoutSession({
                priceId: plan.priceId,
                successUrl: postSubscriptionUrl || 'auto',
                cancelUrl: new URL(
                    `${window.location.pathname}?action=cancel&from=${encodeURIComponent(from ?? '')}`,
                    window.location.origin,
                ).toString(),
            });
        },
        {
            onSuccess: (data) => {
                if (data.sessionUrl) {
                    window.location.href = data.sessionUrl;
                } else {
                    window.location.href = window.location.origin;
                }
            },
            onError: (e) => {
                const message = getErrorMessage(e, 'errorCheckoutMessage');
                enqueueSnackbar(t(message), { variant: 'error' });
            },
        },
    );

    const handleChoose = useCallback(() => {
        if (!landings) {
            session.mutate();
            return;
        }
    }, [landings, session]);

    useEffect(() => {
        if (action === 'cancel') {
            if (queryFrom) {
                navigate(location.pathname, {
                    state: { from: queryFrom },
                });
            } else {
                navigate(location.pathname);
            }
        }
    }, [action, location, navigate, queryFrom]);

    return (
        <Root className={classNames({ 'Plan-recommended': recommended })}>
            {recommended ? (
                <Typography className={'Plan-recommendedBadge'} variant={'body2'} color={'primary'} fontWeight={600}>
                    {t('recommended')}
                </Typography>
            ) : null}

            <Typography variant={'h4'} fontWeight={700}>
                {SubscriptionPlanModel.getPlanName(planName, t)}
            </Typography>
            <Typography variant={'h5'} fontWeight={500} mb={2}>
                {SubscriptionPlanModel.getPlanPrice(plan, period)}
            </Typography>

            <List>
                {previousPlanName ? (
                    <ListItem>
                        <ListItemIcon>
                            <CheckIcon color={'inherit'} />
                        </ListItemIcon>
                        <ListItemText
                            primary={t('planBenefitsFrom', {
                                plan: SubscriptionPlanModel.getPlanName(previousPlanName, t),
                            })}
                        />
                    </ListItem>
                ) : null}

                {SubscriptionPlanModel.getPlanFeatures(planName, t).map((feature, index) => (
                    <ListItem key={index}>
                        <ListItemIcon>
                            <CheckIcon color={'accent'} />
                        </ListItemIcon>
                        <ListItemText primary={feature} />
                    </ListItem>
                ))}
            </List>

            <Grid container flexGrow={1} alignItems={'flex-end'} mt={3}>
                <LoadingButton
                    loading={session.isLoading}
                    variant={'contained'}
                    color={recommended ? 'accent' : 'inherit'}
                    onClick={handleChoose}
                    href={landings ? href : undefined}
                    fullWidth
                    disableElevation
                >
                    {custom
                        ? t('contactUs')
                        : t('choosePlan', { plan: SubscriptionPlanModel.getPlanName(planName, t) })}
                </LoadingButton>
            </Grid>
        </Root>
    );
}
