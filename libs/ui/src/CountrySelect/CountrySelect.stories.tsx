import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { action } from '@storybook/addon-actions';
import { Meta, Story } from '@storybook/react';
import { Form, Formik } from 'formik';
import { QueryClient, QueryClientProvider } from 'react-query';
import { DEFAULT_CALLING_CODE } from '@bookr-technologies/core/constants';
import { ThemeProvider } from '../ThemeProvider';
import { CountrySelect, CountrySelectProps, CountrySelectVariant } from './CountrySelect';
import { FormikCountrySelect } from './FormikCountrySelect';

export default {
    component: CountrySelect,
    title: 'CountrySelect',
} as Meta;

const queryClient = new QueryClient();

const Template: Story<CountrySelectProps> = ({ ...rest }) => (
    <ThemeProvider>
        <QueryClientProvider client={queryClient}>
            <CountrySelect {...rest} />
        </QueryClientProvider>
    </ThemeProvider>
);

export const SelectCountry = Template.bind({});
SelectCountry.args = {
    label: 'Select Country',
};

export const SelectCallingCode = Template.bind({});
SelectCallingCode.args = {
    label: 'Select Calling code',
    variant: CountrySelectVariant.CallingCode,
};

export const FlagSelector = Template.bind({});
FlagSelector.args = {
    variant: CountrySelectVariant.Language,
};

export const FlagSelectorWithDefaultValue = Template.bind({});
FlagSelectorWithDefaultValue.args = {
    value: 'Romania',
    variant: CountrySelectVariant.Language,
};

export const FormikSelector = () => {
    return (
        <ThemeProvider>
            <QueryClientProvider client={queryClient}>
                <Formik
                    initialValues={{
                        country: '',
                        callingCode: '',
                        defaultCallingCode: DEFAULT_CALLING_CODE,
                        defaultCountry: 'Romania',
                    }}
                    onSubmit={action('submit')}
                >
                    {({ values }) => (
                        <Form>
                            <Stack direction={'row'} flexWrap={'wrap'}>
                                <Grid container sx={{ my: 3 }} alignItems={'center'}>
                                    <Grid item xs={3}>
                                        <Typography>Country</Typography>
                                    </Grid>
                                    <Grid item xs={8}>
                                        <FormikCountrySelect name={'country'} label={'Select country'} />
                                    </Grid>
                                </Grid>

                                <Grid container sx={{ my: 3 }} alignItems={'center'}>
                                    <Grid item xs={3}>
                                        <Typography>Calling Code</Typography>
                                    </Grid>
                                    <Grid item xs={8}>
                                        <FormikCountrySelect
                                            name={'callingCode'}
                                            label={'Select country'}
                                            variant={CountrySelectVariant.CallingCode}
                                        />
                                    </Grid>
                                </Grid>

                                <Grid container sx={{ my: 3 }} alignItems={'center'}>
                                    <Grid item xs={3}>
                                        <Typography>Default Country</Typography>
                                    </Grid>
                                    <Grid item xs={8}>
                                        <FormikCountrySelect name={'defaultCountry'} label={'Select country'} />
                                    </Grid>
                                </Grid>

                                <Grid container sx={{ my: 3 }} alignItems={'center'}>
                                    <Grid item xs={3}>
                                        <Typography>Default Calling Code</Typography>
                                    </Grid>
                                    <Grid item xs={8}>
                                        <FormikCountrySelect
                                            name={'defaultCallingCode'}
                                            label={'Select country'}
                                            variant={CountrySelectVariant.CallingCode}
                                        />
                                    </Grid>
                                </Grid>
                            </Stack>
                            <pre>{JSON.stringify(values, null, 2)}</pre>
                        </Form>
                    )}
                </Formik>
            </QueryClientProvider>
        </ThemeProvider>
    );
};
