/* eslint-disable @typescript-eslint/no-explicit-any */
import Autocomplete, { AutocompleteProps, createFilterOptions } from '@mui/material/Autocomplete';
import Avatar from '@mui/material/Avatar';
import InputAdornment from '@mui/material/InputAdornment';
import Paper from '@mui/material/Paper';
import TextField from '@mui/material/TextField';
import { styled } from '@mui/material/styles';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useCountries } from '@bookr-technologies/api/hooks/useCountries';
import { RestCountryModel } from '@bookr-technologies/api/models/RestCountryModel';
import { AvailableLanguages, LanguageType } from '@bookr-technologies/i18n';
import { CustomPopper } from './CustomPopper';

export enum CountrySelectVariant {
    Country = 'country',
    CallingCode = 'callingCode',
    Language = 'language',
    CountryCode = 'countryCode',
}

type RootProps = Omit<AutocompleteProps<RestCountryModel, false, false, false>, 'options' | 'value'>;

export interface CountrySelectProps extends Omit<RootProps, 'renderInput'> {
    variant?: CountrySelectVariant;
    label?: string;
    name?: string;
    value?: string;
    error?: boolean;
    helperText?: string;
    renderInput?: RootProps['renderInput'];
}

const ListboxComponent = styled(Paper, { name: 'ListboxComponent', displayName: 'ListboxComponent' })({
    marginTop: 8,
    minWidth: '220px',
    borderRadius: 14,
    boxShadow: '0 11px 20px rgba(5, 16, 55, 0.1)',
    '.MuiAutocomplete-listbox': {
        padding: '2px 8px',
    },
    '.MuiAutocomplete-option': {
        padding: '8px 16px',
        margin: '8px 0',
        borderRadius: 14,
    },
});

const StyledInputAdornment = styled(InputAdornment)({
    margin: 7,
    '&, .MuiAvatar-root': {
        width: 34,
        height: 34,
    },
    '& + .MuiOutlinedInput-input': {
        paddingLeft: '9px !important',
    },
});

const StyledOption = styled('li')(({ theme }) => ({
    padding: theme.spacing(1),
    '& > img': {
        marginRight: theme.spacing(2),
        flexShrink: 0,
    },
}));

export function CountrySelect({
    label,
    value,
    error,
    helperText,
    onChange,
    renderInput,
    variant = CountrySelectVariant.Country,
    name = 'country',
    ...rest
}: CountrySelectProps) {
    const { isLoading, data } = useCountries();
    const [selectedValue, setSelectedValue] = useState<RestCountryModel | null>(null);

    const options = useMemo(() => {
        if (variant === CountrySelectVariant.Language) {
            return AvailableLanguages.map<RestCountryModel>((language: LanguageType, id) => ({
                id,
                name: language.name,
                code: language.code,
                flag: language.flag,
                callingCode: '',
                currencies: {},
                unicodeFlag: '',
                cca2: '',
            }));
        }

        return (data ?? []).filter(({ callingCode }) =>
            variant === CountrySelectVariant.CallingCode ? !!callingCode : true,
        );
    }, [data, variant]);

    const getValue = useCallback(
        (option: RestCountryModel) => {
            switch (variant) {
                case CountrySelectVariant.Country:
                    return option.name;
                case CountrySelectVariant.CallingCode:
                    return option.callingCode;
                case CountrySelectVariant.Language:
                    return option.code;
                case CountrySelectVariant.CountryCode:
                    return option.cca2;
            }
        },
        [variant],
    );

    const getLabel = useCallback(
        (option: RestCountryModel) => {
            switch (variant) {
                case CountrySelectVariant.CallingCode:
                    return option.callingCode;
                default:
                    return option.name;
            }
        },
        [variant],
    );

    const handleChange = useCallback(
        (event: any, val: any, reason: any, details: any) => {
            if (onChange) {
                event.target = {
                    ...event.target,
                    name: name ?? event.target.name,
                    value: (val ? getValue(val) : val) ?? '',
                };
                onChange(event, val, reason, details);
            }

            if (!value) {
                setSelectedValue(val);
            }
        },
        [getValue, name, onChange, value],
    );

    const filterFunction = useMemo(
        () =>
            createFilterOptions({
                stringify: (option: RestCountryModel) =>
                    (option.name ?? '') + ' ' + (option.callingCode ?? '') + ' ' + (option.code ?? ''),
            }),
        [],
    );

    useEffect(() => {
        setSelectedValue(
            options.find((item) => value && [item.code, item.name, item.callingCode, item.cca2].includes(value)) ??
                null,
        );
    }, [options, value]);

    return (
        <Autocomplete
            {...rest}
            autoHighlight
            disableListWrap
            PopperComponent={CustomPopper}
            value={selectedValue || null}
            loading={isLoading}
            onChange={handleChange}
            options={options}
            getOptionLabel={getLabel}
            isOptionEqualToValue={(option: RestCountryModel, value: RestCountryModel) => option.id === value?.id}
            filterOptions={filterFunction}
            PaperComponent={ListboxComponent}
            renderOption={(props, option: RestCountryModel) => (
                <StyledOption {...props} key={option.id}>
                    <img loading="lazy" width="24" src={option.flag} alt={option.name} />
                    <span>{option.name}</span>
                    &nbsp;
                    {variant === CountrySelectVariant.CallingCode && option.callingCode && (
                        <span>({option.callingCode})</span>
                    )}
                </StyledOption>
            )}
            renderInput={
                renderInput
                    ? renderInput
                    : (params) => (
                          <TextField
                              {...params}
                              error={error}
                              helperText={helperText}
                              variant={'outlined'}
                              label={label}
                              inputProps={{
                                  ...params.inputProps,
                                  autoComplete: 'new-password',
                              }}
                              InputProps={{
                                  ...params.InputProps,
                                  startAdornment:
                                      variant === CountrySelectVariant.Language && selectedValue ? (
                                          <StyledInputAdornment position="start">
                                              <Avatar src={selectedValue.flag} />
                                          </StyledInputAdornment>
                                      ) : null,
                              }}
                          />
                      )
            }
        />
    );
}
