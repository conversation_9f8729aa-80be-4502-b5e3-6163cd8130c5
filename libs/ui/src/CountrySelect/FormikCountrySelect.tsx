import { useFormikContext } from 'formik';
import { useCallback, FocusEvent } from 'react';
import { CountrySelect, CountrySelectProps } from './CountrySelect';

export function FormikCountrySelect({ onChange, onBlur, name = 'country', ...rest }: CountrySelectProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<string>(name);

    const handleBlur = useCallback(
        (event: FocusEvent<HTMLDivElement>) => {
            formik.handleBlur({
                ...event,
                target: {
                    ...event.target,
                    name,
                },
            });

            if (onBlur) {
                onBlur(event);
            }
        },
        [formik, name, onBlur],
    );

    const handleChange = useCallback(
        (event: any, value: any, reason: any, details: any) => {
            formik.handleChange(event);
            if (onChange) {
                onChange(event, value, reason, details);
            }
        },
        [formik, onChange],
    );

    return (
        <CountrySelect
            value={meta.value}
            onChange={handleChange}
            onBlur={handleBlur}
            name={name}
            error={meta.touched && !!meta.error}
            helperText={meta.touched ? meta.error : void 0}
            {...rest}
        />
    );
}
