import { useCallback, useState } from 'react';
import { ConfirmationDialog } from './ConfirmationDialog';
import { useConfirmationDialogStore } from './confirmationDialogStore';

export function GlobalConfirmationDialog() {
    const open = useConfirmationDialogStore((state) => state.open);
    const options = useConfirmationDialogStore((state) => state.options);
    const promise = useConfirmationDialogStore((state) => state.promise);
    const reset = useConfirmationDialogStore((state) => state.reset);
    const setOpen = useConfirmationDialogStore((state) => state.setOpen);

    const [, setPendingState] = useState(false);

    const handleClose = useCallback(() => {
        setPendingState(false);
        setOpen(false);
    }, [setOpen]);

    const handleSubmit = useCallback(() => {
        setPendingState(true);
        setOpen(false);
    }, [setOpen]);

    const handleExit = useCallback(() => {
        setPendingState((pendingState) => {
            promise?.resolve(pendingState);
            return false;
        });
        reset();
    }, [promise, reset]);

    return (
        <ConfirmationDialog
            open={open}
            onClose={handleClose}
            onSubmit={handleSubmit}
            TransitionProps={{ onExited: handleExit }}
            {...options}
        />
    );
}
