import { useCallback } from 'react';
import { Defer } from '@bookr-technologies/core/promises/defer';
import { ConfirmationDialogProps } from './ConfirmationDialog';
import { useConfirmationDialogStore } from './confirmationDialogStore';

export type ConfirmationCallback = (ask?: Partial<ConfirmationDialogProps>) => Promise<boolean>;

/**
 * @usage
 * ```
 * function MyComponent() {
 *      const confirm = useConfirmation();
 *
 *      const handleClick = useCallback(async () => {
 *          try {
 *              await confirm({
 *                  title: 'Are you sure?',
 *                  message: 'This action cannot be undone.',
 *              })
 *          } catch (e) {
 *              if (e instanceof ConfirmationDialogCancelled) {
 *                  return;
 *              }
 *
 *              // handle error
 *          } finally {
 *              // handle finally
 *          }
 *     }, []);
 *
 *     return ( ... );
 * }
 * ```
 */
export function useConfirmation(): ConfirmationCallback {
    const setOpen = useConfirmationDialogStore((state) => state.setOpen);
    const setPromise = useConfirmationDialogStore((state) => state.setPromise);
    const setOptions = useConfirmationDialogStore((state) => state.setOptions);

    return useCallback(
        async (options) => {
            const promise = new Defer<boolean>();
            if (options) {
                setOptions(options);
            }

            setPromise(promise);
            setOpen(true);
            return promise.wait();
        },
        [setOpen, setOptions, setPromise],
    );
}
