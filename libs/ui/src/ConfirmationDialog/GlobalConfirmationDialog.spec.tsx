import { render, unmountComponentAtNode } from 'react-dom';
import { act } from 'react-dom/test-utils';
import { initializeI18n } from '@bookr-technologies/i18n';
import { GlobalConfirmationDialog } from './GlobalConfirmationDialog';
import { ConfirmationCallback, useConfirmation } from './useConfirmation';

describe('ui@ConfirmationDialog/GlobalConfirmationDialog', () => {
    let container: HTMLDivElement | null = null;
    beforeEach(() => {
        // setup a DOM element as a render target
        container = document.createElement('div');
        document.body.appendChild(container);
    });

    afterEach(() => {
        if (container) {
            unmountComponentAtNode(container);
            container.remove();
            container = null;
        }
    });

    it('should cancel/confirm correctly', async () => {
        let confirm: ConfirmationCallback;

        act(() => {
            initializeI18n();

            function Wrapper() {
                confirm = useConfirmation();
                return <GlobalConfirmationDialog />;
            }

            render(<Wrapper />, container);
        });

        expect(container?.hasChildNodes()).toBeFalsy();

        // Test Confirmation action
        let confirmPromise: Promise<boolean> | null = null;
        act(() => {
            confirmPromise = confirm({ submitText: 'CONFIRM' });
        });

        const submitButton = document.body.querySelector<HTMLButtonElement>('[aria-label="CONFIRM"]');
        expect(submitButton).not.toBeNull();
        submitButton?.click();

        expect(confirmPromise).toBeInstanceOf(Promise);
        const resultConfirm = await confirmPromise;
        expect(resultConfirm).toBeTruthy();

        // Test cancel action
        act(() => {
            confirmPromise = confirm({
                cancelText: 'CANCEL',
            });
        });

        const cancelButton = document.body.querySelector<HTMLButtonElement>('[aria-label="CANCEL"]');
        expect(cancelButton).not.toBeNull();
        cancelButton?.click();

        expect(confirmPromise).toBeInstanceOf(Promise);
        const resultCancel = await confirmPromise;
        expect(resultCancel).toBeFalsy();
    });
});
