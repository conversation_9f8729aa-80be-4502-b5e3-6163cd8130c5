import { create } from 'zustand';
import { Defer } from '@bookr-technologies/core/promises';
import { ConfirmationDialogProps } from './ConfirmationDialog';

type DialogOptions = Omit<ConfirmationDialogProps, 'open' | 'onClose' | 'onSubmit'>;

interface ConfirmationDialogStore {
    open: boolean;
    options: DialogOptions;
    promise: Defer<boolean> | null;
    setOpen(open: boolean): void;
    setOptions(options: Partial<DialogOptions>): void;
    setPromise(promise: Defer<boolean> | null): void;
    reset(): void;
}

export const useConfirmationDialogStore = create<ConfirmationDialogStore>((set) => {
    return {
        open: false,
        promise: null,
        options: {
            title: '',
            message: '',
            isDestructiveAction: false,
        },
        setOpen(open: boolean) {
            return set({ open });
        },
        setOptions(options: Partial<DialogOptions>) {
            return set((state) => ({
                ...state,
                options: {
                    ...state.options,
                    ...options,
                },
            }));
        },
        setPromise(promise: Defer<boolean> | null) {
            return set({ promise });
        },
        reset() {
            set({
                promise: null,
                options: {
                    title: '',
                    message: '',
                    isDestructiveAction: false,
                },
            });
        },
    };
});
