import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

export interface ConfirmationDialogProps extends Omit<DialogProps, 'title'> {
    title: ReactNode;
    message: ReactNode;
    onSubmit: () => void;
    onClose: () => void;
    submitText?: string;
    cancelText?: string;
    cancelHide?: boolean;
    isDestructiveAction?: boolean;
}

const StyledDialog = styled(Dialog)`
    padding: 36px;

    .MuiDialog-paper {
        display: flex;
        justify-content: center;
        align-items: center;
        max-width: 580px;
        padding: 36px;
        background: #fff;
        box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
        border-radius: 16px;
    }

    .title {
        font-family: 'Plus Jakarta Display', Poppins, serif;
        font-weight: bold;
        font-size: 33px;
        line-height: 43px;
        text-align: center;
        letter-spacing: 0.0025em;
        color: #1f1f1f;
    }

    .message {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        text-align: center;
        letter-spacing: 0.005em;
        color: ${({ theme }) => theme.palette.grey[500]};
    }

    .actions {
        flex: 1 1 auto;
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .cancel-btn {
        box-shadow: none;
        margin-right: 28px;
        background-color: #eee;

        &:hover {
            background-color: #cbcbcb;
        }
        color: ${({ theme }) => theme.palette.primary.main};
    }

    .submit-btn {
        margin-left: 0;
    }
`;

export function ConfirmationDialog({
    open,
    title,
    message,
    onSubmit,
    submitText,
    onClose,
    isDestructiveAction,
    cancelText,
    cancelHide,
    ...rest
}: ConfirmationDialogProps) {
    const { t } = useTranslation('common');
    return (
        <StyledDialog open={open} onClose={onClose} {...rest}>
            <DialogTitle className={'title'}>{title}</DialogTitle>
            <DialogContent>
                <DialogContentText id="alert-dialog-description" className={'message'}>
                    {message}
                </DialogContentText>
            </DialogContent>
            <DialogActions className={'actions'}>
                {!cancelHide ? (
                    <Button
                        variant={'contained'}
                        onClick={onClose}
                        fullWidth
                        className={'cancel-btn'}
                        aria-label={cancelText || t('no')}
                        disableElevation
                    >
                        {cancelText || t('no')}
                    </Button>
                ) : null}
                <Button
                    variant={'contained'}
                    onClick={onSubmit}
                    color={isDestructiveAction ? 'error' : 'primary'}
                    fullWidth
                    className={'submit-btn'}
                    aria-label={submitText || t('yes')}
                    disableElevation
                >
                    {submitText || t('yes')}
                </Button>
            </DialogActions>
        </StyledDialog>
    );
}
