import Skeleton, { SkeletonProps } from '@mui/material/Skeleton';
import { ReactNode, useMemo } from 'react';

export interface LoadingPlaceholderProps extends Omit<SkeletonProps, 'children' | 'width' | 'height'> {
    loading: boolean;
    children?: ReactNode | (() => ReactNode);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    component?: any;
    color?: string;
    light?: boolean;
    dark?: boolean;

    width?: number | [min: number, max: number] | (() => number);
    height?: number | [min: number, max: number] | (() => number);
}

export function LoadingPlaceholder({
    loading,
    children,
    color,
    light,
    dark,
    sx,
    component: Component,
    width = [50, 140],
    height = 24,
    variant = 'text',
    ...rest
}: LoadingPlaceholderProps) {
    const [computedWidth, computedHeight] = useMemo(() => {
        function compute(value?: number | [min: number, max: number] | (() => number)): number {
            if (typeof value === 'function') {
                return value();
            }

            if (Array.isArray(value)) {
                return Math.floor(Math.random() * (value[1] - value[0] + 1)) + value[0];
            }

            return value || 0;
        }

        if (variant === 'circular') {
            return [compute(width), compute(width)];
        }

        return [compute(width), compute(height)];
    }, [height, variant, width]);

    const computedColor = useMemo(() => {
        if (color) {
            return color;
        }

        if (light) {
            return 'rgba(255, 255, 255, 0.4)';
        }

        return 'rgba(0, 0, 0, 0.1)';
    }, [color, light]);

    if (loading) {
        return (
            <Skeleton
                variant={variant}
                width={computedWidth}
                height={computedHeight}
                animation={'pulse'}
                sx={{ backgroundColor: computedColor, ...sx }}
                {...rest}
            />
        );
    }

    if (Component) {
        return <Component />;
    }

    // eslint-disable-next-line react/jsx-no-useless-fragment
    return <>{typeof children === 'function' ? children() : children ?? null}</>;
}
