import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { ReactNode } from 'react';
import { Switch, SwitchProps } from '../Switch';

interface SwitchCardProps extends Omit<SwitchProps, 'title'> {
    title: string;
    subtitle: string;
    icon: ReactNode;
}

const Root = styled(Paper)(({ theme }) => ({
    backgroundColor: '#fff',
    display: 'flex',
    marginBottom: theme.spacing(3),
}));

export function SwitchCard({ title, subtitle, icon, ...rest }: SwitchCardProps) {
    return (
        <Root variant={'outlined'}>
            {icon}
            <Grid px={2} lineHeight={1}>
                <Typography variant={'body1'} fontWeight={500}>
                    {title}
                </Typography>
                <Typography variant={'caption'} color={'textSecondary'} fontWeight={500} lineHeight={'12px'}>
                    {subtitle}
                </Typography>
            </Grid>
            <Switch {...rest} />
        </Root>
    );
}
