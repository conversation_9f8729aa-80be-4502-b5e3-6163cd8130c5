import PersonIcon from '@mui/icons-material/Person';
import Avatar from '@mui/material/Avatar';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { useTheme, styled } from '@mui/material/styles';
import { useMemo } from 'react';
import { DEFAULT_USER_PHOTO_URL } from '@bookr-technologies/api/constants/UserDefaults';

export interface UserProfileProps extends Omit<IconButtonProps, 'size'> {
    size?: number;
    src?: string;

    /**
     * The user name to display in the avatar.
     */
    alt?: string;

    disableElevation?: boolean;
}

const StyledIconButton = styled(IconButton)({
    padding: 0,
});

export function UserProfile({ src, alt, size, disableElevation, sx, onClick, ...rest }: UserProfileProps) {
    const theme = useTheme();

    const children = useMemo(() => {
        const seg = (alt || '').split(' ');
        const firstLetter = seg[0][0];
        const lastLetter = seg[1] ? seg[1][0] : seg[0][1];

        return `${firstLetter || ''}${lastLetter || ''}`.toUpperCase() || <PersonIcon />;
    }, [alt]);

    const sizeStyle = useMemo(
        () => ({
            width: size,
            height: size,
        }),
        [size],
    );

    const buttonStyle = useMemo(
        () => ({
            ...sizeStyle,
            ...(sx ?? {}),
            boxShadow: disableElevation ? 'none' : theme.shadows[2],
        }),
        [sizeStyle, disableElevation, theme.shadows, sx],
    );

    const avatarStyle = useMemo(
        () => ({
            ...sizeStyle,
            ...(size ? { fontSize: size * 0.45 } : {}),
            fontWeight: 500,
        }),
        [size, sizeStyle],
    );

    const avatar = (
        <Tooltip title={alt ?? ''}>
            <Avatar component={'span'} src={src && src !== DEFAULT_USER_PHOTO_URL ? src : undefined} sx={avatarStyle}>
                {children}
            </Avatar>
        </Tooltip>
    );

    if (!onClick) {
        return avatar;
    }

    return (
        <StyledIconButton sx={buttonStyle} onClick={onClick} {...rest}>
            {avatar}
        </StyledIconButton>
    );
}
