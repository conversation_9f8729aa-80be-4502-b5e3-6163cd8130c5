import { Story, Meta } from '@storybook/react';
import { UserProfile, UserProfileProps } from './UserProfile';

export default {
    component: UserProfile,
    title: 'UserProfile',
} as Meta;

const Template: Story<UserProfileProps> = (args) => <UserProfile {...args} />;

export const Default = Template.bind({});

export const WithAlt = Template.bind({});
WithAlt.args = {
    alt: '<PERSON>',
};

export const WithImage = Template.bind({});
WithImage.args = {
    src: 'https://placeimg.com/256/256/people',
};

export const WithImageAndNoShadow = Template.bind({});
WithImageAndNoShadow.args = {
    src: 'https://placeimg.com/256/256/people',
    disableElevation: true,
};
