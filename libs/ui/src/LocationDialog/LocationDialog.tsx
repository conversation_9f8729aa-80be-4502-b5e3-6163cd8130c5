/* eslint-disable @typescript-eslint/no-explicit-any */
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { Form, Formik } from 'formik';
import type { Coords } from 'google-map-react';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FormikButton, FormikLocationMap } from '../Fields';
import { FormikLocationName } from '../Fields/FormikLocationName';

export interface LocationDialogValues {
    name: string;
    location: Coords;
}

interface LocationDialogProps extends Omit<DialogProps, 'onSubmit'> {
    locationName: string;
    location: Coords;
    title: string;
    onSubmit(values: LocationDialogValues): void | Promise<void>;
}

const StyledGrid = styled(Grid)({
    minHeight: 644,
});

export function LocationDialog({ title, location, locationName, onClose, onSubmit, ...rest }: LocationDialogProps) {
    const { t } = useTranslation('locationDialog');
    const { t: common } = useTranslation('common');
    const initialValues = useMemo(
        () => ({
            location,
            locationName,
        }),
        [location, locationName],
    );

    const inputProps = useMemo(
        () => ({
            startAdornment: (
                <InputAdornment position={'start'}>
                    <SearchIcon />
                </InputAdornment>
            ),
        }),
        [],
    );

    const handleSubmit = useCallback(
        async (values: any) => {
            await onSubmit({
                name: values.locationName,
                location: values.location,
            });

            if (onClose) {
                (onClose as any)();
            }
        },
        [onClose, onSubmit],
    );

    return (
        <Dialog fullWidth maxWidth={'lg'} onClose={onClose} {...rest}>
            <Formik initialValues={initialValues} onSubmit={handleSubmit}>
                <Form>
                    <Grid container>
                        <Grid item xs={7}>
                            <FormikLocationMap name={'location'} />
                        </Grid>
                        <StyledGrid
                            container
                            item
                            xs={5}
                            direction={'column'}
                            alignItems={'flex-start'}
                            justifyContent={'flex-start'}
                            px={3.5}
                        >
                            <Grid container alignItems={'center'} justifyContent={'space-between'} py={4}>
                                <Typography variant={'body1'} fontWeight={600} color={'primary'}>
                                    {title}
                                </Typography>
                                <IconButton onClick={onClose as any}>
                                    <CloseIcon />
                                </IconButton>
                            </Grid>

                            <FormikLocationName
                                locationName={'location'}
                                name={'locationName'}
                                label={t('searchLocation')}
                                InputProps={inputProps}
                            />
                            <Grid container flexGrow={1} alignItems={'flex-end'} justifyContent={'flex-end'} pb={4}>
                                <FormikButton variant={'contained'} disableElevation>
                                    {common('save')}
                                </FormikButton>
                            </Grid>
                        </StyledGrid>
                    </Grid>
                </Form>
            </Formik>
        </Dialog>
    );
}
