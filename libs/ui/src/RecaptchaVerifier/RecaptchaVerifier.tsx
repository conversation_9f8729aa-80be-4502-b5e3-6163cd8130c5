import { RecaptchaVerifier as FirebaseRecaptchaVerifier } from 'firebase/auth';
import { MutableRefObject, useEffect, useMemo } from 'react';
import { FirebaseService } from '@bookr-technologies/core/services';

interface RecaptchaVerifierProps {
    name?: string;
    innerRef?: MutableRefObject<FirebaseRecaptchaVerifier | null>;
    inPlace?: boolean;
}

const recaptchaMap: Record<string, FirebaseRecaptchaVerifier> = {};

export function RecaptchaVerifier({ name = 'default', innerRef, inPlace, ...rest }: RecaptchaVerifierProps) {
    const id = useMemo(() => name + '-recaptcha-container', [name]);
    const elValue = useMemo(() => {
        let el = document.getElementById(id);
        if (!el) {
            el = document.createElement('div');
            el.id = id;
            el.style.display = 'none';
            document.body.appendChild(el);
        }

        return el;
    }, [id]);

    useEffect(() => {
        if (innerRef && !innerRef?.current) {
            if (!recaptchaMap[id]) {
                recaptchaMap[id] = new FirebaseRecaptchaVerifier(FirebaseService.auth(), elValue, {
                    size: 'invisible',
                    ...rest,
                });
            }

            innerRef.current = recaptchaMap[id];
        }
    }, [elValue, id, inPlace, innerRef, name, rest]);

    useEffect(
        () => () => {
            if (innerRef) {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore
                innerRef?.current?.reset();
                innerRef?.current?.clear();
                innerRef.current = null;
            }

            delete recaptchaMap[id];
            elValue.remove();
        },
        [elValue, id, innerRef],
    );

    return null;
}
