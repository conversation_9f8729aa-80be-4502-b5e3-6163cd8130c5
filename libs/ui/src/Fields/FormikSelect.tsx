/* eslint-disable @typescript-eslint/no-explicit-any */
import FormHelperText from '@mui/material/FormHelperText';
import Select, { SelectChangeEvent, SelectProps } from '@mui/material/Select';
import { useFormikContext } from 'formik';
import React, { useCallback } from 'react';

interface FormikSelectProps extends Omit<SelectProps, 'value'> {
    name: string;
}

export function FormikSelect({ children, onChange, error, defaultValue, ...rest }: FormikSelectProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<string>(rest.name);

    const handleChange = useCallback(
        (event: SelectChangeEvent<string>, child: any) => {
            formik.handleChange(event);
            if (onChange) {
                onChange(event, child);
            }
        },
        [formik, onChange],
    );

    return (
        <>
            <Select
                value={meta.value ?? defaultValue}
                onChange={handleChange as any}
                error={error || !!(meta.touched && meta.error)}
                defaultValue={defaultValue}
                {...rest}
            >
                {children}
            </Select>
            {meta.touched && meta.error && <FormHelperText error>{meta.error}</FormHelperText>}
        </>
    );
}

export default FormikSelect;
