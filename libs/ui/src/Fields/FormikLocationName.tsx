import LocationOn from '@mui/icons-material/LocationOn';
import CircularProgress from '@mui/material/CircularProgress';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { useFormikContext } from 'formik';
import { Coords } from 'google-map-react';
import debounce from 'lodash/debounce';
import { ChangeEvent, useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GeocodingResult } from '@bookr-technologies/api/models/GeocodingResult';
import { ZOOMED_VIEW } from '../LocationDialog';
import { googleMapsEndpoint } from '../endpoints/googleMapsEndpoint';
import { FormikTextField, FormikTextFieldProps } from './FormikTextField';

interface FormikLocationNameProps extends FormikTextFieldProps {
    locationName: string;
}

const StyledStack = styled(Stack)({
    backgroundColor: '#fff',
    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    borderRadius: 8,
    width: '100%',
    maxHeight: '400px',
    overflowY: 'auto',
    flexWrap: 'nowrap',
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const toJson = (value: any) => JSON.parse(JSON.stringify(value));

export function FormikLocationName({ name, locationName, ...rest }: FormikLocationNameProps) {
    const { t } = useTranslation('common');
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<string>(name);
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<GeocodingResult[]>([]);

    const handleFocus = useCallback(() => setOpen(true), []);
    const handleClose = useCallback(() => setOpen(false), []);

    const fetch = useMemo(
        () =>
            debounce(async (address: string) => {
                if (!address || address.length < 3) {
                    return;
                }

                setLoading(true);

                try {
                    const results = await googleMapsEndpoint.getPlacesByAddress(address);
                    setOptions(results);
                } catch (e) {
                    console.error(e);
                    setOptions([]);
                }

                setLoading(false);
            }, 300),
        [],
    );

    const handleChange = useCallback(
        ({ target }: ChangeEvent<HTMLInputElement>) => {
            // noinspection JSIgnoredPromiseFromCall
            fetch(target.value);
        },
        [fetch],
    );

    const handleSelect = (result: GeocodingResult) => () => {
        formik.setFieldValue(name, result.formatted_address);
        formik.setFieldValue(locationName, {
            lat: toJson(result.geometry.location).lat,
            lng: toJson(result.geometry.location).lng,
            zoom: ZOOMED_VIEW,
        });
        setOpen(false);
        setOptions([]);
    };

    return (
        <>
            <ClickAwayListener onClickAway={handleClose}>
                <Stack width={'100%'}>
                    <FormikTextField
                        name={name}
                        onFocus={handleFocus}
                        onChange={handleChange}
                        autoComplete={'off'}
                        {...rest}
                    />

                    {open && meta.value ? (
                        <StyledStack>
                            {loading ? (
                                <Grid container alignItems={'center'} justifyContent={'center'} p={3}>
                                    <CircularProgress />
                                </Grid>
                            ) : (
                                <List>
                                    {options.length === 0 ? (
                                        <ListItem>
                                            <ListItemText primary={t('noResults')} />
                                        </ListItem>
                                    ) : null}
                                    {options.map((result) => (
                                        <ListItem button key={result.place_id} onClick={handleSelect(result)}>
                                            <ListItemIcon>
                                                <LocationOn />
                                            </ListItemIcon>
                                            <ListItemText primary={result.formatted_address} />
                                        </ListItem>
                                    ))}
                                </List>
                            )}
                        </StyledStack>
                    ) : null}
                </Stack>
            </ClickAwayListener>

            <Lookup name={name} locationName={locationName} />
        </>
    );
}

function Lookup(props: Pick<FormikLocationNameProps, 'name' | 'locationName'>) {
    const formik = useFormikContext();
    const location = formik.getFieldMeta<Coords>(props.locationName);

    useEffect(
        () => {
            if (location.value) {
                googleMapsEndpoint.getPlacesByCoordinates(location.value).then((data) => {
                    if (data?.length > 0) {
                        formik.setFieldValue(props.name, data[0].formatted_address);
                    }
                });
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [location.value?.lat, location.value?.lng],
    );

    return null;
}
