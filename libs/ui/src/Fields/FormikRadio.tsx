/* eslint-disable @typescript-eslint/no-explicit-any */
import FormControlLabel, { FormControlLabelProps } from '@mui/material/FormControlLabel';
import Radio, { RadioProps } from '@mui/material/Radio';
import { useFormikContext } from 'formik';
import { useEvent } from '@bookr-technologies/hooks';

interface FormikRadioProps extends RadioProps {
    name: string;
    label?: string;
    value?: any;
    controlLabelProps?: FormControlLabelProps;
}

export function FormikRadio({ name, label, value, controlLabelProps, ...rest }: FormikRadioProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<any[] | boolean>(name);
    const isChecked = meta.value === value;

    const handleChange = useEvent(() =>
        formik.handleChange({
            target: {
                name,
                value,
            },
        }),
    );

    const checkbox = <Radio checked={isChecked} onChange={handleChange} name={name} {...rest} />;

    if (!label) {
        return checkbox;
    }

    return <FormControlLabel control={checkbox} label={label} {...controlLabelProps} />;
}
