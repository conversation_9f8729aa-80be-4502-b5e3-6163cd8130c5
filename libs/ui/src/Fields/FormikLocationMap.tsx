import { useFormikContext } from 'formik';
import { Coords, ChangeEventValue } from 'google-map-react';
import React, { useCallback } from 'react';
import { DEFAULT_ZOOM_VIEW } from '../LocationDialog';
import { LocationMap, LocationMapProps } from '../LocationMap';

interface FormikLocationMapProps extends Omit<LocationMapProps, 'center'> {
    name: string;
}

export function FormikLocationMap({ name, onChange, ...rest }: FormikLocationMapProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<Coords & { zoom: number }>(name);

    const handleChange = useCallback(
        (value: ChangeEventValue) => {
            formik.setFieldValue(name, {
                ...value.center,
                zoom: value.zoom || meta.value?.zoom,
            });

            if (onChange) {
                onChange(value);
            }
        },
        [formik, meta.value?.zoom, name, onChange],
    );

    return (
        <LocationMap
            center={meta.value}
            zoom={meta.value?.zoom || DEFAULT_ZOOM_VIEW}
            onChange={handleChange}
            {...rest}
        />
    );
}
