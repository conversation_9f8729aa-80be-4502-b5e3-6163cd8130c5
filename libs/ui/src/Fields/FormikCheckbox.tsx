/* eslint-disable @typescript-eslint/no-explicit-any */
import Checkbox, { CheckboxProps } from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import { useFormikContext } from 'formik';

interface FormikCheckboxProps extends CheckboxProps {
    name: string;
    label?: string;
    value?: any;
}

export function FormikCheckbox({ name, label, value, ...rest }: FormikCheckboxProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<any[] | boolean>(name);
    let values: any[] | boolean = false;
    let isChecked = false;

    if (Array.isArray(meta.value)) {
        values = meta.value || [];
        isChecked = values.includes(value);
    } else if (typeof meta.value === 'boolean') {
        values = meta.value;
        isChecked = meta.value;
    }

    const handleChange = () => {
        let newValues;

        if (Array.isArray(values)) {
            newValues = values;
            if (!isChecked) {
                newValues = [...newValues, value];
            } else {
                newValues = values.filter((v) => v !== value);
            }
        } else {
            newValues = !isChecked;
        }

        return formik.handleChange({
            target: {
                name,
                value: newValues,
            },
        });
    };

    const checkbox = <Checkbox checked={isChecked} onChange={handleChange} name={name} {...rest} />;

    if (!label) {
        return checkbox;
    }

    return <FormControlLabel control={checkbox} label={label} />;
}
