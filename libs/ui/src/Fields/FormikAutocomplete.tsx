/* eslint-disable @typescript-eslint/no-explicit-any */
import Autocomplete, { AutocompleteProps } from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { useFormikContext } from 'formik';
import { useCallback } from 'react';

type autocompleteProps<T> = AutocompleteProps<T, any, any, any>;

export interface FormikAutocompleteProps<T> extends Omit<autocompleteProps<T>, 'name' | 'value' | 'renderInput'> {
    name: string;
    label?: string;
    renderInput?: autocompleteProps<T>['renderInput'];
}

export function FormikAutocomplete<T>({
    name,
    label,
    onChange,
    onBlur,
    renderInput,
    ...rest
}: FormikAutocompleteProps<T>) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta<T>(name);

    const handleChange = useCallback(
        (event: any, value: any, ...args: any[]) => {
            formik.setFieldValue(name, null);
            (onChange as any)?.(event, value, ...args);
        },
        [formik, name, onChange],
    );

    const handleBlur = useCallback(
        (event: any) => {
            formik.handleBlur(event);
            onBlur?.(event);
        },
        [formik, onBlur],
    );

    return (
        <Autocomplete
            value={meta.value}
            onChange={handleChange}
            onBlur={handleBlur}
            renderInput={renderInput ?? ((params) => <TextField {...params} label={label} />)}
            {...rest}
        />
    );
}
