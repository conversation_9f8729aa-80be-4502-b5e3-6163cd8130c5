import TextField, { TextFieldProps } from '@mui/material/TextField';
import { useFormikContext } from 'formik';
import { useCallback } from 'react';

export interface FormikTextFieldProps extends Omit<TextFieldProps, 'name' | 'value'> {
    name: string;
}

export function FormikTextField({ onChange, onBlur, ...rest }: FormikTextFieldProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta(rest.name);

    const handleChange = useCallback(
        (event: any) => {
            formik.handleChange(event);
            if (onChange) {
                onChange(event);
            }
        },
        [formik, onChange],
    );

    const handleBlur = useCallback(
        (event: any) => {
            formik.handleBlur(event);
            if (onBlur) {
                onBlur(event);
            }
        },
        [formik, onBlur],
    );

    return (
        <TextField
            onChange={handleChange}
            onBlur={handleBlur}
            value={meta.value}
            error={meta.touched && !!meta.error}
            helperText={meta.touched ? meta.error : void 0}
            {...rest}
        />
    );
}
