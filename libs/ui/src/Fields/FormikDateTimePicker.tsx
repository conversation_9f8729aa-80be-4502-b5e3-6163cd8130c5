import TextField, { TextFieldProps } from '@mui/material/TextField';
import { useFormikContext } from 'formik';
import moment from 'moment';
import * as React from 'react';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

export interface FormikDateTimePickerProps extends Omit<TextFieldProps, 'name' | 'onChange' | 'value'> {
    name: string;
}

export function FormikDateTimePicker({ ...rest }: FormikDateTimePickerProps) {
    const formik = useFormikContext<any>();
    const { t } = useTranslation('common');
    const [value, setValue] = React.useState<string>(
        formik.initialValues[rest.name]
            ? moment(formik.initialValues[rest.name]).format('YYYY-MM-DDTHH:mm')
            : moment().format('YYYY-MM-DDTHH:mm'),
    );

    const handleChange = useCallback(
        (event: any) => {
            const momentValue = moment(event.target.value);

            if (momentValue.isValid()) {
                formik.setFieldValue(rest.name, momentValue.valueOf());
                setValue(momentValue.format('YYYY-MM-DDTHH:mm'));
            }
        },
        [formik, rest.name],
    );

    return (
        <TextField
            variant={'outlined'}
            id="datetime-local"
            label={t('selectDateAndTime')}
            type="datetime-local"
            color={'secondary'}
            sx={{ width: '100%' }}
            InputLabelProps={{
                shrink: true,
            }}
            onChange={handleChange}
            value={value}
            {...rest}
        />
    );
}
