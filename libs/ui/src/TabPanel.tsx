import Box from '@mui/material/Box';
import * as React from 'react';

interface TabPanelProps {
    children?: React.ReactNode;
    index: number;
    value: number;
}

export const TabPanel = ({ children, value, index, ...other }: TabPanelProps) => {
    if (value !== index) {
        return null;
    }
    return (
        <div role="tabpanel" hidden={value !== index} {...other} className={'tab'}>
            {value === index && <Box className={'box'}>{children}</Box>}
        </div>
    );
};
