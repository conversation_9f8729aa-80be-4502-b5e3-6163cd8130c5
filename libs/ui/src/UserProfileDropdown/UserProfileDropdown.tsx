import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import type { SxProps } from '@mui/system';
import React, { MouseEvent, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@bookr-technologies/store';
import { useBusinessStore } from '@bookr-technologies/store/businessStore';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';
import { UserProfile, UserProfileProps } from '../UserProfile';

// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface Props extends Omit<UserProfileProps, 'alt' | 'src'> {
    image?: string;
    fullName: string;
    phone: string;
}

const StyledMenu = styled(Menu)({
    '.MuiMenuItem-root': {
        fontSize: 14,
        margin: '2px 12px',
        fontWeight: 600,
        lineHeight: '20px',
        color: '#757575',
    },
});

export function UserProfileDropdown({ phone, fullName, image, ...rest }: Props) {
    const logout = useAuthStore((state) => state.logout);
    const { t } = useTranslation('userProfileDropdown');
    const navigate = useNavigate();
    const [anchorEl, setAnchorEl] = React.useState<HTMLButtonElement | null>(null);
    const open = Boolean(anchorEl);
    const user = useCurrentUser();
    const business = useBusinessStore((state) => state.business);

    const subject = encodeURIComponent(`Dashboard Help - ${business?.name || user?.displayName}`);
    const message = encodeURIComponent(
        `I need help with the dashboard! Please contact me!\n\n${user?.displayName || ''}\n\n--\n#ID: ${
            business?.id || user?.uid
        }`,
    );

    const handleClick = useCallback(
        (event: MouseEvent<HTMLButtonElement>) => setAnchorEl(event.currentTarget),
        [setAnchorEl],
    );

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const closer = (func: (...args: any[]) => any) => () => {
        func();
        setAnchorEl(null);
    };

    const handleClose = (route: string) => {
        setAnchorEl(null);
        navigate(route);
    };

    const handleHelp = useCallback(() => {
        window.open(`mailto:<EMAIL>?subject=${subject}&body=${message}`);
    }, [message, subject]);

    return (
        <>
            <UserProfile onClick={handleClick} src={image} alt={fullName} {...rest} />
            <StyledMenu
                anchorEl={anchorEl}
                open={open}
                onClose={() => setAnchorEl(null)}
                onClick={() => setAnchorEl(null)}
                PaperProps={{
                    elevation: 0,
                    sx: styles.paper,
                }}
                transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
            >
                <MenuItem sx={styles.profileInfo} disableRipple>
                    <UserProfile size={50} src={image} alt={fullName} disableElevation />
                    <Box pl={2.5}>
                        <Typography variant={'subtitle1'} color={'primary'} sx={styles.fullName}>
                            {fullName}
                        </Typography>
                        <Typography variant={'caption'} color={'textSecondary'} sx={styles.phone}>
                            {phone}
                        </Typography>
                    </Box>
                </MenuItem>
                <Divider />
                <MenuItem onClick={() => handleClose('/settings/profile')}>{t('yourProfile')}</MenuItem>
                <MenuItem onClick={() => handleClose('/settings/application')}>{t('settings')}</MenuItem>
                <MenuItem onClick={handleHelp}>{t('help')}</MenuItem>
                <Divider />
                <MenuItem onClick={closer(logout)}>{t('logout')}</MenuItem>
            </StyledMenu>
        </>
    );
}

const styles: Record<string, SxProps> = {
    menuItem: {},
    phone: {
        fontSize: '12px',
        fontWeight: '500',
        lineHeight: '20px',
    },
    fullName: {
        fontSize: '16px',
        fontWeight: '600',
        lineHeight: '24px',
    },
    profileInfo: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'flex-start',
        padding: '16px 20px !important',
        margin: '0 !important',
        borderRadius: '14px 14px 0 0 !important',
        '&& + .MuiDivider-root': {
            mt: 0,
        },
    },
    paper: {
        overflow: 'visible',
        filter: 'drop-shadow(0px 2px 6px rgba(5, 16, 55, 0.1))',
        mt: 1.5,
        width: 300,
        '&& .MuiDivider-root': {
            my: 2,
        },
        '&& .MuiMenu-list': {
            pt: 0,
            pb: 2,
        },
        '&:before': {
            content: '""',
            display: 'block',
            position: 'absolute',
            top: 0,
            right: 20,
            width: 10,
            height: 10,
            bgcolor: 'background.paper',
            transform: 'translateY(-50%) rotate(45deg)',
            zIndex: 0,
        },
    },
};
