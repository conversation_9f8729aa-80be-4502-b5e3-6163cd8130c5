import { useMemo } from 'react';
import { useLocation } from 'react-router-dom';

export function useUrlQueryParams(): URLSearchParams;
export function useUrlQueryParams(action: string): string | null;

export function useUrlQueryParams(action?: string) {
    const { search } = useLocation();

    return useMemo(() => {
        const params = new URLSearchParams(search);
        if (action) {
            return params.get(action);
        }

        return params;
    }, [search, action]);
}
