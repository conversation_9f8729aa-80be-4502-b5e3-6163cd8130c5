import { useMemo } from 'react';
import { useIsMountedRef } from './useIsMountedRef';

export function useIsMounted(): {
    isMounted: boolean;
    only(callback: () => void): void;
} {
    const isMounted = useIsMountedRef();

    return useMemo(
        () => ({
            isMounted: isMounted.current,
            only(callback: () => void): void {
                if (isMounted.current) {
                    callback();
                }
            },
        }),
        [isMounted],
    );
}
