import { DragEvent, HTMLAttributes, RefObject, useCallback, useRef, useState } from 'react';

interface Options {
    onDrop?: (data: { files: File[] }) => void;
}

export function useDragAndDrop<T extends HTMLElement = HTMLElement>(
    options: Options = {},
): [
    HTMLAttributes<T> & {
        ref: RefObject<T>;
    },
    {
        isDragging: boolean;
        files: File[];
    },
] {
    const [files, setFiles] = useState<File[]>([]);
    const [isDragging, setIsDragging] = useState(false);
    const ref = useRef<T>(null);

    const onDragOver = useCallback((e: DragEvent<T>) => {
        e.preventDefault();
        setIsDragging(true);
    }, []);

    const onDragLeave = useCallback((e: DragEvent<T>) => {
        e.preventDefault();
        setIsDragging(false);
    }, []);

    const onDrop = useCallback(
        (e: DragEvent<T>) => {
            e.preventDefault();
            const selectedFiles = Array.from(e?.dataTransfer?.files ?? []);
            setFiles(selectedFiles);
            setIsDragging(false);

            if (options?.onDrop) {
                options?.onDrop({
                    files: selectedFiles,
                });
            }
        },
        [options],
    );

    return [
        {
            ref,
            onDragOver,
            onDragLeave,
            onDrop,
        },
        { isDragging, files },
    ];
}
