import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON>allback, useEffect, useMemo, useRef, useState } from 'react';

interface FilePickerOptions {
    multiple?: boolean;
    accept?: string;
    onClick?: MouseEventHandler;
    onChange?: (files: File[]) => void;
}

let previewsCache: Record<string, string> = {};

export function useFilePicker(options: FilePickerOptions = {}) {
    const inputRef = useRef<HTMLInputElement | null>(null);
    const [files, setFiles] = useState<File[]>([]);
    const [file, setFile] = useState<File | null>(null);

    const opts = useMemo(
        () => options,
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [],
    );

    const handleClick = useCallback(
        (event: MouseEvent) => {
            event.preventDefault();

            if (opts.onClick) {
                opts.onClick(event);
            }

            inputRef.current?.click();
        },
        [opts],
    );

    const handleChange = useCallback(
        (event: Event) => {
            const files = Array.from((event.target as HTMLInputElement).files ?? []);
            setFile(files[0]);
            setFiles(files);
            opts.onChange?.(files);

            if (event.target) {
                // eslint-disable-next-line
                (event.target as any).value = null;
            }
        },
        [opts],
    );

    const generatePreview = (file: File) => {
        const hash = file.name + file.size + file.type;
        if (!previewsCache[hash]) {
            previewsCache[hash] = URL.createObjectURL(file);
        }

        return previewsCache[hash];
    };

    const invalidatePreview = useCallback((file: File | string | null) => {
        const hash = file instanceof File ? file.name + file.size + file.type : '';

        Object.entries(previewsCache).forEach(([key, value]) => {
            if (value === file || key === hash) {
                URL.revokeObjectURL(previewsCache[key]);
                delete previewsCache[key];
            }
        });
    }, []);

    const clear = useCallback(() => {
        setFile(null);
        setFiles([]);
    }, []);

    useEffect(() => {
        const input = document.createElement('input');
        input.type = 'file';
        inputRef.current = input;
        return () => {
            inputRef.current?.remove();
            inputRef.current = null;

            Object.values(previewsCache).forEach((url) => URL.revokeObjectURL(url));
            previewsCache = {};
        };
    }, []);

    useEffect(() => {
        const input = inputRef.current;
        if (!input) {
            return;
        }

        input.onchange = handleChange;
        input.accept = opts?.accept ?? '';
        input.multiple = opts?.multiple ?? false;
    }, [handleChange, opts?.accept, opts?.multiple]);

    return {
        clear,
        file,
        files,
        generatePreview,
        handleClick,
        invalidatePreview,
    };
}
