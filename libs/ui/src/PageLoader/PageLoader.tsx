import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import { PropsWithChildren } from 'react';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function PageLoader({ children, ...rest }: PropsWithChildren<any>) {
    return (
        <Grid
            container
            width={'100%'}
            padding={4}
            alignItems={'center'}
            justifyContent={'center'}
            flexDirection={'column'}
            {...rest}
        >
            <CircularProgress color={'secondary'} size={24} />
            {children}
        </Grid>
    );
}

export default PageLoader;
