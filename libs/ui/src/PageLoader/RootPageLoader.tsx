import { styled } from '@mui/material/styles';
import { PropsWithChildren } from 'react';
import logo from '../assets/darkBookrLogo.svg';

const Root = styled('div')`
    display: flex;
    width: 100%;
    padding: 32px;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1 1 auto;
    .RootPageLoader-image {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% {
            opacity: 0.5;
        }

        50% {
            opacity: 1;
        }

        100% {
            opacity: 0.5;
        }
    }
`;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function RootPageLoader({ children }: PropsWithChildren<any>) {
    return (
        <Root>
            <img className={'RootPageLoader-image'} src={logo} alt="Bookr" />
            {children}
        </Root>
    );
}

export default RootPageLoader;
