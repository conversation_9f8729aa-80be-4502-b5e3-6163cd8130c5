import { styled } from '@mui/material/styles';
import CookieConsent from 'react-cookie-consent';
import { useTranslation } from 'react-i18next';

const StyledCookieConsentBannerContainer = styled('div')`
    .cookieConsentBanner {
        font-size: 18px;
        font-weight: 600;
        font-family: 'Poppins', 'React', sans-serif;
        color: #111 !important;
        background-color: #fff !important;
        padding-left: 30px;
        padding-right: 30px;
    }

    .acceptButton {
        font-size: 14px;
        font-weight: 500;
        font-family: 'Poppins', 'React', sans-serif;
        width: 160px;
        height: 40px;
        border-radius: 10px !important;
        color: #111 !important;
        background-color: #eee !important;
    }

    @media (min-width: 600px) and (max-width: 820px) {
        .cookieConsentBanner {
            justify-content: center !important;
        }

        .cookieConsentBanner > div {
            flex: none !important;
            margin-bottom: 0 !important;
        }
    }

    @media (max-width: 600px) {
        .cookieConsentBanner {
            justify-content: center !important;
        }
    }
`;

export function CookieBanner() {
    const { t } = useTranslation('cookieBanner');

    return (
        <StyledCookieConsentBannerContainer>
            <CookieConsent
                containerClasses={'cookieConsentBanner'}
                buttonClasses={'acceptButton'}
                buttonText={t('acceptButton')}
            >
                {t('title')}
            </CookieConsent>
        </StyledCookieConsentBannerContainer>
    );
}
