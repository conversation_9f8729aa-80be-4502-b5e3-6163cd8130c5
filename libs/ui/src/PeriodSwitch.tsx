import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup, { ToggleButtonGroupProps } from '@mui/material/ToggleButtonGroup';
import { styled } from '@mui/material/styles';
import { useCallback, useEffect, useState } from 'react';
import { MouseEvent } from 'react';
import { useTranslation } from 'react-i18next';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';

interface Props extends Omit<ToggleButtonGroupProps, 'value' | 'onChange'> {
    value?: PeriodType;
    onChange?: (period: PeriodType) => void;
}

const Root = styled(ToggleButtonGroup)(({ theme }) => ({
    backgroundColor: '#eee',
    borderRadius: 16,
    padding: theme.spacing(1),
    '.MuiToggleButton-root': {
        padding: theme.spacing(1),
        border: 'none',
        borderRadius: '9px !important',
        height: 40,
        minWidth: 142,
        textTransform: 'capitalize',
        marginRight: theme.spacing(1),
        fontSize: 16,
        fontWeight: 500,
        color: '#111',

        '&:last-child': {
            marginRight: 0,
        },
        '&.Mui-selected': {
            backgroundColor: '#fff',
            boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
        },
    },
}));

export function PeriodSwitch({ value, onChange }: Props) {
    const { t } = useTranslation('periodSwitch');
    const [period, setPeriod] = useState(value || PeriodType.Monthly);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const handleChangeValue = useCallback(
        (event: MouseEvent<HTMLElement>, value: PeriodType) => {
            if (value) {
                setPeriod(value);
                if (onChange) {
                    onChange(value);
                }
            }
        },
        [onChange],
    );

    useEffect(() => {
        setPeriod(value || PeriodType.Yearly);
    }, [value]);

    return (
        <Root value={period} exclusive onChange={handleChangeValue} aria-label="text alignment">
            <ToggleButton value={PeriodType.Monthly} aria-label="Lunar">
                {t('monthly')}
            </ToggleButton>
            <ToggleButton value={PeriodType.Yearly} aria-label="Anual">
                {t('yearly')}
            </ToggleButton>
        </Root>
    );
}
