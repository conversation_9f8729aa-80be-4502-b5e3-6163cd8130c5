import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { UserModel } from '@bookr-technologies/api/models/UserModel';

interface Props {
    clients?: Array<UserModel>;
}

const StyledButton = styled(Button)({
    backgroundColor: 'rgba(47, 128, 251, 0.1)',
    fontWeight: 500,
    color: '#2F80FB',
    borderRadius: '10px',
    alignSelf: 'end',
    '&:hover': {
        color: '#fff',
    },
});

const StyledListItem = styled(ListItem)({
    marginBottom: '20px',
    borderRadius: '10px',
    textDecoration: 'none',
    '&:hover': {
        backgroundColor: '#F6F6F6',
        cursor: 'pointer',
    },
});

const StyledLink = styled(Link)({
    textDecoration: 'none',
    color: '#111111',
});

export function UsersProfilesList({ clients }: Props) {
    const { t } = useTranslation('common');

    const usersElements = clients?.map((client, key) => (
        <StyledLink to={`/clients/${client.uid}`} key={client.uid}>
            <StyledListItem alignItems="center">
                <ListItemAvatar>
                    <Avatar alt={client.displayName} src={client.photoURL} />
                </ListItemAvatar>
                <ListItemText primary={client.displayName} primaryTypographyProps={{ fontWeight: 600 }} />
                <StyledButton variant="contained" size="small" disableElevation>
                    {t('profile')}
                </StyledButton>
            </StyledListItem>
        </StyledLink>
    ));

    return <List sx={{ width: '100%' }}>{usersElements}</List>;
}
