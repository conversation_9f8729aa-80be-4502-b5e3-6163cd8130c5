import StarIcon from '@mui/icons-material/Star';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { SubscriptionModel } from '@bookr-technologies/api/models/SubscriptionModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useAuthStore } from '@bookr-technologies/store';
import { useUser } from '@bookr-technologies/store/hooks/useUser';

interface PrivilegeBasedAccessProps {
    privileges: UserPrivilegeType | UserPrivilegeType[];
}

const Root = styled('div')(({ theme }) => ({
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
    backdropFilter: 'blur(8px) saturate(180%)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    '.MuiTypography-root': {
        color: '#fff',
    },
    '.PrivilegeBasedAccess-boxed': {
        display: 'inline-flex',
        backgroundColor: theme.palette.accent.main,
        color: theme.palette.accent.contrastText,
        borderRadius: theme.spacing(1.5),
        padding: theme.spacing(1, 2),
        fontWeight: 500,
        cursor: 'pointer',
    },
}));

export function PrivilegeBasedAccess({ privileges }: PrivilegeBasedAccessProps) {
    const { t } = useTranslation('privilegeBasedAccess');
    const rootRef = useRef<HTMLDivElement>(null);
    const user = useUser();
    const navigate = useNavigate();
    const subscription = useAuthStore((state) => state.subscription);
    const userPrivileges = useMemo(() => (Array.isArray(privileges) ? privileges : [privileges]), [privileges]);
    const [{ canView }, setActive] = useState(() => ({ canView: canUserView(user, subscription, userPrivileges) }));

    useEffect(() => {
        const parent = rootRef.current?.parentElement;
        if (parent) {
            parent.style.position ||= 'relative';
            parent.style.overflow ||= 'hidden';
        }
    }, []);

    useEffect(() => {
        const interval = setInterval(() => {
            setActive({ canView: false });
            const canView = canUserView(user, subscription, userPrivileges);
            setActive({ canView });

            if (!canView) {
                clearInterval(interval);
            }
        }, 1000);

        return () => clearInterval(interval);
    }, [user, subscription, userPrivileges]);

    if (canView) {
        return null;
    }

    return (
        <Root ref={rootRef}>
            <div
                className={'PrivilegeBasedAccess-boxed'}
                onClick={() => navigate('/subscription/plans/select', { replace: true })}
            >
                <Typography variant={'body1'} fontWeight={500} mr={1}>
                    <Trans
                        t={t}
                        i18nKey={'requiredPrivileges'}
                        count={userPrivileges.length}
                        components={{ b: <b /> }}
                        values={{
                            privileges: userPrivileges.map((privilege) => t(privilege)).join(', '),
                        }}
                    />
                </Typography>
                <StarIcon opacity={0.7} />
            </div>
        </Root>
    );
}

function canUserView(user: UserModel, subscription: SubscriptionModel | null, userPrivileges: UserPrivilegeType[]) {
    return (user && UserModel.hasPrivileges(user, ...userPrivileges)) || SubscriptionModel.isTrial(subscription);
}
