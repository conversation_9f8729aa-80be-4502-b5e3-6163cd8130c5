{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ui/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/ui/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/ui"], "options": {"jestConfig": "libs/ui/jest.config.ts", "passWithNoTests": true}}, "storybook": {"executor": "@nrwl/storybook:storybook", "options": {"uiFramework": "@storybook/react", "port": 4400, "config": {"configFolder": "libs/ui/.storybook"}}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@nrwl/storybook:build", "outputs": ["{options.outputPath}"], "options": {"uiFramework": "@storybook/react", "outputPath": "dist/storybook/ui", "config": {"configFolder": "libs/ui/.storybook"}}, "configurations": {"ci": {"quiet": true}}}}}