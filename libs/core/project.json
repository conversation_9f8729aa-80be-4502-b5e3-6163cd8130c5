{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/core/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nrwl/web:rollup", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/core", "tsConfig": "libs/core/tsconfig.lib.json", "project": "libs/core/package.json", "entryFile": "libs/core/src", "external": ["react/jsx-runtime"], "rollupConfig": "tools/rollup/rollup.lib.js", "format": ["esm", "cjs"], "compiler": "swc", "assets": [{"glob": "libs/core/README.md", "input": ".", "output": "."}]}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/core/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/core"], "options": {"jestConfig": "libs/core/jest.config.ts", "passWithNoTests": true}}}}