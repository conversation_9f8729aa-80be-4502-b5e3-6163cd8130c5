import { getAnalytics } from 'firebase/analytics';
import { FirebaseApp, getApp, initializeApp } from 'firebase/app';
import { connectAuthEmulator, getAuth } from 'firebase/auth';
import { env } from '@bookr-technologies/env';

export class FirebaseService {
    static get app(): FirebaseApp {
        return getApp();
    }

    static init() {
        let exist = false;
        try {
            if (this.app) {
                exist = true;
            }
        } catch (e) {
            exist = false;
        } finally {
            if (!exist) {
                initializeApp(env('firebase'));
            }

            if (env('auth.emulator.enabled')) {
                connectAuthEmulator(this.auth(), 'http://localhost:9099');
            }
        }
    }

    static auth() {
        return getAuth(this.app);
    }

    static analytics() {
        return getAnalytics(this.app);
    }
}
