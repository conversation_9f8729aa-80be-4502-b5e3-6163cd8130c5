/* eslint-disable @typescript-eslint/no-explicit-any */
export class Defer<T = any> {
    private readonly __promise: Promise<T>;
    // noinspection JSMismatchedCollectionQueryUpdate
    private __resolve: (((value?: PromiseLike<T> | T) => void) | undefined)[] = [];
    // noinspection JSMismatchedCollectionQueryUpdate
    private __reject: (((reason?: any) => void) | undefined)[] = [];

    constructor(resolve$?: (value?: T | PromiseLike<T>) => void, reject$?: (reason?: any) => void) {
        this.__promise = new Promise<T>((resolve, reject) => {
            this.__reject = [reject$, reject];
            this.__resolve = [resolve$, resolve as any];
        });

        this.resolve = this.resolve.bind(this);
        this.reject = this.reject.bind(this);
    }

    public wait(): Promise<T> {
        return this.__promise;
    }

    /**
     * Resolve promise
     * @param value
     */
    public resolve(value?: T | PromiseLike<T>): void {
        this.__resolve.forEach((fn) => {
            if (fn) {
                fn(value);
            }
        });
    }

    /**
     * Reject promise
     * @param reason
     */
    public reject(reason?: any): void {
        this.__reject.forEach((fn) => {
            if (fn) {
                fn(reason);
            }
        });
    }

    /**
     * Resolve the defer and wait right away
     * @param value
     */
    public resolveAndWait(value?: T | PromiseLike<T>): Promise<T> {
        this.resolve(value);
        return this.wait();
    }
}
