/* eslint-disable @typescript-eslint/no-explicit-any */
interface CsvParseOptions {
    columns?: string[];
    noHeader?: boolean;
}

export function parseCsv<T = Record<string, any>>(data: string, options?: CsvParseOptions): T[] {
    const rows = data.split('\n');
    let firstRow = '';

    if (!options?.noHeader) {
        firstRow = rows.shift() ?? '';
    }

    const cols = options?.columns ? options?.columns : firstRow.split(',').map((col: string) => col.trim());

    return rows
        .map((row) => {
            if (row.length === 0) {
                return null;
            }

            const values = row.split(',').map((value) => value.trim());
            return cols.reduce((acc, col, index) => ({ ...acc, [col]: (values[index] ?? '').trim() }), {} as T);
        })
        .filter(Boolean) as T[];
}
