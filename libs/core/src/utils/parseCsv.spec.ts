import { parseCsv } from './parseCsv';

describe('core@utils/parseCsv', () => {
    it('should parse csv correctly', () => {
        const data = `Display Name, Phone Number
John <PERSON>, +40 722 123 456`;

        expect(parseCsv(data)).toMatchObject([
            {
                'Display Name': '<PERSON>',
                'Phone Number': '+40 722 123 456',
            },
        ]);

        expect(parseCsv(data, { columns: ['displayName', 'phoneNumber'] })).toMatchObject([
            {
                displayName: '<PERSON>',
                phoneNumber: '+40 722 123 456',
            },
        ]);

        expect(parseCsv(data, { noHeader: true, columns: ['displayName', 'phoneNumber'] })).toMatchObject([
            {
                displayName: 'Display Name',
                phoneNumber: 'Phone Number',
            },
            {
                displayName: '<PERSON> Do<PERSON>',
                phoneNumber: '+40 722 123 456',
            },
        ]);
    });
});
