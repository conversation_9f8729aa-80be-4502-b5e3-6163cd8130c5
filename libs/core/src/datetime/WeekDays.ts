export enum WeekDaysEnum {
    ALL = 'all',
    MONDAY = 'monday',
    TUESDAY = 'tuesday',
    WEDNESDAY = 'wednesday',
    THURSDAY = 'thursday',
    FRIDAY = 'friday',
    SATURDAY = 'saturday',
    SUNDAY = 'sunday',
}

export const WeekDays = [
    WeekDaysEnum.SUNDAY,
    WeekDaysEnum.MONDAY,
    WeekDaysEnum.TUESDAY,
    WeekDaysEnum.WEDNESDAY,
    WeekDaysEnum.THURSDAY,
    WeekDaysEnum.FRIDAY,
    WeekDaysEnum.SATURDAY,
    WeekDaysEnum.ALL,
];

export function todayWeekDay(): WeekDaysEnum {
    const date = new Date();
    const day = date.getDay();

    return WeekDays[day];
}

export function getWeekDayIndex(day: string): number {
    return WeekDays.indexOf(day.toLowerCase() as WeekDaysEnum);
}
