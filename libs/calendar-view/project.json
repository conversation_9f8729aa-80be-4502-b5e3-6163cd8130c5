{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/calendar-view/src", "projectType": "library", "tags": [], "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/calendar-view/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/calendar-view"], "options": {"jestConfig": "libs/calendar-view/jest.config.ts", "passWithNoTests": true}}}}