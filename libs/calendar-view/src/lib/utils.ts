/* eslint-disable @typescript-eslint/no-explicit-any */
import { CalendarEvent } from './calendar-event';

type EventMatrix = CalendarEvent[][];

function collision(a: CalendarEvent, b: CalendarEvent): boolean {
    return a.end > b.start && a.start < b.end;
}

function expand(ev: CalendarEvent, column: number, columns: EventMatrix): number {
    let colSpan = 1;

    for (const row of columns) {
        for (const event of row) {
            if (collision(ev, event)) {
                return colSpan;
            }
        }

        colSpan++;
    }

    return colSpan;
}

function pack(columns: any, width: any, calculatedEvents: any): void {
    const colLength = columns.length;

    for (let i = 0; i < colLength; i++) {
        const col = columns[i];
        for (let j = 0; j < col.length; j++) {
            const colSpan = expand(col[j], i, columns);
            const L = (i / colLength) * width;
            const W = (width * colSpan) / colLength + 4;

            calculatedEvents.push(col[j].calculateBounds());
        }
    }
}

export function populateEvents(events: CalendarEvent[], screenWidth: number): CalendarEvent[] {
    let lastEnd: any;
    let columns: any;
    const calculatedEvents: any = [];

    events = events
        .map((ev, index) => ({ ...ev, index: index }))
        .sort(function (a, b) {
            if (a.start < b.start) return -1;
            if (a.start > b.start) return 1;
            if (a.end < b.end) return -1;
            if (a.end > b.end) return 1;
            return 0;
        });

    columns = [];
    lastEnd = null;

    events.forEach((ev: any) => {
        if (lastEnd !== null && ev.start >= lastEnd) {
            pack(columns, screenWidth, calculatedEvents);
            columns = [];
            lastEnd = null;
        }

        let placed = false;
        for (let i = 0; i < columns.length; i++) {
            const col = columns[i];
            if (!collision(col[col.length - 1], ev)) {
                col.push(ev);
                placed = true;
                break;
            }
        }

        if (!placed) {
            columns.push([ev]);
        }

        if (lastEnd === null || ev.end > lastEnd) {
            lastEnd = ev.end;
        }
    });

    if (columns.length > 0) {
        pack(columns, screenWidth, calculatedEvents);
    }

    return calculatedEvents;
}

export function twoDigits(num: number): string {
    if (num < 10) {
        return '0' + num;
    }

    return String(num);
}
