import { CalculateBoundsOptions, CalendarEvent, CalendarEventImpl } from './calendar-event';

export class CalendarEventList {
    private events: CalendarEventImpl[] = [];
    private groups: Map<string, Set<CalendarEventImpl>> = new Map();

    constructor(events: CalendarEvent[], options: CalculateBoundsOptions) {
        this.events = this.computeBounds(
            events.map((value) => CalendarEventImpl.normalize(value)),
            options,
        );

        this.mapEventsToResources();
    }

    public get size(): number {
        return this.events.length;
    }

    public getEventsByResource(resourceId: string): Set<CalendarEventImpl> {
        return this.groups.get(resourceId) ?? new Set();
    }

    private mapEventsToResources() {
        this.events.forEach((event) => {
            const group = this.groups.get(event.resourceId) ?? new Set();
            this.groups.set(event.resourceId, group.add(event));
        });
    }

    private computeBounds(events: CalendarEventImpl[], options: CalculateBoundsOptions) {
        return events.map((event) => event.calculateBounds(options));
    }
}
