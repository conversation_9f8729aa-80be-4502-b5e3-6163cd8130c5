import ButtonBase from '@mui/material/ButtonBase';
import TextField from '@mui/material/TextField';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useState } from 'react';
import { useEvent, useModal } from '@bookr-technologies/hooks';

export function DateSelector() {
    const modal = useModal();
    const [value, setValue] = useState<Date>(new Date());

    const handleChange = useEvent(() => {
        //
    });

    return (
        <LocalizationProvider dateAdapter={AdapterMoment}>
            <DatePicker
                {...modal.props}
                value={value}
                onChange={handleChange}
                renderInput={(params) => (
                    <ButtonBase component={'div'} onClick={modal.open} sx={{ mr: 2 }}>
                        <TextField {...params} />
                    </ButtonBase>
                )}
            />
        </LocalizationProvider>
    );
}
