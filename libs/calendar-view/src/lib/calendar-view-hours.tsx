import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import { range } from 'lodash';
import moment from 'moment';
import { ReactNode, useMemo } from 'react';
import { skipProp } from '@bookr-technologies/core/utils';
import { useCalendarView } from './calendar-view-context';
import { useWeekDays } from './use-week-days';

export function CalendarViewHours({ children }: { children?: ReactNode }) {
    const { columnWidth, props, cellHeight } = useCalendarView();
    const { resources = [], date = new Date() } = props;
    const hours = useHours();
    const weekDays = useWeekDays(date);
    const rowContent = weekDays.map(([weekDay]) => (
        <div className="CalendarViewHours-resourceGroup" key={weekDay}>
            {resources.map((resource) => (
                <div className="CalendarViewHours-resourceCell" key={resource.id} />
            ))}
        </div>
    ));

    return (
        <Root cellHeight={cellHeight} columnWidth={columnWidth}>
            <div className="CalendarViewHours-timeline">
                <div className="CalendarViewHours-cell" />
                {hours.map((hour, index) => (
                    <div className="CalendarViewHours-cell" key={`${hour}_${index}`}>
                        <Typography variant="body1" fontWeight={600} mt={-1.5}>
                            {hour}
                        </Typography>
                    </div>
                ))}
                <div className="CalendarViewHours-cell" />
            </div>
            <div className="CalendarViewHours-grid">
                <div className="CalendarViewHours-row bordered">{rowContent}</div>

                {hours.map((hour, index) => (
                    <div
                        className={classNames('CalendarViewHours-row', { bordered: index % 2 === 1 })}
                        key={`${hour}_${index}`}
                    >
                        {rowContent}
                    </div>
                ))}
            </div>
            {children}
        </Root>
    );
}

const useHours = () =>
    useMemo(() => {
        const start = moment().startOf('day');

        return range(48).map((i) =>
            start
                .clone()
                .add(i * 30, 'minutes')
                .format('LT'),
        );
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [moment.locale()]);

const Root = styled('aside', {
    shouldForwardProp: skipProp('cellHeight', 'columnWidth'),
})<{ cellHeight: number; columnWidth: number }>(({ theme, cellHeight, columnWidth }) => ({
    display: 'flex',
    flexDirection: 'row',
    zIndex: 20,
    width: 'max-content',
    minWidth: '100%',
    position: 'relative',
    '&, & .CalendarViewHours-timeline': {
        backgroundColor: '#f6f6f6',
    },
    '& .CalendarViewHours-timeline': {
        display: 'flex',
        flexDirection: 'column',
        width: 128,
        borderRight: `1px solid #e2e2e2`,
        position: 'sticky',
        left: 0,
        zIndex: 22,
    },
    '& .CalendarViewHours-cell': {
        display: 'flex',
        alignItems: 'flex-start',
        justifyContent: 'center',
        height: cellHeight / 2,
        '&:nth-child(2n)': {},
    },
    '& .CalendarViewHours-row': {
        display: 'flex',
        flexDirection: 'row',
    },
    '& .CalendarViewHours-resourceGroup': {
        display: 'flex',
        flexDirection: 'row',
    },
    '& .CalendarViewHours-resourceCell': {
        height: cellHeight / 2,
        width: columnWidth,
        borderRight: `1px solid #e2e2e2`,
    },
    '& .CalendarViewHours-row.bordered': {
        '& .CalendarViewHours-resourceCell': {
            borderBottom: `1px solid #e2e2e2`,
        },
    },
}));
