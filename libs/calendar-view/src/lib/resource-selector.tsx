import GroupsIcon from '@mui/icons-material/Groups';
import Grid from '@mui/material/Grid';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';

export function ResourceSelector() {
    const { t } = useTranslation('common');

    return (
        <Select
            multiple
            labelId={'calendar-view-resource-select'}
            id={'calendar-view-resource-select'}
            variant={'filled'}
            label={t('allStaffMembers')}
            placeholder={t('allStaffMembers')}
            value={[]}
            sx={{ mr: 2 }}
        >
            <MenuItem value={'all'}>
                <Grid container alignItems={'center'}>
                    <GroupsIcon />
                    <Typography>{t('allStaffMembers')}</Typography>
                </Grid>
            </MenuItem>
        </Select>
    );
}
