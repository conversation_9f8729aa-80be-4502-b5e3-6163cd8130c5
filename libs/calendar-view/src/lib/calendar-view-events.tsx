import { styled } from '@mui/material/styles';
import { skipProp } from '@bookr-technologies/core/utils';
import { useCalendarView } from './calendar-view-context';
import { CalendarViewEvent } from './calendar-view-event';
import { useWeekDays } from './use-week-days';

export function CalendarViewEvents() {
    const { columnWidth, props, cellHeight, list } = useCalendarView();
    const { resources = [], date = new Date() } = props;
    const weekDays = useWeekDays(date);
    const resourcesWidth = Math.max(1, resources.length) * columnWidth;

    return (
        <Root cellHeight={cellHeight} columnWidth={columnWidth} resourcesWidth={resourcesWidth}>
            {weekDays.map(([weekDay, date, isoDate]) => (
                <section className="CalendarViewEvents-resourceGroup" key={weekDay}>
                    {resources.map((resource) => (
                        <div className="CalendarViewEvents-resourceColumn" key={resource.id}>
                            {Array.from(list.getEventsByResource(resource.id))
                                .filter((event) => event.occurOn(isoDate))
                                .map((event) => (
                                    <CalendarViewEvent key={event.resourceId + '-' + event.start} event={event} />
                                ))}
                        </div>
                    ))}
                </section>
            ))}
        </Root>
    );
}

const Root = styled('aside', {
    shouldForwardProp: skipProp('cellHeight', 'columnWidth', 'resourcesWidth'),
})<{ cellHeight: number; columnWidth: number; resourcesWidth: number }>(
    ({ theme, cellHeight, columnWidth, resourcesWidth }) => ({
        display: 'flex',
        position: 'absolute',
        top: 0,
        zIndex: 20,
        left: 128,
        '.CalendarViewEvents-resourceGroup': {
            display: 'flex',
            width: resourcesWidth,
        },
        '.CalendarViewEvents-resourceColumn': {
            position: 'relative',
            display: 'flex',
            width: columnWidth,
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'column',
        },
    }),
);
