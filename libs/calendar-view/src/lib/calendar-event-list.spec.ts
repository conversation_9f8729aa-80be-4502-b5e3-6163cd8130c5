import { CalendarEventList } from './calendar-event-list';

describe('calendar-view#calendar-event-list', () => {
    it('should correctly create an instance', () => {
        expect(() => {
            const list = new CalendarEventList([], {
                height: 100,
                topPadding: 0,
            });
            expect(list).toBeDefined();

            expect(list.size).toEqual(0);
        }).not.toThrow();
    });

    it('should correctly get all events of a resource', () => {
        const list = new CalendarEventList(
            [
                {
                    resourceId: 'unique-1',
                    color: 'red',
                    end: '2021-01-01T12:00:00.000Z',
                    start: '2021-01-01T10:00:00.000Z',
                    summary: 'summary',
                    title: 'title',
                },
                {
                    resourceId: 'unique-2',
                    color: 'red',
                    end: '2021-01-01T12:00:00.000Z',
                    start: '2021-01-01T10:00:00.000Z',
                    summary: 'summary',
                    title: 'title',
                },
                {
                    resourceId: 'unique-3',
                    color: 'red',
                    end: '2021-01-01T12:00:00.000Z',
                    start: '2021-01-01T10:00:00.000Z',
                    summary: 'summary',
                    title: 'title',
                },
                {
                    resourceId: 'unique-3',
                    color: 'red',
                    end: '2021-01-01T12:00:00.000Z',
                    start: '2021-01-01T10:00:00.000Z',
                    summary: 'summary',
                    title: 'title',
                },
                {
                    resourceId: 'unique-2',
                    color: 'red',
                    end: '2021-01-01T12:00:00.000Z',
                    start: '2021-01-01T10:00:00.000Z',
                    summary: 'summary',
                    title: 'title',
                },
                {
                    resourceId: 'unique-3',
                    color: 'red',
                    end: '2021-01-01T12:00:00.000Z',
                    start: '2021-01-01T10:00:00.000Z',
                    summary: 'summary',
                    title: 'title',
                },
            ],
            {
                height: 100,
                topPadding: 0,
            },
        );

        expect(list.size).toEqual(6);
        expect(list.getEventsByResource('unique-1').size).toEqual(1);
        expect(list.getEventsByResource('unique-2').size).toEqual(2);
        expect(list.getEventsByResource('unique-3').size).toEqual(3);
        expect(list.getEventsByResource('unique-4').size).toEqual(0);
    });
});
