import FullscreenIcon from '@mui/icons-material/Fullscreen';
import PrintIcon from '@mui/icons-material/Print';
import ViewDayIcon from '@mui/icons-material/ViewDay';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';

export function Controls() {
    return (
        <>
            <StyledButton>
                <PrintIcon />
            </StyledButton>
            <StyledButton>
                <ViewDayIcon />
            </StyledButton>
            <StyledButton>
                <FullscreenIcon />
            </StyledButton>
        </>
    );
}

const StyledButton = styled(IconButton)(({ theme }) => ({
    marginRight: theme.spacing(2),
    width: 40,
    height: 40,
    borderRadius: 10,
    border: '1px solid #e2e2e2',
    backgroundColor: '#f6f6f6',
    '&:last-child': {
        marginRight: 0,
    },
}));
