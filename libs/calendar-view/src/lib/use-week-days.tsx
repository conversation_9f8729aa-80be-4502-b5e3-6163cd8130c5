import { range } from 'lodash';
import moment from 'moment/moment';
import { DateLike } from '@bookr-technologies/core/datetime';

export function useWeekDays(start: DateLike) {
    const startDate = moment(start).startOf('week');
    return range(7).map((i) => {
        const day = startDate.clone().add(i, 'days');
        return [day.format('dddd'), day.format('DD MMM'), day.toISOString()];
    });
}
