import { styled } from '@mui/material/styles';
import { CalendarViewActions } from './calendar-view-actions';
import { CalendarViewControls } from './calendar-view-controls';

export function CalendarViewHeader() {
    return (
        <Root>
            <div className="CalendarViewHeader-controlsHolder">
                <CalendarViewControls />
            </div>
            <div className="CalendarViewHeader-actionsHolder">
                <CalendarViewActions />
            </div>
        </Root>
    );
}

const Root = styled('header')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'nowrap',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    padding: theme.spacing(3.5, 3),
    '& .CalendarViewHeader-controlsHolder': {
        display: 'flex',
        alignItems: 'center',
    },
    '& .CalendarViewHeader-actionsHolder': {},
}));
