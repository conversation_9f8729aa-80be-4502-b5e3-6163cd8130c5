import { styled } from '@mui/material/styles';
import { useMemo, useRef, useState } from 'react';
import { useWindowListener } from '@bookr-technologies/hooks';
import { CalendarEventList } from './calendar-event-list';
import { CalendarViewContextType, CalendarViewProvider } from './calendar-view-context';
import { CalendarViewGrid } from './calendar-view-grid';
import { CalendarViewHeader } from './calendar-view-header';
import { CalendarViewProps } from './types';

export function CalendarView(props: CalendarViewProps) {
    const [columnWidth, setColumnWidth] = useState(150);
    const [cellHeight, setCellHeight] = useState(100);
    const rootRef = useRef<HTMLDivElement>(null);
    const state = useMemo<CalendarViewContextType>(
        () => ({
            props,
            list: new CalendarEventList(props.events ?? [], {
                topPadding: cellHeight / 2,
                height: cellHeight,
            }),
            cellHeight,
            columnWidth,
        }),
        [columnWidth, cellHeight, props],
    );

    useWindowListener('resize', () => {
        if (props.resources && props.resources?.length <= 1) {
            setColumnWidth(Math.max(150, (rootRef.current?.clientWidth || 0) / 7));
        }
    });

    return (
        <CalendarViewProvider value={state}>
            <Root ref={rootRef}>
                <CalendarViewHeader />
                <CalendarViewGrid />
            </Root>
        </CalendarViewProvider>
    );
}

const Root = styled('div')(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    backgroundColor: theme.palette.background.default,
    maxWidth: '100%',
}));
