/* eslint-disable @typescript-eslint/no-explicit-any */
import moment from 'moment/moment';
import { DateLike } from '@bookr-technologies/core/datetime';

class CalendarEventBounds {
    constructor(private width: number, private height: number, private left: number, private top: number) {}

    public getTop() {
        return this.top;
    }

    public getHeight() {
        return this.height;
    }
}

export interface CalendarEvent {
    resourceId: string;
    end: string | Date;
    start: string | Date;
    summary: string;
    title: string;
    color?: string;
    data?: Record<string, any>;
}

export interface CalculateBoundsOptions {
    height: number;
    topPadding?: number;
}

export class CalendarEventImpl implements CalendarEvent {
    private bounds: CalendarEventBounds;

    constructor(
        public end: string | Date,
        public resourceId: string,
        public start: string | Date,
        public summary: string,
        public title: string,
        public color?: string,
        public data?: Record<string, any>,
    ) {
        this.bounds = new CalendarEventBounds(0, 0, 0, 0);
    }

    public static normalize(instance: CalendarEvent | CalendarEventImpl) {
        if (instance instanceof CalendarEventImpl) {
            return instance;
        }

        return new CalendarEventImpl(
            instance.end,
            instance.resourceId,
            instance.start,
            instance.summary,
            instance.title,
            instance.color,
            instance.data,
        );
    }

    public calculateBounds({ height, topPadding }: CalculateBoundsOptions) {
        const startTime = moment(this.start);
        const endTime = this.end ? moment(this.end) : startTime.clone().add(1, 'hour');
        const dayStartTime = startTime.clone().startOf('day');
        const diffHours = startTime.diff(dayStartTime, 'hours', true);

        this.bounds = new CalendarEventBounds(
            0,
            endTime.diff(startTime, 'hours', true) * height,
            0,
            diffHours * height + (topPadding ?? 0),
        );

        return this;
    }

    public getTop() {
        return this.bounds.getTop();
    }

    public getHeight() {
        return this.bounds.getHeight();
    }

    public occurOn(date: DateLike) {
        const occurrenceDate = moment(date).format('YYYY-MM-DD');
        const start = moment(this.start);
        const end = this.end ? moment(this.end) : start.clone().add(1, 'hour');

        return occurrenceDate === start.format('YYYY-MM-DD') || occurrenceDate === end.format('YYYY-MM-DD');
    }
}
