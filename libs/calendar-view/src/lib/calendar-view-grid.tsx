import { styled } from '@mui/material/styles';
import { CalendarViewEvents } from './calendar-view-events';
import { CalendarViewHours } from './calendar-view-hours';
import { CalendarViewResources } from './calendar-view-resources';

export function CalendarViewGrid() {
    return (
        <Root>
            <CalendarViewResources />
            <CalendarViewHours>
                <CalendarViewEvents />
            </CalendarViewHours>
        </Root>
    );
}

const Root = styled('main')(({ theme }) => ({
    width: '100%',
    maxHeight: '100%',
    overflow: 'auto',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
}));
