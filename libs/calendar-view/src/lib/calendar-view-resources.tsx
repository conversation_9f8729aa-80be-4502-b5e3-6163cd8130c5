import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { UserProfile } from '@bookr-technologies/ui/UserProfile';
import { useCalendarView } from './calendar-view-context';
import { useWeekDays } from './use-week-days';

export function CalendarViewResources() {
    const { columnWidth, props } = useCalendarView();
    const { resources = [], date = new Date() } = props;
    const resourcesWidth = Math.max(1, resources.length) * columnWidth;
    const weekDays = useWeekDays(date);

    return (
        <Root resourcesWidth={resourcesWidth} columnWidth={columnWidth}>
            <div className="CalendarViewResources-row CalendarViewResources-dates">
                <div className="CalendarViewResources-separator" />
                {weekDays.map(([weekDay, date]) => (
                    <div key={weekDay} className="CalendarViewResources-date CalendarViewResources-column">
                        <Typography
                            variant="subtitle1"
                            fontWeight={500}
                            align={'center'}
                            position={'sticky'}
                            left={140}
                        >
                            <b>{weekDay}</b>,&nbsp;{date}
                        </Typography>
                    </div>
                ))}
            </div>
            <div className="CalendarViewResources-row CalendarViewResources-resources">
                <div className="CalendarViewResources-separator" />
                {weekDays.map(([weekDay]) => (
                    <div key={weekDay} className="CalendarViewResources-resourceDay CalendarViewResources-column">
                        {resources.map((resource) => (
                            <div key={resource.id} className="CalendarViewResources-resource">
                                <UserProfile src={resource.photoURL} alt={resource.displayName} />
                            </div>
                        ))}
                    </div>
                ))}
            </div>
        </Root>
    );
}

const Root = styled('header')<{ resourcesWidth: number; columnWidth: number }>(
    ({ theme, resourcesWidth, columnWidth }) => ({
        backgroundColor: '#fff',
        display: 'flex',
        flexDirection: 'column',
        position: 'sticky',
        top: 0,
        width: 'max-content',
        borderBottom: '1px solid #e2e2e2',
        zIndex: 30,
        '& .CalendarViewResources-row': {
            display: 'flex',
            minHeight: 54,
            borderTop: '1px solid #e2e2e2',
        },
        '& .CalendarViewResources-separator': {
            minWidth: 128,
            maxWidth: 128,
            height: 54,
            position: 'sticky',
            left: 0,
            backgroundColor: '#fff',
            zIndex: 10,
            borderRight: '1px solid #e2e2e2',
        },
        '& .CalendarViewResources-column': {
            borderRight: '1px solid #e2e2e2',
            maxWidth: resourcesWidth,
            minWidth: resourcesWidth,
            padding: '12px 6px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
        },
        '& .CalendarViewResources-resource': {
            maxWidth: columnWidth,
            minWidth: columnWidth,
            height: 54,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            borderLeft: '1px solid #e2e2e2',
            '&:first-child': {
                borderLeft: 'none',
            },
        },
        '& .CalendarViewResources-resourceDay': {
            display: 'flex',
            alignItems: 'center',
            padding: 0,
            borderTop: 'none',
        },
    }),
);
