import { createContext, useContext } from 'react';
import { CalendarEventList } from './calendar-event-list';
import { CalendarViewProps } from './types';

export interface CalendarViewContextType {
    cellHeight: number;
    columnWidth: number;
    list: CalendarEventList;
    props: CalendarViewProps;
}

const CalendarViewContext = createContext<CalendarViewContextType>({
    cellHeight: 100,
    columnWidth: 150,
    list: new CalendarEventList([], {
        topPadding: 0,
        height: 100,
    }),
    props: {
        resources: [],
        events: [],
    },
});

export const CalendarViewProvider = CalendarViewContext.Provider;
export const useCalendarView = () => useContext(CalendarViewContext);
