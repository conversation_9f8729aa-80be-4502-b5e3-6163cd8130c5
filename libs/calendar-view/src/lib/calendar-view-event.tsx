import CloseIcon from '@mui/icons-material/Close';
import Box from '@mui/material/Box';
import ButtonBase from '@mui/material/ButtonBase';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Popover from '@mui/material/Popover';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { readableColor } from 'polished';
import { useMemo } from 'react';
import { formatDate } from '@bookr-technologies/core/datetime';
import { useEvent, useModal } from '@bookr-technologies/hooks';
import { CalendarEventImpl } from './calendar-event';

export function CalendarViewEvent({ event }: { event: CalendarEventImpl }) {
    const color = event.color ?? '#2f80fb';
    const textColor = useMemo(() => readableColor(color), [color]);
    const height = event.getHeight();
    const time = `${formatDate(event.start, 'LT')} - ${formatDate(event.end, 'LT')}`;

    const popover = useModal();

    const sharedTypographyProps = {
        fontSize: 10,
        fontWeight: 600,
        lineHeight: '11px',
        textAlign: 'left' as const,
        color: textColor,
    };

    const handleOpen = useEvent((event) => popover.openWithContext(event.currentTarget));

    return (
        <>
            <Root
                backgroundColor={color}
                top={event.getTop()}
                height={height}
                title={`${event.title} - ${event.summary}, ${time}`}
                onClick={handleOpen}
            >
                <div className="CalendarViewEvent-text">
                    <Typography {...sharedTypographyProps}>{event.title}</Typography>
                    {height >= 50 ? <Typography {...sharedTypographyProps}>{event.summary}</Typography> : null}
                </div>
                <Typography mt={height >= 60 ? 1 : 0.5} {...sharedTypographyProps}>
                    {time}
                </Typography>
            </Root>
            <Popover
                anchorEl={popover.context}
                anchorOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                }}
                PaperProps={{ sx: { width: 320 } }}
                {...popover.props}
            >
                <Grid
                    container
                    direction={'row'}
                    py={2}
                    px={1.5}
                    flexWrap={'nowrap'}
                    alignItems={'flex-start'}
                    justifyContent={'space-between'}
                >
                    <Box>
                        <Typography variant="subtitle1" fontWeight="700">
                            Barbering
                        </Typography>
                        <Typography variant="subtitle1" fontWeight="700">
                            {formatDate(new Date(), 'DD MMM, LT')}
                        </Typography>
                    </Box>
                    <IconButton>
                        <CloseIcon />
                    </IconButton>
                </Grid>
            </Popover>
        </>
    );
}

const Root = styled(ButtonBase)<{
    backgroundColor: string;
    height: number;
    top: number;
}>(({ theme, backgroundColor, height, top }) => ({
    backgroundColor,
    top,
    height,
    margin: 2,
    minHeight: 40,
    position: 'absolute',
    left: 0,
    width: 'calc(100% - 5px)',
    padding: '6px 8px',
    borderRadius: 8,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
}));
