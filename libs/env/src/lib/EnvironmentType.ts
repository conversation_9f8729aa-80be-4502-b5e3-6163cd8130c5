interface FirebaseConfig {
    apiKey: string;
    authDomain: string;
    projectId: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
    databaseURL: string;
    storageBucket: string;
}

interface ApplicationConfig {
    env: 'production' | 'staging' | 'local';
    pixelId: string;
}

interface OAuthGoogleConfig {
    webClientId: string;
}

interface OAuthConfig {
    google: OAuthGoogleConfig;
}

interface AuthConfig {
    emulator: {
        enabled: boolean;
    };
}

export interface EnvironmentType {
    app: ApplicationConfig;
    auth: AuthConfig;
    apiUrl: string;
    firebase: FirebaseConfig;
    oauth: OAuthConfig;
}
