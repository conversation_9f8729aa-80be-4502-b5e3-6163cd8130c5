/* eslint-disable @typescript-eslint/no-explicit-any */
import { get } from 'lodash';
import { EnvironmentType } from './EnvironmentType';
import { getAdapter } from './adapters';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type Prev = [never, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, ...0[]];
type Join<K, P> = K extends string | number
    ? P extends string | number
        ? `${K}${'' extends P ? '' : '.'}${P}`
        : never
    : never;

type Paths<T, D extends number = 10> = [D] extends [never]
    ? never
    : T extends object
    ? {
          [K in keyof T]-?: K extends string | number ? `${K}` | Join<K, Paths<T[K], Prev[D]>> : never;
      }[keyof T]
    : '';

type Value<T extends Record<any, any>, K extends string> = K extends `${infer P1}.${infer P2}`
    ? T[P1][P2]
    : K extends `${infer P1}`
    ? T[P1]
    : never;

/**
 * Get environment variable, or default value if not set.
 * This function Prioritizes over the config file.
 * @example So the check looks like this:
 * ```
 * const value = env('firebase.projectId', 'defaultValue');
 * // The function will try to access the config object path.
 * // For key `firebase.projectId` the flow looks like:
 * // config.firebase.projectId >> defaultValue
 * ```
 *
 * @param key - Environment variable name.
 * @param defaultValue - Default value.
 */
export function env<T extends EnvironmentType, K extends Paths<T>, V extends Value<T, K>>(key: K, defaultValue?: V): V {
    const adapter = getAdapter();
    const config = adapter.get();

    return get(config, key, defaultValue) as V;
}
