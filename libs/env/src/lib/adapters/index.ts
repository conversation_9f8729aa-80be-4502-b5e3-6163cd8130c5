import { Adapter } from './Adapter';

export { LocalConfigurationAdapter } from './LocalConfigurationAdapter';
export { ExternalConfigurationAdapter } from './ExternalConfigurationAdapter';

let globalAdapter: Adapter;

export function setAdapter(adapter: Adapter) {
    globalAdapter = adapter;
}

export function getAdapter() {
    if (!globalAdapter) {
        throw new Error('No adapter has been set!');
    }

    return globalAdapter;
}
