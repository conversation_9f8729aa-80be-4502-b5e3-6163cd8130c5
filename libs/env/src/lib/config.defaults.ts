import { EnvironmentType } from './EnvironmentType';

export default {
    // apiUrl: 'http://localhost:8080',
    apiUrl: 'https://staging-api.bookr.ro',
    app: {
        env: 'local',
        pixelId: '',
    },
    auth: {
        emulator: {
            enabled: false,
        },
    },
    firebase: {
        apiKey: 'AIzaSyABnTzuA_UCrj3poKssKiX9oGF7sJMFEiI',
        authDomain: 'bookr-api.firebaseapp.com',
        projectId: 'bookr-api',
        messagingSenderId: '393065803535',
        appId: '1:393065803535:web:3e8203d5c227a79a03187a',
        measurementId: 'G-0K6BPRYR67',
        databaseURL: 'https://bookr-api.firebaseio.com',
        storageBucket: 'bookr-api.appspot.com',
    },
    oauth: {
        google: {
            webClientId: '393065803535-b7vkp1o2h1i0g3eq7c1bgrf8nm72h9ql.apps.googleusercontent.com',
        },
    },
} as EnvironmentType;
