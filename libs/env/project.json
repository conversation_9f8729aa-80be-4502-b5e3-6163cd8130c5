{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/env/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nrwl/web:rollup", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/env", "tsConfig": "libs/env/tsconfig.lib.json", "project": "libs/env/package.json", "entryFile": "libs/env/src", "external": ["react/jsx-runtime"], "rollupConfig": "tools/rollup/rollup.lib.js", "format": ["esm", "cjs"], "compiler": "swc", "assets": [{"glob": "libs/env/README.md", "input": ".", "output": "."}]}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/env/**/*.ts"]}}}}