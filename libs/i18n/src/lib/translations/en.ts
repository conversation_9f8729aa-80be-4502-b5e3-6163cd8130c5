/* eslint-disable no-template-curly-in-string */
// TODO: Rethink translations in a much more elegant and reusable way.
const enDictionary = {
    seo: {
        title: 'BOOKR - Online Booking App',
        seo_title: 'BOOKR - Online Booking App',
        seo_description:
            'BOOKR is a free beauty and medical scheduling app and makes appointments easy to find and book within seconds. Book anytime, from anywhere, 24/7.',
        seo_keywords: 'bookr mobile app application booking appointment',
        social_title: 'BOOKR - Online Booking App',
        social_description:
            'BOOKR is a free beauty and medical scheduling app and makes appointments easy to find and book within seconds. Book anytime, from anywhere, 24/7.',
        social_image_url: 'https://d230pchl7el2vc.cloudfront.net/seo-banner-en.png',
    },
    common: {
        breakTitle: 'Break title',
        price: 'Price',
        paidOnline: 'Paid online',
        generalNotes: 'General notes',
        updateYourVatSettings: 'Update your VAT settings',
        isTvaRegistered: 'Are you VAT registered?',
        vatCode: 'VAT code',
        clientArrivedToLocation: 'Client arrived to location',
        status: 'Status',
        monday: 'Monday',
        tuesday: 'Tuesday',
        wednesday: 'Wednesday',
        thursday: 'Thursday',
        friday: 'Friday',
        saturday: 'Saturday',
        sunday: 'Sunday',
        enable: 'Enable',
        enabled: 'Enabled',
        continueButton: 'Continue',
        noResults: 'No results found',
        allStaffMembers: 'All staff members',
        owner: 'Owner',
        month: 'month',
        week: 'Week',
        day: 'Day',
        days_one: 'day',
        days_other: 'days',
        mins_one: '{{count}} min',
        mins_other: '{{count}} mins',
        hours_one: '{{count}} hour',
        hours_other: '{{count}} hours',
        minsLabel_one: 'minute',
        minsLabel_other: 'minutes',
        hoursLabel_one: 'hour',
        hoursLabel_other: 'hours',
        time: 'Time',
        name: 'Name',
        phoneNumber: 'Phone number',
        staffName: 'Staff name',
        break: 'Break',
        appointment: 'Appointment',
        viewDetails: 'View details',
        cancel: 'Cancel',
        profile: 'Profile',
        save: 'Save',
        saveChanges: 'Save Changes',
        seeMore: 'See more',
        seeLess: 'See less',
        phone: 'Phone number',
        email: 'Email',
        block: 'Block',
        book: 'Book',
        with: 'with',
        create: 'Create',
        client: 'Client',
        edit: 'Edit',
        clientEmail: 'Client Email',
        callingCodeField: 'Country',
        emailField: 'Email address',
        passwordField: 'Password',
        phoneNumberField: 'Phone number',
        addNewAppointment: 'Add new appointment',
        selectDateAndTime: 'Select date and time',
        selectService: 'Select service',
        selectStaffMember: 'Select staff member',
        appointmentDetails: 'Appointment details',
        bookingMadeSuccessfully: 'Your booking was\nmade successfully!',
        clientName: 'Client name',
        serviceName: 'Service',
        dateAndTime: 'Date & Time',
        openAppointment: 'Open',
        breakCreatedSuccessfully: 'Break created successfully!',
        addNewBreak: 'Add break',
        from: 'From',
        to: 'To',
        yourBreaks: 'Your breaks',
        delete: 'Delete',
        yes: 'Yes',
        no: 'No',
        blockClientTitle: 'Are you sure you want to block {{displayName}}?',
        blockClientMessage:
            '{{displayName}} will not be able to book anymore at your place. You can unblock {{displayName}} at anytime by clicking the Unblock button.',
        deleteClientTitle: 'Are you sure you want to delete {{displayName}}?',
        deleteClientMessage:
            '{{displayName}} will not appear anymore in your client list. You can add {{displayName}} again by clicking the Add button.',
        blockedSuccessfully: '{{displayName}} blocked successfully!',
        unblock: 'Unblock',
        unblockedSuccessfully: '{{displayName}} unblocked successfully!',
        language: 'Language',
        subscription: 'Subscription',
        notes: 'Notes',
        moreOptions: 'More options',
        done: 'Done',
        change: 'Change',
        slotExpired: 'This slot just expired',
        slotExpiredMessage:
            'An existing appointment (for {{displayName}}: {{start}} - {{end}}) overlaps with your selected time. Are you sure you want to add this appointment too?',
        ok: 'OK',
        cancelConfirmTitle: 'Cancel the appointment for {{clientName}}',
        cancelConfirmMessage: 'The appointment will be cancelled. Do you want to continue?',
        noShowConfirmTitle: 'Mark the appointment for {{clientName}} as no show',
        noShowConfirmMessage: 'The appointment will be marked as no show. Do you want to continue?',
        print: 'Print',
        printRecommendation: 'For the best print experience we recommend a size of 10mm for each margin of the page.',
        date: 'Date',
        numberOfAppointments: 'Nr. appointments',
        staffMemberName: "Staff's name",
        appointmentsInThisHour: '<b>{{value}} appointments</b> starting in this hour.',
        poweredByBookr: 'Powered by bookr.ro',
        joinOnlineEvent: 'Join online event',
        somethingWentWrong: 'Something went wrong',
        categories: {
            hairstyling: 'Hairstyling',
            barber: 'Barber',
            makeup: 'Make-up',
            cosmetics: 'Cosmetics',
            manicure: 'Manicure',
            massage: 'Massage',
            tattoos: 'Tattoos',
            photography: 'Photography',
            videography: 'Videography',
            dentistry: 'Dentistry',
            psychologist: 'Psychologist',
            dermatology: 'Dermatology',
            nutrition_dietetics: 'Nutrition & Dietetics',
            body_remodeling: 'Body remodeling',
            implant: 'Implant',
            podiatry: 'Podiatry',
            gynecology: 'Gynecology',
            plastic_surgery: 'Plastic surgery',
            permanent_hair_removal: 'Permanent hair removal',
            orl: 'ORL',
            sport: 'Sport',
            coworking: 'Coworking',
            event: 'Event',
            ophthalmology: 'Ophthalmology',
            surgery: 'Surgery',
            other: 'Other',
            mentoring: 'Mentoring',
            coaching: 'Coaching',
            consultancy: 'Consultancy',
        },
        paymentMethods: {
            cash: 'Cash',
            card: 'Card',
        },
        paymentOnline: 'This appointment will be paid online, send the payment link to the client.',
        appointmentLabels: {
            confirmed: 'Confirmed',
            completed: 'Finished',
            cancelled: 'Cancelled',
            noShow: 'No Show',
        },
        errors: {
            requiredField: 'Required field',
            invalidTime: "Invalid time '${value}'",
            minValue: 'Value must be greater than {{minValue}}',
            timeBefore: 'Time must be before {{value}}',
            timeAfter: 'Time must be after {{value}}',
            minLength: 'Value must have at least {{minLength}} characters',
            passwordNotMatch: 'Passwords do not match',
        },
    },
    layoutSidebar: {
        calendar: 'Calendar',
        clients: 'Clients',
        feedback: 'Feedback',
        sales: {
            sales: 'Sales',
            overview: 'Overview',
            activity: 'Activity',
        },
        settings: {
            settings: 'Settings',
            business: 'Business',
            profile: 'Profile',
            application: 'Application',
        },
    },
    settingsLayout: {
        tabs: {
            general: 'General',
            categories: 'Categories',
            gallery: 'Gallery',
            workingHours: 'Working Hours',
            staff: 'Staff',
            virtualTour: '3D Virtual Tour',
            services: 'Services',
            payments: 'Payments',
            subscription: 'Subscription',
            messages: 'Messages',
            others: 'Others',
            notifications: 'Notifications',
            integrations: 'Integrations',
        },
    },
    newAppointmentButton: {
        newAppointment: 'New Appointment',
    },
    newAppointmentDialog: {
        addClientName: 'Add Client name',
    },
    userProfileDropdown: {
        yourProfile: 'Your profile',
        settings: 'Settings',
        help: 'Help',
        logout: 'Logout',
    },
    applicationSettingsPage: {
        title: 'Application',
        subtitle: 'Settings',
        billingInfo: 'Billing Info',
        name: 'Name',
        line1: 'Address Line 1',
        line2: 'Address Line 2',
        postalCode: 'Postal Code (or ZIP code)',
        city: 'City',
        taxId: 'Tax ID (VAT code)',
        settingsUpdated: 'Your settings were updated successfully!',
        chooseLanguage: 'Choose your language',
        subscriptionStatus: 'Subscription status',
        TRIAL: 'Trial',
        ACTIVE: 'Active',
        PAST_DUE: 'Inactive',
        yourTrialWillEnd: 'Your trial subscription will end on',
        yourSubscriptionIsActive: 'Your subscription is active! 🎉',
        yourSubscriptionIsInactive: 'Your subscription is inactive. Activate now!',
        buyNow: 'Buy now!',
        paymentSuccessTitle: 'Your payment was completed successfully!',
        paymentSuccessMessage: 'You will receive an email with your order details!',
        smsBalance: 'SMS Balance',
        makeSureToTopup: 'Please make sure to top up SMS to ensure successful delivery of communications to attendees.',
        currentBalance: 'Current Balance',
        remaining: 'left',
        topUp: 'Top Up',
        smsOptionsSelect:
            'Choose the number of SMS you can use for your business. And we will process the payment in a few seconds',
        deleteAccount: 'Delete account',
        deleteAccountHeader: 'Delete Your Account',
        deleteAccountMessage: 'We will not be able to recover your account after deletion.',
        deleteAccountDetails: 'Deleting account will do the following',
        deleteAccountAction1: 'Log you out on all devices',
        deleteAccountAction2: 'Delete all of your account information',
        deleteAccountAction3: 'Remove all the staff members within this business',
        deleteAccountAction4: 'Cancel all the appointments',
    },
    appointmentsPage: {
        title: 'Appointments',
        selectWeek: 'Select the week',
        appointmentUpdatedSuccessfully: 'Appointment updated successfully',
        appointmentUpdateError: "Appointment couldn't be updated.",
        rescheduleAppointment: 'Reschedule Appointment',
        moveAppointment: 'Do you want to move the appointment?',
        moveAppointmentMessage:
            'The appointment will be moved to {{dateTime}}. {{clientName}} will be notified about this change.',
        moveAppointmentButton: 'Move the appointment',
        changeView: "Change calendar's view",
        fullScreen: 'Fullscreen',
        fullScreenReturn: 'Return to the initial size',
        exportAppointments: 'Export appointments of the selected staff members',
    },
    appointmentDetailsPage: {
        title: 'Appointment Details',
        notes: 'Notes',
        noNotes: 'No notes added',
        addNote: 'Add note',
        deleteNote: 'Delete note',
        errorGettingNotes: "Couldn't get client notes",
        imReady: "I'm ready",
        moreOptions: 'More options',
        absentClient: 'No show',
        reschedule: 'Reschedule',
        cancel: 'Cancel',
        imReadyError: 'An error occurred while marking you as ready.',
        noShowError: 'An error occurred while marking client absent.',
        cancelError: 'An error occurred while canceling appointment.',

        imReadyConfirmTitle: "We'll let {{clientName}} know that you are ready.",
        imReadyConfirmMessage:
            "One notification will be sent to {{clientName}}. We'll let {{clientName}} know that you are ready to start the appointment. Do you want to continue?",

        imReadyConfirmSubmit: 'Send notification',
        imReadyConfirmCancel: 'Cancel',
    },
    businessSettingsPage: {
        title: 'Business',
        subtitle: 'Settings',
        virtualTourRequestTitle: 'Congratulations! We received your request 🎉',
        virtualTourRequestMessage:
            'We successfully received your request for the 3D Virtual Tour integration. In the next couple of days, a BOOKR member will contact you via phone or email. Thank you!',

        accountDetails: 'Account details',
        businessName: 'Business Name',
        businessDescription: 'Business Description',
        phoneNumber: 'Phone Number',
        socialNetworks: 'Social Networks',
        facebook: 'Facebook',
        instagram: 'Instagram',
        website: 'Website',
        location: 'Location',
        changeLocation: 'Change Location',
        allCategories: 'All Categories',
        yourGallery: 'Your Gallery',
        yourEmployees: 'Your Employees',
        addNewEmployee: 'Add New Employee',
        addNewEmployeeDescription: 'Add a new employee by adding its email.',
        callingCodeField: 'Country',
        phoneNumberField: 'Phone number',
        sendInvite: 'Send Invite',
        add3dVirtualTour: 'Add 3D Virtual Tour',
        add3dVirtualTourDescription:
            'This is our more complex feature. We will show to your customers your business location with an 3D Virtual tour.',
        requestAccess: 'Request the access',

        errorUpdatingSettings: 'An error occurred while updating your business settings.',
        errorUpdatingProfilePicture: 'An error occurred while updating your business profile picture.',
        errorUpdatingCategories: 'An error occurred while updating your business categories.',
        errorUpdatingMembers: 'An error occurred while updating your business members.',
        errorInvitingEmployee: 'An error occurred while inviting new employee.',
        warningUploadingImages:
            'An error occurred while uploading images to your business gallery. So me images were not uploaded.',
        errorUploadingImages: 'An error occurred while uploading images to your business gallery.',
        errorUpdatingWorkingHours: 'An error occurred while updating your business working hours.',

        // Business Services Page
        businessServices: 'Business Services',
        selectStaffToViewServices: 'Select staff members to view their services',
        allStaffServicesSubtitle: '{{count}} services from {{staffCount}} staff members',
        singleStaffServicesSubtitle: '{{count}} services from {{staffName}}',
        multipleStaffServicesSubtitle: '{{count}} services from {{staffCount}} selected staff members',
        noStaffMembers: 'No Staff Members',
        noStaffMembersDescription: 'There are no staff members in your business yet.',
        errorRequestingVirtualTour: 'An error occurred while requesting virtual tour.',

        previewMessage: 'Preview',
        saveMessage: 'Save Changes',
        sendTestMessage: 'Send Test Message',
        sendTestMessageConfirmationTitle: 'A test message will be sent to {{phoneNumber}}.',
        sendTestMessageConfirmationMessage: 'Do you want to continue?',
        testMessageSent: 'A test message has been sent successfully.',
        errorSendingTestMessage: 'An error occurred while sending test message.',
        noPreviewContent: 'There is no content to preview.',
        personalizeYourMessages: 'Customize messages',
        personalizeYourMessagesDescription:
            'Personalizeaza aceste mesaje pe placul tau pentru a crea o experienta cat mai placuta clientului tau.',
        selectTemplateType: 'Select the template you want to customize:',
        selectTemplateTypeDescription: {
            templateAppointmentReminder: 'Your clients will receive reminder notifications for their appointments.',
            templateAppointmentCreated: 'Your clients will receive this notifications when an appointment is created.',
            templateAppointmentCancelled:
                'Your clients will receive this notifications when an appointment is cancelled.',
            templateAppointmentRescheduled:
                'Your clients will receive this notifications when an appointment is rescheduled.',
        },
        templateNames: {
            templateAppointmentReminder: 'Reminder',
            templateAppointmentCreated: 'Created',
            templateAppointmentCancelled: 'Cancelled',
            templateAppointmentRescheduled: 'Rescheduled',
        },
        variables: {
            appointmentId: 'ID',
            businessName: 'Business Name',
            businessLocation: 'Business Location',
            businessPhoneNumber: 'Business Phone Number',
            clientName: 'Client Name',
            staffName: 'Staff Name',
            dateTime: 'Date Time',
            date: 'Date',
            time: 'Time',
            newLine: 'New Line',
            serviceName: 'Service Name',
            servicePrice: 'Service Price',
            serviceDuration: 'Service Duration',
        },
    },
    clientPage: {
        title: 'Client',
        backToClients: 'Back to clients',
        totalAppointments: 'Total appointments',
        confirmed: 'Confirmed',
        completed: 'Finished',
        cancelled: 'Cancelled',
        noShow: 'No show',
        clientRevenue: 'Revenue from client',
        appointmentsHistory: 'Appointment History',
        appointments: 'Appointments',
        documents: 'Documents',
        noDocuments: 'This client has no uploaded documents',
        uploadDocuments: 'Upload documents',
        clientsDocuments: 'Customer Documents',
        addDocument: 'Add document',
        notes: 'Notes',
        documentsListError: 'An error occurred while loading client documents',
    },
    clientsPage: {
        title: 'Clients',
        searchPlaceholder: 'Find a customer',
        displayMore: 'Display more',
    },
    feedbackPage: {
        title: 'Feedback',
        whatDoYouThinkAboutUs: 'What do you think about BOOKR?',
        chooseFeedbackCategory: 'Choose the feedback category',
        tellUsWhatWeCanImprove: 'Tell us what we can improve',
        send: 'Send',
        feedbackSentSuccessfully: 'Your feedback has been sent successfully',
        feedbackSentSuccessfullyDescription:
            'We want to improve your experience and your feedback is crucial to evolve our product!',
        errorSendingFeedback: 'An error occurred while sending your feedback.',
        categories: {
            bugReport: 'BUG Report',
            suggestions: 'Suggestions',
            content: 'Content',
            compliment: 'Compliment',
            other: 'Others',
        },
    },
    profileSettingsPage: {
        title: 'Profile',
        subtitle: 'Settings',
        yourInformation: 'Your Information',
        displayName: 'Your Name',
        phoneNumber: 'Phone Number',
        emailAddress: 'Email Address',
        numberOfDays: 'Number of Days',
        yourAvailability: 'Your Availability',
        yourAvailabilityDescription:
            'The number of days in the future for accepting bookings. For example, if you enter the value 14, then your clients will be able to make an appointment (starting from today) with max. 14 days in the future.',
        yourServices: 'Your Services',
        yourServicesDescription: 'Show to your clients what you have to offer.',
        newService: 'New Service',
        errorUpdatingProfileSettings: 'An error occurred while updating your profile settings.',
        errorUpdatingProfilePicture: 'An error occurred while updating your profile picture.',
        errorUpdatingWorkingHours: 'An error occurred while updating your working hours.',
        security: 'Security',
        changePassword: 'Change Password',
        changePasswordDescription: 'Choose a secure password to protect your account.',
        manageYourPayments: 'Manage your payments',
        manageYourPaymentsDescription: 'Manage your income settings',
        chooseIncomeDistribution: 'Choose the method of income distribution',
        multipleSelectPayment: 'Multiple select Payment',
        multipleSelectPaymentDescription:
            'The income will be managed individually by your staff members each of one of them will be able to setup a payment configuration for their income.',
        onlinePayments: 'Online Payments',
        onlinePaymentsDescription: 'This option allows you to pay for your services online.',
        onlinePaymentsCaption: 'If you want to enable online payments, start the setup below.',
        onlinePaymentsSetup: 'Setup online payments',
        yourAccountStatus: {
            notConfigured: 'Your account status: needs setup',
            configured: 'Your account status: configured',
        },
        frequentQuestions: {
            title: 'Frequent questions',
            questions: [
                {
                    title: 'How are the services paid?',
                    description:
                        'Services are paid through a configured bank account. The business owner can set up a single bank account for the whole business, or allow each staff member to add their individual bank account.',
                },
                {
                    title: 'For what type of services can I add online payment?',
                    description:
                        'Online payment can only be added for simple services. Subscription-based services cannot be paid online.',
                },

                {
                    title: 'How long does it take to receive payment for a service?',
                    description: 'The payment processor transfers payments to the configured account in 1-3 days.',
                },
                {
                    title: 'When will the client pay for the service?',
                    description: 'The customer pays for the appointment at the time of creating it.',
                },
                {
                    title: 'What are the fees?',
                    description:
                        'The business (or you) pays a fee to the payment processor for each transaction. The fee is 2.9% + 0.25€ for each transaction. Bookr adds its own fee of 0.60€ for each transaction.',
                },
                {
                    title: 'What happens if a staff member cancels an appointment?',
                    description:
                        "If a staff member cancels an appointment, the payment will be refunded to the customer's account within 7-10 business days.",
                },
                {
                    title: 'What happens if a customer cancels an appointment?',
                    description:
                        "If a customer cancels an appointment, the payment will be refunded to the customer's account within 7-10 business days only if the cancellation is made at least a certain number of hours before the appointment. This value can be set in your profile.",
                },
                {
                    title: 'How can I stop receiving online payments?',
                    description: 'Online payment can be stopped by disabling it for each service you offer.',
                },
            ],
        },
    },
    signInPage: {
        title: 'Sign-in to BOOKR!',
        subtitle: 'Sign-in with your email',
        signInTitle: 'Sign-in to your account',
        signInSubtitle: 'Enter your email and password to sign-in.',
        callingCodeRequired: 'Country is required',
        emailRequired: 'Email address is required',
        phoneNumberRequired: 'Phone number is required',
        errorSignIn: 'An error occurred while signing in.',
        forgotPassword: 'Forgot password?',
        errorResettingPassword: 'An error occurred while resetting your password.',
        passwordResetEmailSent: 'Password reset email sent successfully',
    },
    authConfirmationPage: {
        title: 'Enter the SMS code',
        subtitle: 'Enter the code you received in the SMS',
        notReceivedMessage: "Didn't get the code?",
        sendCodeAgainMessage: 'Send it again!',
        verificationCodeNotSent: 'Verification code cannot be sent',
        codeSent: 'A new verification code has been sent',
    },
    signUpPage: {
        title: "Let's know each other!",
        subtitle: 'Complete all the fields',
        emailField: 'Email',
        firstNameField: 'First name',
        lastNameField: 'Last name',
        passwordField: 'Create a password',
        passwordConfirmationField: 'Confirm your password',
        errorCreatingAccount: 'An error occurred while creating your account',
        EMAIL_EXISTS: 'This email address is already in use',
    },
    signUpVerificationPage: {
        title: 'Confirmation sent!',
        subtitle: 'Please verify your email.',
        verificationMessage: 'A verification link was sent to: <b>{{email}}</b> This will expire soon!',
        resendEmail: 'Resend the email',
        wrongEmail: 'Wrong email?',
        enterNewEmail: 'You need <cta>to create a new account!</cta>',
        pleaseConfirmEmailError: 'Please confirm your email',
        confirmed: 'Confirmed',
        errorCheckingConfirmedEmail: 'An error occurred while checking if the email confirmed.',
        emailNotConfirmed: 'Email not confirmed',
        emailNotConfirmedMessage:
            'Your email is not confirmed. Please check your inbox and confirm using the link we sent you. If you didn\'t receive one, click on the "$t(resendEmail)" button.',
    },
    signUpBusinessPage: {
        title: 'About your business',
        subtitle: 'We need more information about your business.',
        businessNameField: 'Your Business Name',
        emailField: 'Email address',
        phoneField: 'Phone number',
        countryField: 'Country',
    },
    signUpBusinessTypePage: {
        title: 'What kind of business is it?',
        subtitle: 'We need more information about your business.',
        chooseBusinessDomain: 'Choose your business domain:',
        selectCategoryHelperText: 'Select at least one category.',
        categories: {
            hairstyling: 'Hairstyling',
            barber: 'Barber',
            nails: 'Nails',
            massage: 'Massage',
            medical: 'Medical',
            makeup: 'Make-up',
        },
    },
    signUpBusinessLocationPage: {
        title: 'Where is your business location?',
        subtitle: 'We need more information about your business.',
        addressField: 'Enter your business location',
    },
    signUpBusinessProgramPage: {
        title: 'Business working hours',
        subtitle: 'You can update these hours at anytime.',
    },
    signUpBusinessIdentityPage: {
        title: 'Your business',
        subtitle: 'Add your business logo or a picture with an overview of your workplace',
        uploadPhoto: 'Upload a photo',
        finish: 'Finish',
        skip: 'Skip for now',
        businessCreated: 'Your business was created!',
    },
    workingProgram: {
        start: 'Start',
        end: 'End',
        days: {
            monday: 'Monday',
            tuesday: 'Tuesday',
            wednesday: 'Wednesday',
            thursday: 'Thursday',
            friday: 'Friday',
            saturday: 'Saturday',
            sunday: 'Sunday',
        },
    },
    locationDialog: {
        searchLocation: 'Search location',
    },
    galleryGrid: {
        addNewPhoto: 'Add new photo',
    },
    employeeManager: {
        remove: 'Remove',
        owner: 'Owner',
    },
    servicesManager: {
        noServices: 'No services',
        noServicesDescription: 'There are no services added yet. Add a new service to your business.',
        noServicesForSelectedStaff: 'No services for selected staff',
        selectedStaffHaveNoServices: 'The selected staff members have no services configured.',
        duration: 'Duration',
        price: 'Price',
        description: 'Description',
        minutes: 'minutes',
        hour: 'hour',
        hours: 'hours',
        hiddenFromClients: 'Hidden from clients',
    },
    serviceDialog: {
        addServiceTitle: 'Add Service',
        editServiceTitle: 'Edit Service',
        serviceInformation: 'Service Information',
        serviceName: 'Service Name',
        price: 'Price',
        currency: 'Currency',
        duration: 'Duration',
        description: 'Description',
        optional: 'optional',
        breakBetweenServices: 'Break Between Services',
        breakBetweenServicesTooltip:
            "Set a time interval in which a customer cannot make a reservation after this service ends and a new service can be booked. Let's say someone makes an appointment from 14:00 to 15:00. If Break Interval is set to 30 mins, the next available booking time can only be made from 15:30. You will have 30 mins break between services to get ready.",
        serviceHiddenFromClient: "Don't show this service to clients",
        serviceHiddenFromClientTooltip: 'If you check this option, the service will not be shown to the client.',
        serviceOnlineEvent: 'Create an online video event for this service',
        serviceOnlineEventTooltip:
            'If you check this option, when the client will book the service, an online event will be created where your customer will be able to join using Google Meet.',
        serviceOnlinePayments: 'Allow clients to pay for this service online',
        serviceOnlinePaymentsTooltip:
            'If you check this option, the client will be able to pay for this service online.',
        integrationType: 'Integration type',
        chooseColor: 'Set a color for this service',
        resetColor: 'Reset Color',
        cancel: 'Cancel',
        addServiceButton: 'Add Service',
        editServiceButton: 'Edit Service',
        deleteService: 'Delete Service',
        errorDeletingService: 'An error occurred while deleting the service',
        addNoteTitle: 'Add Note',
        editNoteTitle: 'Edit Note',
        addNoteButton: 'Add Note',
        editNoteButton: 'Save Note',
        deleteNoteButton: 'Delete',
        addNotePlaceholder: 'Add a note for the current client',
        successDeleteNote: 'Note deleted successfully',
        successAddNote: 'Note created successfully',
        successEditNote: 'Note updated successfully',
        errorDeleteNote: 'Unable to delete note',
        errorAddNote: 'Unable to add note',
        errorEditNote: 'Unable to edit note',
        minutes: 'minutes',
    },
    inviteClients: {
        heading: 'Import new clients',
        description: 'Import the clients you wish to hvae in your database.',
        addNewClients: 'Add your clients',

        gettingStartedTitle: 'Download the template ',
        gettingStartedDescription:
            'Click on the button "$t(download)" to receive the file that you need to complete in order to import the clients.',
        download: 'Download',
        cancel: 'Cancel',
        continue: 'Continue',
        close: 'Close',
        inviteAgain: 'Add again',
        iHaveDownloaded: 'I downloaded the file',
        displayName: 'Display Name',
        phone: 'Phone number',
        displayNamePlaceholder: 'John Doe',
        phonePlaceholder: '+41 234 567 890',
        templateFileName: 'Template.csv',

        uploadTitle: 'Upload the completed document.',
        dragAndDropHere: 'Drag and drop the file here or <cta>search for the file.</cta>',
        dropContent: 'Drop content here',
        dontHaveTemplate: "You don't have the template?",
        uploadedWrongFile: 'Did you upload the wrong file? <cta>Try again!</cta>',
        preview: 'Preview',
        noFileSelected: 'File not selected!',
        errorParsingFile: 'There was an error uploading the file.',

        reviewTitle: 'Preview the clients that will be imported.',
        finish: 'Finish',
        notSpecified: 'N/A',
        inviteClientsError: 'An error occurred while importing clients.',
        inviteClientsSuccess: 'Clients imported successfully.',
        inviteClientsWarning: 'Clients import finished, some of them were not imported',
        importingClients: 'Your clients are being importing, we will keep you updated.',

        clientName: 'Client name',
        callingCodeField: 'Country',
        phoneNumberField: 'Phone number',
        singleClientImportInvalidFieldsError: 'Please input all fields!',

        dialogHeader: 'Add your customers to BOOKR in just a few moments.',
        dialogText: 'Choose below the way you want to add your customers to your business database.',
        singleClientHeader: 'One customer',
        singleClientText:
            'Add a single customer using his phone number. Please make sure you enter the correct phone number.',
        multipleClientsHeader: 'More customers',
        multipleClientsText: 'Add multiple clients using an excel document.',

        singleClientDialogHeader: 'Add only one customer',
        singleClientDialogText:
            'Add a single customer using his phone number. Please make sure you enter the correct phone number.',

        singleClientSuccessDialogHeader: 'You have successfully added the customer',
        singleClientSuccessText:
            'Your customer was added to your database. By clicking on the "Add again" button, you can invite other customers.',

        inviteClientError: 'An error occurred while importing the client.',
        inviteClientSuccess: 'Client imported successfully.',
        inviteClientWarning: 'Client import finished, but it was not imported',
        importingClient: 'Your client is being imported, we will keep you updated.',
    },
    changePasswordDialog: {
        title: 'Change Password',
        description:
            'Choose a secure password to protect your account. </br>' +
            'Your password must be at least 8 characters long',
        oldPassword: 'Old Password',
        newPassword: 'New Password',
        passwordConfirmation: 'Confirm Password',
        cancel: 'Cancel',
        submit: 'Change Password',
        errorChangingPassword: 'An error occurred while changing the password',
        notLoggedIn: 'You are not logged in',
        passwordChanged: 'Password changed successfully',
    },
    overviewPage: {
        title: 'Sales Overview',
        totalAppointments: 'Total Appointments',
        appointmentsFinished: 'Appointments Finished',
        totalClients: 'Total Clients',
        appointmentsCanceled: 'Appointments Canceled',
    },
    activityPage: {
        title: 'Orders Activity',
        columnClientName: 'Client Name',
        columnPhoneNumber: 'Phone Number',
        columnEmail: 'Email',
        columnService: 'Service',
        columnDate: 'Date',
        columnAmount: 'Amount',
        columnStatus: 'Status',
        new: 'New',
        canceled: 'Canceled',
        rescheduled: 'Rescheduled',
        confirmed: 'Confirmed',
        completed: 'Finished',
        cancelled: 'Cancelled',
        noShow: 'No Show',
    },
    topStaffMembersCard: {
        topStaffMembers: 'Top Staff Members',
        ordersNumber_zero: 'No orders',
        ordersNumber_one: '{{count}} order',
        ordersNumber_other: '{{count}} orders',
        memberSince: 'Staff member since: {{date}}',
    },
    timeBookedCard: {
        timeBooked: 'Time Booked',
        timeWasted: 'Non-booked hours',
        timeUsed: 'Worked hours',
        totalTime: 'Total Time {{count}} $t(hours, { "count": {{count}} })',
    },
    topActivitiesCard: {
        topActivities: 'Top Activities',
        noActivities: 'No activities.',
        totalServices: 'Total Services',
    },
    totalEarningsCard: {
        totalEarnings: 'Total Earnings',
    },
    cookieBanner: {
        title: '🍪 We use cookies to personalize your BOOKR experience',
        acceptButton: 'Accept',
    },
    subscriptionCard: {
        headline: 'Subscription',
        trial: 'Trial',
        currentPlan: 'Current plan',
        nextPayment: 'Next Payment: {{date}}',
        upgradePlan: 'Upgrade plan',
        cancelSubscription: 'Cancel subscription',
        billingHistory: 'Billing history',
        noBillings: 'No billings',
        noBillingsDescription: 'There are no billings for your subscriptions yet.',
        date: 'Date',
        planType: 'Type',
        planPrice: 'Price',
        invoiceLink: 'Link',
        viewInvoice: 'View invoice',
        cancelSubscriptionTitle: 'Are you sure you want to cancel your current subscription?',
        cancelSubscriptionMessage:
            'After canceling your subscription, you will need to choose a new plan to continue using BOOKR.',
        upgradeSubscriptionTitle: 'You are going to upgrade your subscription',
        upgradeSubscriptionMessage:
            'Your current subscription will be <b>cancelled</b> and you will need to choose a new plan. Are you sure you want to continue?',
    },
    subscriptionPlans: {
        free: {
            name: 'Free',
            description: 'Free',
            features: `
                One staff member
                Business listing
                Your personal website
                Unlimited appointments
                Google integration
            `,
        },
        standard: {
            name: 'Standard',
            description: 'Standard',
            features: `
                Unlimited staff members
                Team management
                Client history
                Automatic Notifications
                Reminders
            `,
        },
        professional: {
            name: 'Professional',
            description: 'Professional',
            features: `
                Sales reports
                Performance indicators
                Top staff members
                Custom notifications
            `,
        },
        custom: {
            name: 'Custom',
            description: 'Custom solutions for your business',
            price: 'Custom price',
            features: `
                Custom solutions for your business
            `,
        },
    },
    planSelect: {
        recommended: 'Recommended',
        choosePlan: 'Choose {{plan}}',
        planBenefitsFrom: 'Plan benefits from {{plan}}',
        pricePerMonth: '{{price}}/month',
        pricePerYear: '{{price}}/year',
        errorCheckoutMessage: 'An error occurred while checking out',
        contactUs: 'Contact us',
    },
    planSelectPage: {
        title: 'Select one of our plans',
    },
    periodSwitch: {
        monthly: 'Monthly',
        yearly: 'Yearly',
    },
    pricingPlans: {
        title: 'Compare our plans',
        subtitle:
            'No surprises, just fair and honest prices. Simply choose a plan with the desired features or compare plans',
        monthly: 'Monthly',
        yearly: 'Yearly',
        month: 'month',
        year: 'year',
        free: 'Free',
        standard: 'Standard',
        professional: 'Professional',
        custom: 'Custom',
        startNow: 'Start now',
        recommended: 'Recommended',
        contactUs: 'Contact us',
        comparePlans: 'Compare them',
        promoText: 'Save 20% using the annual payment!',
        testRowText: 'Access to the booking system',
        freePrice: 'lei 0,00',
        standardPrice: 'lei 187,99',
        professionalPrice: 'lei 590',
        customPrice: 'custom price',
        booking: 'Booking',
        staffMembers: 'Staff Members',
        businessListing: 'Business Listing',
        ownWebsite: 'Own website',
        accessToCommunity: 'Access to the community',
        activityHistory: 'Activity History',
        unlimitedBookings: 'Unlimited bookings',
        googleIntegration: 'Google Integration',
        waitingList: 'Waiting list',
        businessManagement: 'Business Management',
        salesReports: 'Sales Reports',
        performanceIndicators: 'Performance indicators',
        topProfessionals: 'Top Professionals',
        teamManagement: 'Team management',
        clientsManagement: 'Clients Management (CRM)',
        customerRecord: 'Customer record',
        importClients: 'Import clients',
        listClients: 'List clients',
        clientsDocuments: 'Clients documents',
        notifications: 'Notifications',
        automaticNotifications: 'Automatic notifications',
        reminders: 'Reminders',
        personalisedNotifications: 'Personalised notifications',
        smsNotifications: 'SMS notifications*',
        assistance: 'Assistance',
        clientSupport: 'Client Support',
        limited: 'Limited',
        unlimited: 'Unlimited',
        anytime: '24/7',
        loading: 'Loading pricing plans',
        errorLoadingPrices: 'Error loading pricing plans',
        benefitsFrom: 'Benefits from ',
        customText: 'Customized solutions for your business',
    },
    privilegeBasedAccess: {
        FREE_ACCESS: 'Free',
        STANDARD_ACCESS: 'Standard',
        PROFESSIONAL_ACCESS: 'Professional',
        requiredPrivileges_one: 'Require <b>{{privileges}}</b> plan access',
        requiredPrivileges_other: 'Require one of <b>{{privileges}}</b> plan access',
    },
    homepage: {
        comparePlans: 'Compare plans',
        tcs: 'Terms and Conditions',
        privacyPolicy: 'Privacy Policy',
        home: 'Home',
        signUp: 'Sign Up',
        heroTitle: 'Manage your bookings, business and staff members in one place',
        heroSubtitle:
            'Discover BOOKR, the booking platform that helps you manage your business, your staff and your clients in an easy way.',
        whichDomainIsYourBusiness: 'In which domain is your business?',
        medicalCardDescription: 'Suited for medical clinics, doctors, dentists, etc.',
        beautyCardDescription: 'Suited for salons, beauty salons, spa, etc.',
        learnMore: 'Learn more',
        featuresTitle: 'How does BOOKR helps you <br /> manage your business?',
        feature1Title: 'Simple and fast bookings',
        feature1Description:
            'Forget about the hassle of appointments over the phone or paper. Your clients can book appointments to your business anytime, anywhere.',
        feature2Title: 'Activity reports',
        feature2Description:
            'Track your business activity and see how your business is doing. You can see the activity of your staff members and clients.',
        feature3Title: 'Performance indicators',
        feature3Description: 'You get predictability and find solutions to scale your business.',
        feature4Title: 'Reminders and notifications',
        feature4Description:
            'You and your clients will receive automatic reminders and notifications, so nobody will forget about their appointments.',
        feature5Title: 'Visibility and interaction',
        feature5Description: 'Grow your interaction and visibility rate with 350% by using BOOKR.',
        digitalizeBusiness: 'Digitalize your business with <br /> BOOKR',
        feature6Title: 'Easy to use',
        feature6Description:
            'Intuitive for you and for your clients. BOOKR is easy to use and you can manage your business in a few clicks.',
        feature7Title: '24/7 access',
        feature7Description: 'BOOKR is your digital assistance. You can access your business 24/7, 365 days a year.',
        feature8Title: '3D Virtual Tour',
        feature8Description:
            'We are the only company that offers a 3D virtual tour of your business. You and your clients can see the location of your business in 3D.',
        digitalWaysManageBusinessTitle: '3 digital ways to manage your business, wherever you are',
        feature9Title: 'Mobile app',
        feature9Description:
            'BOOKR is available on the App Store and Google Play. You can manage your business from your phone.',
        feature10Title: 'BOOKR Web',
        feature10Description:
            'BOOKR is available on the web. We create your own website and you can manage your business from your computer.',
        feature11Title: 'Dashboard',
        feature11Description: 'A complex dashboard with all the information you need to manage your business.',
        startNow: 'Get started!',
        conclusionTitle: 'Connect with over 500 businesses <br /> all over the world',
        conclusionDescription:
            'Use BOOKR for free to book your appointments at your favourite places <br /> and manage your business in a digital way',
        aboutBookr: 'About BOOKR',
        forBusinesses: 'For Businesses',
    },
    beauty: {
        heroTitle: 'Beauty appointments, now simpler',
        heroSubtitle:
            'Discover BOOKR, the booking platform that helps you manage your business, your staff and your clients in an easy way.',
        businessesInBeauty: 'Digitalized beauty businesses',
        hairstyling: 'Hairstyling',
        manicure: 'Manicure',
        massage: 'Massage',
        makeup: 'Makeup',
        barber: 'Barber',
        section1Heading: 'BOOKR gives you back over 500 hours that you spend on your appointments',
        businessManagement: 'Manage your business',
        businessManagementDescription:
            'You have 100% control over your business. Schedule, prices, location, services and staff management, all in one place',
        accessToClients: '24/7 <br /> access to <br /> your <br /> clients.',
        new: 'New',
    },
    medical: {
        heroTitle: 'Medical appointments, now simpler',
        heroSubtitle:
            'Discover BOOKR, the online booking platform that helps you manage your business, your staff and your patients in an easy way.',
        bookrDashboardPatients: 'BOOKR Dashboard – a new way to <br /> manage patients and appointments',
        feature1Title: 'Web access',
        feature1Description:
            'Improve your relationship with your patients by managing their bookings using our innovative booking dashboard platform.',
        feature2Title: 'Digital patient records',
        feature2Description:
            'Improve your relationship with your patients by managing their bookings using our innovative booking dashboard platform.',
        feature3Title: 'Easier bookings',
        feature3Description: 'Schedule your appointments with ease. You can book your patients in a few clicks.',
        feature4Title: 'Digital medical businesses only with BOOKR',
        feature4Description:
            'All the medical businesses in the world use BOOKR. We provide you with the best medical booking platform for you and for your patients.',
        medicalPlaces: 'Medical clinics <br /> Medical centers',
        feature5Title: 'Reports and statistics',
        feature5Description:
            'You can see the real-time data reports and statistics of your business. You can see statistics for your business and your staff members.',
        why: 'Why',
        predictability: 'Predicability',
        predictabilityDescription:
            'BOOKR is the only booking platform that allows you to predict your business’s future.',
        userExperience: 'User Experience',
        userExperienceDescription:
            'You, your staff members and your patients will love BOOKR. Simple. Intuitive. Fast.',
        conclusionTitle: 'Use BOOKR Dashboard for your medical business',
        conclusionDescription:
            'Provide your patients with a better experience and a better experience for you. Contact us for more information!',
    },
    businessPage: {
        viewVirtualTour: 'View virtual tour',
        services: 'Services',
        service: 'Service',
        hide: 'Hide',
        showAllServices: 'Show All Services',
        aboutUs: 'About Us',
        schedule: 'Schedule',
        bookNow: 'Book now',
        cancel: 'Cancel',
        createYourAppointment: 'Create Your appointment',
        appointmentSuccess: 'Your appointment was made successfully!',
        appointmentDetails: 'Your appointment details',
        dateAndTime: 'Date & Time',
        downloadBookrApp: 'If you want to know more details about your appointment, download the BOOKR application',
        chooseTheRightDate: 'Choose the right date for you',
        chooseTheRightTime: 'Choose the right time for you',
        noSlotsAvailable: 'No slots available',
        noSlotsAvailableText:
            'We no longer have seats available for this date, but we can add you to a waiting list in case seats are vacated.',
    },
    calendarPage: {
        title: 'Calendar',
    },
};

export default enDictionary;
