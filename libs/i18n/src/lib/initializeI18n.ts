import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import { translations } from './translations';

export function initializeI18n(language?: string) {
    const selectedLanguage = language || navigator.language.split(/[-_]/)[0];

    return i18n.use(initReactI18next).init({
        resources: translations,
        fallbackLng: 'en',
        lng: selectedLanguage ?? 'en',
        interpolation: {
            escapeValue: false,
        },
    });
}
