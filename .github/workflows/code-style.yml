name: 'Code style'

on: pull_request
jobs:
    prettier:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v2
              with:
                  ref: ${{ github.head_ref }}
                  fetch-depth: 0

            - name: Restore cache
              uses: actions/cache@v3
              with:
                  path: |
                      yarn.lock
                      node_modules
                  key: ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}
                  restore-keys: |
                      ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}

            - name: Dependency install
              run: yarn install --ignore-scripts --pure-lockfile --non-interactive

            - name: Pretty code
              run: yarn nx format

            - name: Commit changes
              uses: stefanzweifel/git-auto-commit-action@v4
              with:
                  commit_message: Prettier changes

    eslint:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v2
              with:
                  ref: ${{ github.head_ref }}
                  fetch-depth: 0

            - name: Restore yarn cache
              uses: actions/cache@v3
              with:
                  path: |
                      yarn.lock
                      node_modules
                  key: ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}
                  restore-keys: |
                      ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}

            - name: Restore eslint cache
              uses: actions/cache@v3
              with:
                  path: |
                      .eslintrc.json
                  key: ${{ runner.os }}-eslint-v1-${{ hashFiles('.eslintrc.json') }}
                  restore-keys: |
                      ${{ runner.os }}-eslint-v1

            - name: Dependency install
              run: yarn install --ignore-scripts --pure-lockfile --non-interactive

            - name: Linting
              run: yarn nx run-many --target lint --all --parallel 4
#    tests:
#        runs-on: ubuntu-latest
#        steps:
#            - name: Checkout
#              uses: actions/checkout@v2
#              with:
#                  ref: ${{ github.head_ref }}
#                  fetch-depth: 0
#
#            - name: Restore cache
#              uses: actions/cache@v3
#              with:
#                  path: |
#                      yarn.lock
#                      node_modules
#                  key: ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}
#                  restore-keys: |
#                      ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}
#
#            - name: Dependency install
#              run: yarn install --ignore-scripts --pure-lockfile --non-interactive
#
#            - name: Run Tests
#              run: yarn nx run-many --target test --all --parallel 4
