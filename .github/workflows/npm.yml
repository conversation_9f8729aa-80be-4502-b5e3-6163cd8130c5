name: 'NPM Packages'

on:
    release:
        types: [published]
    workflow_dispatch:
        inputs:
            version:
                description: 'Fixed version'
                required: false
                type: string

            versionBump:
                description: Increment version
                required: false
                type: choice
                default: patch
                options:
                    - major
                    - premajor
                    - minor
                    - preminor
                    - patch
                    - prepatch
                    - prerelease

jobs:
    npm-publish:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              uses: actions/checkout@v2
              with:
                  ref: ${{ github.head_ref }}

            - name: Setup Node v16
              uses: actions/setup-node@v2
              with:
                  node-version: '16'

            - name: Restore cache
              uses: actions/cache@v3
              with:
                  path: |
                      yarn.lock
                      node_modules
                  key: ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}
                  restore-keys: |
                      ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}

            - name: Dependency install
              run: yarn install --ignore-scripts --pure-lockfile --non-interactive

            - name: Setup NPM
              run: |
                  echo "REF: ${{ github.head_ref }}"
                  echo "//npm.pkg.github.com/:_authToken=${{ secrets.GITHUB_TOKEN }}" > ~/.npmrc
                  echo "" >> ~/.npmrc
                  echo "@bookr-technologies:registry=https://npm.pkg.github.com" >> ~/.npmrc
                  cp ~/.npmrc ./.npmrc

            - name: Publish
              id: publish
              env:
                  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
                  PUBLISH_FIXED_VERSION: ${{ github.event.inputs.version || github.event.release.tag_name }}
                  PUBLISH_INCREMENT_VERSION: ${{ github.event.inputs.versionBump }}
              run: node tools/scripts/publish.js

            - name: Cleanup
              run: rm ./.npmrc

            - uses: stefanzweifel/git-auto-commit-action@v4
              with:
                  commit_message: '[Bot] Publish changes'
                  commit_user_name: Bookr
                  commit_user_email: <EMAIL>
                  commit_author: Bookr <<EMAIL>>

            - name: Slack Notification
              uses: rtCamp/action-slack-notify@v2
              env:
                  SLACK_CHANNEL: ci-cd
                  SLACK_COLOR: ${{ job.status }}
                  SLACK_ICON: https://github.com/bookr-technologies.png?size=48
                  SLACK_MESSAGE: Version ${{ steps.publish.outputs.version }} published to the the npm registry
                  SLACK_TITLE: New version of packages
                  SLACK_USERNAME: bookr-bot
                  SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
