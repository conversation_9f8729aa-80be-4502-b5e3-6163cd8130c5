name: Deploy dashboard - Staging

on:
    workflow_dispatch:
    push:
        branches:
            - main
        paths:
            - apps/dashboard/**/*
            - libs/**/*
            - .eslintrc.json
            - .prettierrc
            - babel.config.json
            - firebase.json
            - package.json
            - tsconfig.base.json
            - workspace.json

jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v2
              with:
                  ref: ${{ github.head_ref }}
                  fetch-depth: 0

            - name: Restore cache
              uses: actions/cache@v3
              with:
                  path: |
                      yarn.lock
                      node_modules
                  key: ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}
                  restore-keys: |
                      ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}

            - name: Dependency install
              run: yarn install --ignore-scripts --pure-lockfile --non-interactive

            - name: Build
              run: yarn deploy:dashboard:staging
              env:
                  CI: true
                  FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
