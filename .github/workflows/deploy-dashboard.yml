name: Dashboard deployment

on:
    workflow_dispatch:
        inputs:
            environment:
                description: Environment
                required: true
                type: choice
                options:
                    - production
                    - staging

jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout
              uses: actions/checkout@v2
              with:
                  ref: ${{ github.head_ref }}
                  fetch-depth: 0

            - name: Restore cache
              uses: actions/cache@v3
              with:
                  path: |
                      yarn.lock
                      node_modules
                  key: ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}
                  restore-keys: |
                      ${{ runner.os }}-yarn-v1-${{ hashFiles('yarn.lock') }}

            - name: Dependency install
              run: yarn install --ignore-scripts --pure-lockfile --non-interactive

            - name: Build
              run: yarn deploy:dashboard:${{ github.event.inputs.environment }}
              env:
                  CI: true
                  FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
