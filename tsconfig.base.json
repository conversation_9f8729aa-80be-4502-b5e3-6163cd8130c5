{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@bookr-technologies/api": ["libs/api/src/index.ts"], "@bookr-technologies/api/*": ["libs/api/src/*"], "@bookr-technologies/calendar-view": ["libs/calendar-view/src/index.ts"], "@bookr-technologies/core/*": ["libs/core/src/*"], "@bookr-technologies/env": ["libs/env/src/index.ts"], "@bookr-technologies/exports": ["libs/exports/src/index.ts"], "@bookr-technologies/hooks": ["libs/hooks/src/index.ts"], "@bookr-technologies/i18n": ["libs/i18n/src/index.ts"], "@bookr-technologies/store": ["libs/store/src/index.ts"], "@bookr-technologies/store/*": ["libs/store/src/*"], "@bookr-technologies/ui": ["libs/ui/src/index.ts"], "@bookr-technologies/ui/*": ["libs/ui/src/*"], "@mui/styled-engine": ["./node_modules/@mui/styled-engine-sc"]}}, "exclude": ["node_modules", "tmp"]}