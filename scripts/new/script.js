#!/usr/bin/env node

const yargs = require('yargs');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

const {
    _: [name, command],
} = yargs.argv;

const filename = name.replace(/:/g, '/');
const ext =
    path.extname(filename) ||
    {
        sh: '.sh',
        bash: '.sh',
        zsh: '.zsh',
        js: '.js',
        mjs: '.mjs',
        node: '.js',
        nodejs: '.js',
    }[command] ||
    '.sh';
const scriptPath = path.resolve(__dirname, `../${filename}`).split('.')[0] + ext;
const scriptName = filename.replace(/\//g, ':').split('.')[0];

const defaultCommand =
    {
        '.js': 'node',
        '.mjs': 'node',
        '.sh': 'bash',
        '.zsh': 'zsh',
    }[ext] || 'sh';

const pkgPath = path.resolve(__dirname, '../../package.json');
const pkg = JSON.parse(fs.readFileSync(pkgPath).toString());
pkg.scripts[scriptName] = 'scripty';

fs.writeFileSync(pkgPath, JSON.stringify(pkg, null, 4));
fs.mkdirSync(path.dirname(scriptPath), { recursive: true });
fs.writeFileSync(
    scriptPath,
    `#!/usr/bin/env ${command || defaultCommand}
`,
);

execSync(`sudo chmod u+rx '${scriptPath}'`);
