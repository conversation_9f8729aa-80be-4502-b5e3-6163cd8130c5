const config = require('@nrwl/react/plugins/bundle-rollup');
const fs = require('fs');
const glob = require('glob');

module.exports = (options) => {
    const newConfig = config(options);
    // noinspection JSCheckFunctionSignatures
    const inputStats = fs.lstatSync(newConfig.input);

    if (inputStats.isDirectory()) {
        const base = newConfig.input;
        newConfig.input = glob.sync(`${base}/**/*.ts`).reduce(
            (acc, file) => ({
                ...acc,
                [file.replace(base + '/', '').replace(/\.ts$/, '')]: file,
            }),
            {},
        );
    }

    newConfig.output.entryFileNames = newConfig.output.entryFileNames.replace(/\.(umd|cjs)\.js$/, '.js');
    newConfig.output.chunkFileNames = newConfig.output.chunkFileNames.replace(/\.(umd|cjs)\.js$/, '.js');

    return newConfig;
};
