const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const semver = require('semver');
const glob = require('glob');
const core = require('@actions/core');

const cwd = path.join(__dirname, '../..');
const tsc = path.join(cwd, './node_modules/.bin/tsc');
const publishFixedVersion = process.env.PUBLISH_FIXED_VERSION || 'auto';
const publishIncrementVersion = process.env.PUBLISH_INCREMENT_VERSION || 'patch';

function log(name, action, ...args) {
    console.log(`[${name}::${action}]`, ...args.flat());
}

async function version(name) {
    const packageJson = path.join(cwd, `./libs/${name}/package.json`);
    const pkg = JSON.parse(fs.readFileSync(packageJson, 'utf8').toString());
    let packageVersion = pkg.version;

    log(
        name,
        'version',
        [
            `Attempting to fetch version from:`,
            `Current version: ${packageVersion}`,
            `Declared version: ${publishFixedVersion}`,
            `Increment version: ${publishIncrementVersion}`,
        ].join('\n'),
    );

    if (publishFixedVersion === 'auto') {
        packageVersion = semver.inc(packageVersion, publishIncrementVersion);
    } else {
        packageVersion = semver.clean(publishFixedVersion);
    }

    pkg.version = packageVersion;
    log(name, 'version', 'New version:', packageVersion);
    log(name, 'publish', 'Write to new version to package.json');
    fs.writeFileSync(packageJson, JSON.stringify(pkg, null, 4) + '\n');

    core.setOutput('version', packageVersion);
}

async function processPackage(name) {
    const packageJson = path.join(cwd, `./dist/libs/${name}/package.json`);
    const pkg = JSON.parse(fs.readFileSync(packageJson, 'utf8').toString());

    pkg.main = './index.js';
    pkg.typings = './';
    pkg.dependencies = {
        ...pkg.peerDependencies,
        ...pkg.dependencies,
    };

    pkg.peerDependencies = {};

    log(name, 'publish', 'Write to new package.json contents');
    fs.writeFileSync(packageJson, JSON.stringify(pkg, null, 4) + '\n');
}

async function build(name) {
    log(name, 'build', 'Building package...');

    await version(name);

    await new Promise((resolve, reject) => {
        const proc = exec(
            `yarn nx run ${name}:build`,
            {
                cwd,
                env: {
                    ...process.env,
                    FORCE_COLOR: '1',
                },
            },
            (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            },
        );

        proc.stdout.pipe(process.stdout);
        proc.stderr.pipe(process.stderr);
    });

    log(name, 'build', 'Build complete');
}

async function declarations(name) {
    log(name, 'build', 'Building declarations...');
    await new Promise((resolve, reject) => {
        const proc = exec(`${tsc} -p ./libs/${name}/tsconfig.lib.json --emitDeclarationOnly`, { cwd }, (err) => {
            if (err) {
                reject(err);
            } else {
                resolve();
            }
        });

        proc.stdout.pipe(process.stdout);
        proc.stderr.pipe(process.stderr);
    });

    const base = path.join(cwd, `./dist/out-tsc/libs/${name}/src`);

    await Promise.all(
        glob.sync(path.join(base, `./**/*.d.ts`)).map((file) => {
            const destination = path.join(cwd, `./dist/libs/${name}/${file.replace(base, '')}`);

            return new Promise((resolve, reject) => {
                log(name, 'copy', [path.relative(cwd, file), ' -> ', path.relative(cwd, destination)]);
                fs.mkdirSync(path.dirname(destination), { recursive: true });
                fs.copyFile(path.join(file), destination, (err) => {
                    if (err) {
                        reject(err);
                    } else {
                        resolve();
                    }
                });
            });
        }),
    );
}

async function publish(name) {
    log(name, 'publish', 'Publishing package...');

    const npmRc = path.join(cwd, `./.npmrc`);
    const destNpmRc = path.join(cwd, `./dist/libs/${name}/.npmrc`);
    log(name, 'copy', [path.relative(cwd, npmRc), ' -> ', path.relative(cwd, destNpmRc)]);
    fs.copyFileSync(npmRc, destNpmRc);

    await processPackage(name);

    log(name, 'publish', 'Publishing package...');
    await new Promise((resolve, reject) => {
        const proc = exec(
            `npm publish`,
            {
                cwd: path.join(cwd, `./dist/libs/${name}`),
            },
            (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            },
        );

        proc.stdout.pipe(process.stdout);
        proc.stderr.pipe(process.stderr);
    });

    log(name, 'publish', 'Publishing Complete.');
}

async function sync(items, action) {
    const itemsCopy = [...items];

    while (itemsCopy.length > 0) {
        await action(itemsCopy.shift());
    }
}

async function main() {
    console.log('Running publish script... [%s]', cwd);

    const libs = ['core', 'env', 'api'];
    await sync(libs, build);
    await sync(libs, declarations);
    await Promise.all(libs.map(publish));
}

main()
    .then(() => {
        fs.rmSync(path.join(cwd, `./dist/out-tsc`), { recursive: true });
        console.log('Publish script completed.');
    })
    .catch((err) => {
        console.error('Publish script failed.');
        console.error(err);
        process.exit(1);
    });
