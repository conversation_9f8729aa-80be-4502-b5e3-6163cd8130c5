{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nrwl/nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"no-restricted-imports": ["error", {"paths": [{"name": "@mui/material", "message": "Please use `import {package} from '@mui/material/[package]'` instead."}, {"name": "@mui/lab", "message": "Please use `import {package} from '@mui/lab/[package]'` instead."}, {"name": "@mui/icons-material", "message": "Please use `import {package} from '@mui/icons-material/[package]'` instead."}]}], "@nrwl/nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nrwl/nx/typescript"], "rules": {}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nrwl/nx/javascript"], "rules": {}}]}