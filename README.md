# BOOKR

[![Deploy dashboard - Staging](https://github.com/bookr-technologies/bookr/actions/workflows/deploy-dashboard-staging.yml/badge.svg)](https://github.com/bookr-technologies/bookr/actions/workflows/deploy-dashboard-staging.yml)

This project was generated using [Nx](https://nx.dev).

## Generate a library

Run `nx g @nrwl/react:lib my-lib` to generate a library.

> You can also use any of the plugins above to generate libraries as well.

Libraries are shareable across libraries and applications. They can be imported from `@bookr-technologies/mylib`.

## Development server

Run `yarn start my-app` for a dev server. Navigate to http://localhost:4200/. The app will automatically reload if you
change any of the source files.

## Code scaffolding

Run `nx g @nrwl/react:component my-component --project=my-app` to generate a new component.

## Build

Run `yarn build my-app` to build the project. The build artifacts will be stored in the `dist/` directory. Use the
`--prod` flag for a production build or `--configuration staging` for a staging build.

## Running unit tests

Run `nx test my-app` to execute the unit tests via [Jest](https://jestjs.io).

Run `nx affected:test` to execute the unit tests affected by a change.

## Running end-to-end tests

Run `nx e2e my-app` to execute the end-to-end tests via [Cypress](https://www.cypress.io).

Run `nx affected:e2e` to execute the end-to-end tests affected by a change.

## Understand your workspace

Run `nx dep-graph` to see a diagram of the dependencies of your projects.

## How to run the UI storybook?

`yarn nx run ui:storybook`

## Manage the configurations

Each application has its own configuration, which is usually found at `apps/my-app/environments/environment.ts`, but we
also have global configurations, which could be found at `libs/env/src/lib/config.ts`.

Based on the stage, the application will load the right configuration for us (e.g. `*.production.ts` or `*.staging.ts`).

Usage:

```
// Using global configuration
import { env } from '@bookr-technologies/env';

console.log(env('app.pixelId'))

// Using application configuration
import environment from '../environments/environment';

console.log(environment.property);

// AVOID DOING THIS
import environment from '../environments/environment.staging';

console.log(environment.property);
```

**NOTE: The replacement of the file will be done automatically by the compiler.**

## Further help

Visit the [Nx Documentation](https://nx.dev) to learn more.
