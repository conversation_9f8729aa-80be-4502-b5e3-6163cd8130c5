{"tabWidth": 4, "printWidth": 120, "semi": true, "singleQuote": true, "jsxSingleQuote": false, "trailingComma": "all", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "proseWrap": "always", "importOrder": ["^@fullcalendar/react", "^@fullcalendar/(.*)$", "^@bookr-technologies/(.*)$", "^[./]"], "importOrderSeparation": false, "overrides": [{"files": ["*.js", "*.jsx"], "options": {"parser": "babel-flow"}}]}