{"npmScope": "bookr-technologies", "affected": {"defaultBase": "main"}, "implicitDependencies": {"package.json": {"dependencies": "*", "devDependencies": "*"}, ".eslintrc.json": "*"}, "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint", "test", "e2e", "build-storybook"]}}}, "generators": {"@nrwl/react": {"application": {"style": "none", "linter": "eslint", "babel": true}, "component": {"style": "none"}, "library": {"style": "none", "linter": "eslint"}}, "@nrwl/next": {"application": {"style": "css", "linter": "eslint"}}}, "defaultProject": "dashboard", "$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"build": {"dependsOn": ["^build"]}}}