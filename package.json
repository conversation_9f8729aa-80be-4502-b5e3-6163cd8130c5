{"name": "bookr-technologies", "version": "0.0.0", "license": "MIT", "scripts": {"start": "nx serve", "build": "nx build", "test": "nx test", "deploy:dashboard:production": "nx run dashboard:build:production && npx firebase deploy --only hosting:dashboard-bookr-production", "deploy:client:production": "nx run client:build:production && npx firebase deploy --only hosting:client-bookr-production", "deploy:dashboard:staging": "nx run dashboard:build:staging && npx firebase deploy --only hosting:dashboard-bookr-staging", "deploy:client:staging": "nx run client:build:staging && npx firebase deploy --only hosting:client-bookr-staging", "dep:upgrade": "npx npm-check-updates -i", "new:lib": "scripty", "new:script": "scripty", "lib:publish": "scripty", "lib:version": "scripty"}, "private": true, "workspaces": ["libs/**", "apps/**"], "dependencies": {"@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/resource-timegrid": "^5.11.3", "@fullcalendar/timegrid": "^5.11.3", "@googlemaps/js-api-loader": "^1.14.3", "@googlemaps/react-wrapper": "^1.1.35", "@mui/icons-material": "5.10.3", "@mui/lab": "5.0.0-alpha.99", "@mui/material": "5.10.5", "@mui/styled-engine": "npm:@mui/styled-engine-sc@5.10.3", "@mui/styles": "^5.10.3", "@mui/x-data-grid": "^5.17.2", "@mui/x-date-pickers": "^5.0.1", "@prismicio/client": "^6.7.1", "@reduxjs/toolkit": "1.8.5", "@storybook/core-server": "6.5.11", "async": "^3.2.4", "awesome-phonenumber": "^3.3.0", "axios": "^0.27.2", "classnames": "^2.3.2", "core-js": "^3.25.1", "firebase": "^10.7.1", "formik": "^2.2.9", "google-map-react": "^2.2.0", "i18next": "^21.9.1", "invert-color": "^2.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.37", "notistack": "^2.0.5", "polished": "^4.2.2", "prismic-reactjs": "^1.3.4", "pure-react-carousel": "^1.29.0", "query-string": "^7.1.1", "react": "17.0.2", "react-calendar": "^3.8.0", "react-color": "^2.19.3", "react-cookie-consent": "^8.0.1", "react-custom-events": "^1.1.1", "react-dates": "^21.8.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "17.0.2", "react-facebook-pixel": "^1.0.4", "react-firebaseui": "^6.0.0", "react-helmet": "^6.1.0", "react-i18next": "^11.18.6", "react-is": "18.2.0", "react-number-format": "^4.9.4", "react-query": "^3.39.2", "react-redux": "8.0.2", "react-router-dom": "6.4.1", "react-slick": "^0.29.0", "react-socks": "^2.2.0", "react-window": "^1.8.7", "recharts": "^2.1.14", "regenerator-runtime": "0.13.9", "styled-components": "5.3.5", "tslib": "^2.4.0", "yup": "^0.32.11", "zustand": "^4.1.1"}, "devDependencies": {"@actions/core": "^1.9.1", "@babel/core": "7.19.0", "@babel/preset-typescript": "7.18.6", "@nrwl/cli": "14.7.13", "@nrwl/cypress": "14.7.13", "@nrwl/eslint-plugin-nx": "14.7.13", "@nrwl/jest": "14.7.13", "@nrwl/linter": "14.7.13", "@nrwl/react": "14.7.13", "@nrwl/storybook": "14.7.13", "@nrwl/web": "14.7.13", "@nrwl/workspace": "14.7.13", "@storybook/addon-essentials": "6.5.11", "@storybook/builder-webpack5": "6.5.11", "@storybook/manager-webpack5": "6.5.11", "@storybook/react": "6.5.11", "@svgr/webpack": "^6.3.1", "@swc/core": "^1.3.0", "@swc/jest": "^0.2.22", "@testing-library/react": "13.4.0", "@trivago/prettier-plugin-sort-imports": "^3.3.0", "@types/async": "^3.2.15", "@types/google-map-react": "^2.1.7", "@types/google.maps": "^3.50.1", "@types/jest": "29.0.2", "@types/lodash": "^4.14.185", "@types/node": "18.7.17", "@types/react": "17.0.50", "@types/react-calendar": "^3.5.2", "@types/react-color": "^3.0.6", "@types/react-dates": "^21.8.3", "@types/react-dom": "17.0.17", "@types/react-helmet": "^6.1.5", "@types/react-is": "17.0.3", "@types/react-slick": "^0.23.10", "@types/react-window": "^1.8.5", "@types/styled-components": "5.1.26", "@typescript-eslint/eslint-plugin": "5.37.0", "@typescript-eslint/parser": "5.37.0", "babel-jest": "29.0.3", "babel-loader": "8.2.5", "babel-plugin-styled-components": "2.0.7", "cypress": "^10.8.0", "eslint": "8.22.0", "eslint-config-next": "12.3.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-react": "7.31.8", "eslint-plugin-react-hooks": "4.6.0", "firebase-tools": "^11.9.0", "glob": "^8.0.3", "jest": "29.0.3", "jest-environment-jsdom": "^29.0.3", "nx": "14.7.13", "prettier": "2.7.1", "react-test-renderer": "18.2.0", "scripty": "^2.1.1", "semver": "^7.3.7", "swc-loader": "^0.2.3", "ts-jest": "29.0.1", "ts-node": "10.9.1", "typescript": "4.8.3", "url-loader": "^4.1.1"}, "resolutions": {"@mui/styled-engine": "npm:@mui/styled-engine-sc@latest", "@types/react": "17.0.50", "@types/react-dom": "17.0.17"}}