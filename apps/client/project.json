{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/client/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/client", "index": "apps/client/src/index.html", "baseHref": "/", "main": "apps/client/src/main.tsx", "polyfills": "apps/client/src/polyfills.ts", "tsConfig": "apps/client/tsconfig.app.json", "assets": ["apps/client/src/assets", "apps/client/src/sitemap.xml", "apps/client/src/documents", "apps/client/src/assets/favicon/favicon.png", "apps/client/src/assets/favicon/android-chrome-192x192.png", "apps/client/src/assets/favicon/android-chrome-512x512.png", "apps/client/src/assets/favicon/apple-touch-icon.png", "apps/client/src/assets/favicon/favicon.ico", "apps/client/src/assets/favicon/favicon.png", "apps/client/src/assets/favicon/favicon-16x16.png", "apps/client/src/assets/favicon/favicon-32x32.png", "apps/client/src/assets/favicon/site.webmanifest"], "styles": [], "scripts": [], "webpackConfig": "@nrwl/react/plugins/webpack"}, "configurations": {"production": {"fileReplacements": [{"replace": "apps/client/src/environments/environment.ts", "with": "apps/client/src/environments/environment.production.ts"}, {"replace": "libs/env/src/lib/config.ts", "with": "libs/env/src/lib/config.production.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}, "staging": {"fileReplacements": [{"replace": "apps/client/src/environments/environment.ts", "with": "apps/client/src/environments/environment.staging.ts"}, {"replace": "libs/env/src/lib/config.ts", "with": "libs/env/src/lib/config.staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}, "development": {}}}, "serve": {"executor": "@nrwl/webpack:dev-server", "options": {"buildTarget": "client:build:development", "hmr": true, "allowedHosts": "bookr.test"}, "configurations": {"production": {"buildTarget": "client:build:production", "hmr": false}, "development": {"buildTarget": "client:build:development"}}, "defaultConfiguration": "development"}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/client/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/client"], "options": {"jestConfig": "apps/client/jest.config.ts", "passWithNoTests": true}}}, "tags": []}