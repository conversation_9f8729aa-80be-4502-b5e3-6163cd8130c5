{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}]}