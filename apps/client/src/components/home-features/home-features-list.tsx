import CheckIcon from '@mui/icons-material/Check';
import Typography from '@mui/material/Typography';
import React, { ReactNode } from 'react';
import classes from './home-features.module.scss';

interface HomeFeaturesListProps {
    items: ReactNode[];
}

export function HomeFeaturesList({ items }: HomeFeaturesListProps) {
    return (
        <div className={classes.sectionList}>
            {items.map((item, index) => (
                <div key={index} className={classes.sectionListItem}>
                    <div className={classes.sectionListIconHolder}>
                        <CheckIcon className={classes.sectionListIcon} />
                    </div>
                    <div className={classes.sectionListContent}>
                        <Typography variant={'subtitle1'} className={classes.sectionListContentTypography}>
                            {item}
                        </Typography>
                    </div>
                </div>
            ))}
        </div>
    );
}

export default HomeFeaturesList;
