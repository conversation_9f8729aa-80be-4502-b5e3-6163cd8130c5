@use '../../styles/breakpoints';

.root {
    background-color: #f6f6f6;
    padding: 120px 0 80px;
    @include breakpoints.down('sm') {
        padding-top: 48px;
    }
}

.heading {
    font-size: 44px;
    font-weight: 700;
    line-height: 60px;
    margin-bottom: 48px;
    font-family: 'Plus Jakarta Display', 'Poppins', sans-serif;
    @include breakpoints.down(1140) {
        font-size: 24px;
        line-height: 32px;
        margin-bottom: 48px;
    }
    @include breakpoints.down('xs') {
        br {
            display: none;
        }
    }
}

.section {
    position: relative;
    padding: 180px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    @include breakpoints.down(1140) {
        flex-direction: column-reverse;
        padding: 80px 0 0 0;
    }
}

.sectionBackgroundLayer {
    position: absolute;
    z-index: 1;
    top: 0;
    bottom: 0;
    width: 50%;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    @include breakpoints.down('xs') {
        width: 100%;
    }

    .sectionBackgroundLayerImage {
        max-width: 100%;
        object-fit: contain;

        @include breakpoints.down('xs') {
            object-fit: cover;
            min-height: 240px;
        }
    }

    &:global(.reversed) {
        right: auto;
        left: 0;
        justify-content: flex-end;
    }

    @include breakpoints.down(1140) {
        position: relative;
        margin-top: 64px;
    }
}

.sectionContainerRow {
    @include breakpoints.down(1140) {
        justify-content: center !important;
    }
}

.sectionContainerColumn {
    @include breakpoints.down(1140) {
        width: 66.66664%;
        max-width: 66.66664% !important;
        flex-basis: 66.66664%;
    }

    @include breakpoints.down('sm') {
        width: 100%;
        max-width: 100% !important;
        flex-basis: 100%;
    }
}

.sectionCaption {
    color: #2f80fb;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 6px;
    @include breakpoints.down(1140) {
        font-size: 14px;
    }
}

.sectionHeading {
    font-size: 32px;
    color: #111;
    font-weight: 700;
    line-height: 42px;
    margin-bottom: 24px;
    @include breakpoints.down(1140) {
        font-size: 24px;
        line-height: 32px;
    }

    @include breakpoints.down('xs') {
        br {
            display: none;
        }
    }
}

.sectionContent {
    max-width: 560px;
}

.sectionList {
    margin-top: 24px;
    padding-left: 34px;
}

.sectionListItem {
    position: relative;
    margin-bottom: 24px;
}

.sectionListIconHolder {
    width: 34px;
    height: 24px;
    position: absolute;
    top: 0;
    left: -34px;
}

.sectionListIcon {
    color: #2f80fb;
}

.sectionListContent {
    width: 100%;
}

.sectionListContentTypography {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    @include breakpoints.down(1140) {
        font-size: 14px;
    }
}
