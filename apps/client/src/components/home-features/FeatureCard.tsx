import Grid, { GridProps } from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';

const Root = styled(Grid)`
    display: flex;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
    border-radius: 20px;
    background-color: #fff;
    position: relative;
    overflow: hidden;
    flex: 1 1 auto;
    z-index: 1;
    &.inverted {
        background-color: #1f1f1f;
    }
`;

interface FeatureCardProps extends GridProps {
    inverted?: boolean;
}

export function FeatureCard({ inverted, className, ...rest }: FeatureCardProps) {
    return <Root container className={classNames('FeaturedCard-root', className, { inverted })} {...rest} />;
}

export const FeatureCardImage = styled(({ className, ...rest }) => (
    <img className={className + ' FeatureCardImage-root'} alt="Featured card" {...rest} />
))`
    & {
        position: relative;
        display: inline-flex;
        flex: 1 1 auto;
        height: auto;
        object-fit: contain;
        object-position: bottom;
        max-width: ${({ width = 0 }) => (width > 0 ? width + 'px' : '100%')};
        z-index: -1;
    }
`;
