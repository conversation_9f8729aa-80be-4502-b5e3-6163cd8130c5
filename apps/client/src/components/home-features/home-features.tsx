import AccessTimeIcon from '@mui/icons-material/AccessTime';
import ViewInArIcon from '@mui/icons-material/ViewInAr';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Tab from '@mui/material/Tab';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React, { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import analytics from '../../assets/home-features/analytics.jpg';
import dashboardApp from '../../assets/home-features/dashboard-application.jpg';
import easyToUse from '../../assets/home-features/easy-to-use.jpg';
import interaction from '../../assets/home-features/interaction.jpg';
import mobileApp from '../../assets/home-features/mobile-application.jpg';
import notifications from '../../assets/home-features/notifications.jpg';
import simpleAppointments from '../../assets/home-features/simple-appoiments.jpg';
import webapp from '../../assets/home-features/webapp-application.jpg';
import { ApplyNowButton } from '../ApplyNowButton';
import AppStoreButtons from '../app-store-buttons/app-store-buttons';
import { FeatureCard, FeatureCardImage } from './FeatureCard';
import classes from './home-features.module.scss';

const Root = styled('section')`
    .headline {
        font-family: 'Plus Jakarta Display', 'Poppins', sans-serif;
        font-size: 46px;
    }

    ${({ theme }) => theme.breakpoints.down('sm')} {
        & .headline {
            font-size: 26px;
            font-weight: 600;
        }

        & .subHeadline {
            font-size: 16px;
            line-height: 24px !important;
            font-weight: 500;
        },
    }

    .FeatureCardImage-root {
        max-width: 362px;
        &.mobileRight {
            margin-left: auto;
            margin-right: 0 !important;
        }
        ${({ theme }) => theme.breakpoints.down('sm')} {
            max-width: 240px !important;
            margin-left: auto;
            margin-right: auto;
        },
    },

    .HomeFeatures-simpleAppointmentsContent,
    .HomeFeatures-notificationsContent,
    .HomeFeatures-easyToUseContent,
    .HomeFeatures-tabContent {
        padding: 50px 40px 50px 50px;
        flex-grow: 1;
    }

    ${({ theme }) => theme.breakpoints.down('sm')} {
        .HomeFeatures-simpleAppointmentsContent,
        .HomeFeatures-notificationsContent,
        .HomeFeatures-easyToUseContent,
        .HomeFeatures-tabContent {
            padding: 32px 32px 0;

            .headline {
                font-size: 28px;
                margin-bottom: 14px;
            }

            .subHeadline {
                font-size: 16px;
            }
        }

        ${FeatureCardImage} {
            max-width: 100%;
        }
    }

    .HomeFeatures-easyToUse {
        ${FeatureCardImage} {
            ${({ theme }) => theme.breakpoints.down('sm')} {
                display: none;
            }
        }
    }

    .HomeFeatures-easyToUseContent {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;

        ${({ theme }) => theme.breakpoints.down('sm')} {
            padding-bottom: 32px;
        }
    }

    .HomeFeatures-button {
        border-radius: 20px;
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;
        padding: 16px 32px;
        min-width: 240px;
        margin-top: auto;
    }

    .HomeFeatures-analytics, .HomeFeatures-interaction, .HomeFeatures-access {
        background-color: #111;
    }

    .HomeFeatures-analytics {
        ${FeatureCardImage} {
            width: 100%;
        }
    }

    .HomeFeatures-analyticsContent,
    .HomeFeatures-interactionContent {
        padding: 16px 50px 50px 50px;
        ${({ theme }) => theme.breakpoints.down('sm')} {
            padding: 16px 32px 32px 32px;
        }
        .headline {
            color: #fff;
        }

        .subHeadline {
            color: #afafaf;
        }
    }

    .HomeFeatures-interactionContent {
        padding-top: 50px;
    }

    .HomeFeatures-statsContent {
        padding: 50px;
        ${({ theme }) => theme.breakpoints.down('sm')} {
            padding: 32px;
        }
    }

    .HomeFeatures-statsBars {
        padding: 0 50px 50px;
        ${({ theme }) => theme.breakpoints.down('sm')} {
            padding: 0 32px 32px;
        }
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        width: 100%;
    }

    .HomeFeatures-statsBar {
        height: 156px;
        position: relative;
        background-color: #eee;

        &,
        .HomeFeatures-statsBarInner {
            width: 36px;
            border-radius: 8px;
        }

        .HomeFeatures-statsBarInner {
            content: '';
            display: block;
            position: absolute;
            bottom: 0;
            left: 0;
            background-color: #2f80fb;
        }
    }

    .HomeFeatures-accessContent,
    .HomeFeatures-tourContent {
        padding: 50px;

        ${({ theme }) => theme.breakpoints.down('sm')} {
            padding: 32px;
        }

        .headline,
        .icon {
            color: #ff5858;
        }

        .subHeadline {
            color: #fff;
            line-height: 32px;
        }

        .icon {
            width: 54px;
            height: 54px;
            font-size: 54px;
        }
    }

    .HomeFeatures-tourContent {
        .headline {
            color: #111;
        }

        .subHeadline {
            color: #757575;
        }

        .icon {
            color: #2f80fb;
        }
    }

    .MuiTabPanel-root {
        padding: 0;
        margin-top: 40px;
    }

    .MuiTabs-root {
        background-color: #eee;
        border-radius: 16px;
        max-width: 540px;
        margin: 0 auto;

        .MuiTabs-indicator {
            display: none;
        }

        .MuiTabs-scroller {
            padding: 8px;
        }

        .MuiTab-root {
            width: 100%;
            flex: 1 1 auto;
            color: #111;
            font-weight: 500;
            font-size: 16px;
            line-height: 24px;
            text-transform: capitalize;
            border-radius: 9px;

            &.Mui-selected {
                background-color: #fff;
                box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
            }
        }
    }
`;

export function HomeFeatures() {
    const { t } = useTranslation('homepage');
    const [value, setValue] = React.useState('1');
    const isDownSm = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));
    const handleChange = useCallback((event: any, newValue: string) => setValue(newValue), []);

    return (
        <Root className={classes.root}>
            <Container maxWidth={'md'}>
                <Typography component={'h3'} className={classes.heading} align={'center'}>
                    <Trans t={t} i18nKey={'featuresTitle'} components={{ br: <br /> }} />
                </Typography>
            </Container>
            <Container maxWidth={'md'}>
                <FeatureCard className={'HomeFeatures-simpleAppointments'}>
                    <Grid item xs className={'HomeFeatures-simpleAppointmentsContent'}>
                        <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                            {t('feature1Title')}
                        </Typography>
                        <Typography
                            variant={'subtitle1'}
                            color={'textSecondary'}
                            fontSize={18}
                            className={'subHeadline'}
                        >
                            {t('feature1Description')}
                        </Typography>
                    </Grid>
                    <FeatureCardImage src={simpleAppointments} alt={t('feature1Title')} width={362} />
                </FeatureCard>

                <Grid container mt={1.5} spacing={2.5}>
                    <Grid item xs={12} sm={6} container>
                        <FeatureCard className={'HomeFeatures-analytics'} inverted>
                            <FeatureCardImage src={analytics} />
                            <Grid item xs className={'HomeFeatures-analyticsContent'} mt={2}>
                                <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                    {t('feature2Title')}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    color={'textSecondary'}
                                    fontSize={18}
                                    className={'subHeadline'}
                                >
                                    {t('feature2Description')}
                                </Typography>
                            </Grid>
                        </FeatureCard>
                    </Grid>

                    <Grid item xs={12} sm={6} container>
                        <FeatureCard className={'HomeFeatures-stats'}>
                            <Grid item xs className={'HomeFeatures-statsContent'}>
                                <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                    {t('feature3Title')}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    color={'textSecondary'}
                                    fontSize={18}
                                    className={'subHeadline'}
                                >
                                    {t('feature3Description')}
                                </Typography>
                            </Grid>

                            <Grid className={'HomeFeatures-statsBars'}>
                                <div className={'HomeFeatures-statsBar'}>
                                    <div className={'HomeFeatures-statsBarInner'} style={{ height: '25%' }} />
                                </div>
                                <div className={'HomeFeatures-statsBar'}>
                                    <div className={'HomeFeatures-statsBarInner'} style={{ height: '45%' }} />
                                </div>
                                <div className={'HomeFeatures-statsBar'}>
                                    <div className={'HomeFeatures-statsBarInner'} style={{ height: '100%' }} />
                                </div>
                                <div className={'HomeFeatures-statsBar'}>
                                    <div className={'HomeFeatures-statsBarInner'} style={{ height: '80%' }} />
                                </div>
                                <div className={'HomeFeatures-statsBar'}>
                                    <div className={'HomeFeatures-statsBarInner'} style={{ height: '50%' }} />
                                </div>
                            </Grid>
                        </FeatureCard>
                    </Grid>
                </Grid>

                <Grid container mt={4}>
                    <FeatureCard className={'HomeFeatures-notifications'}>
                        <Grid item xs className={'HomeFeatures-notificationsContent'}>
                            <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                {t('feature4Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                            >
                                {t('feature4Description')}
                            </Typography>
                        </Grid>
                        <FeatureCardImage src={notifications} alt={t('feature4Title')} width={360} />
                    </FeatureCard>
                </Grid>

                <Grid container mt={4}>
                    <FeatureCard inverted className={'HomeFeatures-interaction'}>
                        <Grid item xs className={'HomeFeatures-interactionContent'}>
                            <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                {t('feature5Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                            >
                                {t('feature5Description')}
                            </Typography>
                        </Grid>
                        <FeatureCardImage src={interaction} alt={'Notificări și Remindere'} width={360} />
                    </FeatureCard>
                </Grid>
            </Container>

            <Container maxWidth={'md'}>
                <Typography component={'h3'} className={classes.heading} align={'center'} mt={15}>
                    <Trans t={t} i18nKey={'digitalizeBusiness'} components={{ br: <br /> }} />
                </Typography>
            </Container>

            <Container maxWidth={'md'}>
                <Grid container mt={4}>
                    <FeatureCard className={'HomeFeatures-easyToUse'}>
                        <Grid item xs container className={'HomeFeatures-easyToUseContent'}>
                            <Grid container direction={'column'} mb={3}>
                                <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                    {t('feature6Title')}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    color={'textSecondary'}
                                    fontSize={18}
                                    className={'subHeadline'}
                                >
                                    {t('feature6Description')}
                                </Typography>
                            </Grid>
                            <ApplyNowButton className={'HomeFeatures-button'} variant={'contained'} color={'inherit'}>
                                {t('startNow')}
                            </ApplyNowButton>
                        </Grid>
                        <FeatureCardImage src={easyToUse} alt={'Ușor de utilizat'} width={360} />
                    </FeatureCard>
                </Grid>

                <Grid container mt={1.5} spacing={2.5}>
                    <Grid item xs={12} sm={6} container>
                        <FeatureCard className={'HomeFeatures-access'} inverted>
                            <Grid item xs className={'HomeFeatures-accessContent'} mt={2}>
                                <AccessTimeIcon className={'icon'} />
                                <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                    {t('feature7Title')}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    fontSize={24}
                                    fontWeight={600}
                                    className={'subHeadline'}
                                >
                                    {t('feature7Description')}
                                </Typography>
                            </Grid>
                        </FeatureCard>
                    </Grid>

                    <Grid item xs={12} sm={6} container>
                        <FeatureCard className={'HomeFeatures-tour'}>
                            <Grid item xs className={'HomeFeatures-tourContent'}>
                                <ViewInArIcon className="icon" />
                                <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                    {t('feature8Title')}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    fontSize={24}
                                    fontWeight={600}
                                    className={'subHeadline'}
                                    color={'secondary'}
                                >
                                    {t('feature8Description')}
                                </Typography>
                            </Grid>
                        </FeatureCard>
                    </Grid>
                </Grid>
            </Container>

            <Container maxWidth={'md'}>
                <Typography component={'h3'} className={classes.heading} align={'center'} mt={15}>
                    {t('digitalWaysManageBusinessTitle')}
                </Typography>
            </Container>

            <Container maxWidth={'md'}>
                <TabContext value={value}>
                    <TabList onChange={handleChange} aria-label="Ways to use BOOKR">
                        <Tab label="Mobile" value="1" />
                        <Tab label="Webpage" value="2" />
                        <Tab label="Dashboard" value="3" />
                    </TabList>
                    <TabPanel value="1">
                        <FeatureCard className={'HomeFeatures-tab'}>
                            <Grid item xs container className={'HomeFeatures-tabContent'}>
                                <Grid container direction={'column'}>
                                    <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                        {t('feature9Title')}
                                    </Typography>
                                    <Typography
                                        variant={'subtitle1'}
                                        color={'textSecondary'}
                                        fontSize={18}
                                        className={'subHeadline'}
                                    >
                                        {t('feature9Description')}
                                    </Typography>
                                </Grid>
                                <AppStoreButtons justifyContent={'flex-start'} mt={isDownSm ? 3 : 'auto'} />
                            </Grid>
                            <FeatureCardImage src={mobileApp} alt={t('feature9Title')} width={400} />
                        </FeatureCard>
                    </TabPanel>
                    <TabPanel value="2">
                        <FeatureCard className={'HomeFeatures-tab'}>
                            <Grid item xs className={'HomeFeatures-tabContent'}>
                                <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                    {t('feature10Title')}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    color={'textSecondary'}
                                    fontSize={18}
                                    className={'subHeadline'}
                                >
                                    {t('feature10Description')}
                                </Typography>
                            </Grid>
                            <FeatureCardImage src={webapp} alt={'BOOKR Web'} className={'mobileRight'} />
                        </FeatureCard>
                    </TabPanel>
                    <TabPanel value="3">
                        <FeatureCard className={'HomeFeatures-tab'}>
                            <Grid item xs container className={'HomeFeatures-tabContent'}>
                                <Grid container direction={'column'}>
                                    <Typography variant={'h3'} fontWeight={700} mb={3} className={'headline'}>
                                        {t('feature11Title')}
                                    </Typography>
                                    <Typography
                                        variant={'subtitle1'}
                                        color={'textSecondary'}
                                        fontSize={18}
                                        className={'subHeadline'}
                                        mb={2}
                                    >
                                        {t('feature11Description')}
                                    </Typography>
                                </Grid>
                                <ApplyNowButton
                                    className={'HomeFeatures-button'}
                                    variant={'contained'}
                                    color={'secondary'}
                                >
                                    {t('learnMore')}
                                </ApplyNowButton>
                            </Grid>
                            <FeatureCardImage src={dashboardApp} alt={'BOOKR Dashboard'} className={'mobileRight'} />
                        </FeatureCard>
                    </TabPanel>
                </TabContext>
            </Container>
        </Root>
    );
}

export default HomeFeatures;
