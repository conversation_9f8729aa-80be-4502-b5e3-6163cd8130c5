import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import classNames from 'classnames';
import React, { PropsWithChildren, ReactNode } from 'react';
import classes from './home-features.module.scss';

interface HomeFeaturesSectionProps {
    caption: ReactNode;
    heading: ReactNode;
    image: string;
    reversed?: boolean;
}

export function HomeFeaturesSection({
    caption,
    heading,
    image,
    reversed,
    children,
}: PropsWithChildren<HomeFeaturesSectionProps>) {
    return (
        <section className={classNames(classes.section, { reversed })}>
            <div className={classNames(classes.sectionBackgroundLayer, { reversed })}>
                <img src={image} alt={'Section background'} className={classes.sectionBackgroundLayerImage} />
            </div>
            <Container className={classes.sectionContainer}>
                <Grid
                    container
                    justifyContent={reversed ? 'flex-end' : 'flex-start'}
                    className={classes.sectionContainerRow}
                >
                    <Grid
                        item
                        container
                        xs={6}
                        direction={'column'}
                        justifyContent={'center'}
                        alignItems={'flex-start'}
                        className={classes.sectionContainerColumn}
                    >
                        <Typography variant={'button'} className={classes.sectionCaption}>
                            {caption}
                        </Typography>
                        <Typography variant={'h4'} component={'h3'} className={classes.sectionHeading}>
                            {heading}
                        </Typography>
                        <div className={classes.sectionContent}>{children}</div>
                    </Grid>
                </Grid>
            </Container>
        </section>
    );
}

export default HomeFeaturesSection;
