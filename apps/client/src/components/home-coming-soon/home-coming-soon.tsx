import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import applicationPreview from '../../assets/application-preview.png';
import classes from './home-coming-soon.module.scss';

export function HomeComingSoon() {
    return (
        <section className={classes.root}>
            <div className={classes.backgroundLayer}>
                <img src={applicationPreview} alt={'Application preview'} className={classes.backgroundLayerImage} />
            </div>
            <Container>
                <Grid container justifyContent={'flex-start'}>
                    <Grid
                        item
                        container
                        xs={12}
                        sm={8}
                        md={6}
                        direction={'column'}
                        justifyContent={'center'}
                        alignItems={'flex-start'}
                    >
                        <Typography variant={'button'} className={classes.caption}>
                            În curând și pe web...
                        </Typography>
                        <Typography variant={'h4'} component={'h3'} className={classes.heading}>
                            Tot ce vezi acum în aplicația mobilă plus de{' '}
                            <span className={classes.textGradient}>3 ori mai mult!</span>
                        </Typography>
                        <Typography variant={'body1'} className={classes.subheading}>
                            <b>BOOKR Dashboard</b> iți va oferi de 3 ori mai multă claritate asupra business-ului tau,
                            punându-ți la dispoziție funcționalități avansate, ce pot fi accesate de pe desktop.
                        </Typography>
                    </Grid>
                </Grid>
            </Container>
        </section>
    );
}

export default HomeComingSoon;
