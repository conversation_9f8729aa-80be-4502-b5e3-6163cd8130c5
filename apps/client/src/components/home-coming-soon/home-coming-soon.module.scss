@use '../../styles/breakpoints';

.root {
    background-color: #fff;
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    @include breakpoints.down('md') {
        min-height: 0;
        padding: 180px 0;
    }
    @include breakpoints.down('sm') {
        flex-direction: column-reverse;
        align-items: flex-end;
        padding: 96px 0 0;
    }
}

.backgroundLayer {
    position: absolute;
    z-index: 1;
    top: 0;
    bottom: 0;
    width: 50%;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .backgroundLayerImage {
        max-width: 100%;
        object-fit: contain;
    }

    @include breakpoints.down('sm') {
        position: relative;
    }
}

.caption {
    background-color: #2f80fb;
    color: #fff;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    display: inline-flex;
    padding: 4px 14px;
    border-radius: 12px;
    @include breakpoints.down('sm') {
        font-size: 12px;
        line-height: 20px;
    }
}
.heading {
    font-size: 38px;
    font-weight: 700;
    line-height: 54px;
    margin-top: 4px;
    margin-bottom: 36px;
    @include breakpoints.down('sm') {
        margin-top: 14px;
        font-size: 32px;
        line-height: 42px;
    }
}
.subheading {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #111;
    @include breakpoints.down('sm') {
        font-size: 14px;
        margin-bottom: 48px;
    }
}

.textGradient {
    border-radius: 14px;
    padding: 0 14px;
    color: #fff;
    white-space: nowrap;
    background-color: #2f80fb;
    background: linear-gradient(114.28deg, #29e9f5 -4.86%, #7a64ff 14.73%, #ff508b 58.64%, #fd6d53 76.88%);
}
