import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Typography from '@mui/material/Typography';
import classNames from 'classnames';
import React from 'react';
import { BlogPostEntity } from '../../../entity/blog-post.entity';
import { useAllBlogPosts, useBlog } from '../../../redux/selectors/use-blog-post';
import { Loader } from '../../loader/Loader';
import { BlogPostTile } from '../blog-post-tile';
import classes from './blog-slice-all-blog-posts.module.scss';

const ALL_POST_NAVIGATION_KEY = 'all';

export function BlogSliceAllBlogPosts() {
    const [activeTab, setActiveTab] = React.useState('all');

    const blogData = useBlog();
    const allBlogPosts: Record<string, BlogPostEntity> = useAllBlogPosts();

    const handleChange = (event: any, newValue: string) => {
        setActiveTab(newValue);
    };
    if (!blogData.data || !Object.keys(allBlogPosts).length) {
        return <Loader />;
    }

    const navigation = [
        ALL_POST_NAVIGATION_KEY,
        ...blogData.data.navigation.map((entry: { category: { uid: any } }) => entry.category.uid),
    ];
    const articleTiles = Object.keys(allBlogPosts)
        .map((key) => {
            const hasCategory =
                allBlogPosts[key].data.main_category.uid === activeTab || activeTab === ALL_POST_NAVIGATION_KEY;
            return hasCategory && <BlogPostTile data={allBlogPosts[key]} key={key} />;
        })
        .filter(Boolean);

    return (
        <section className={classes.root}>
            <Container>
                <Tabs
                    value={activeTab}
                    variant="scrollable"
                    scrollButtons="auto"
                    onChange={handleChange}
                    TabIndicatorProps={{
                        style: {
                            backgroundColor: '#2F80FB',
                            height: '100%',
                            borderRadius: '20px',
                            color: 'ffffff',
                        },
                    }}
                    className={classes.navigation}
                >
                    {navigation.map((key) => (
                        <Tab
                            key={key}
                            value={key}
                            label={key}
                            className={classNames(classes.navigationItems, activeTab === key && classes.active)}
                        />
                    ))}
                </Tabs>
            </Container>
            <Container className={classes.posts}>
                {articleTiles.length === 0 ? (
                    <Typography className={classes.noPostsMessage} variant={'body2'}>
                        Acesta sectiune nu are postari momentan, dar poti verifica celelate categorii. {'\ud83c\udf89'}
                    </Typography>
                ) : (
                    <Grid container spacing={2} direction="row" justifyContent="flex-start" alignItems="flex-start">
                        {articleTiles}
                    </Grid>
                )}
            </Container>
        </section>
    );
}

export default BlogSliceAllBlogPosts;
