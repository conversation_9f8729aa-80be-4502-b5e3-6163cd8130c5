import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { BlogSliceCtaDefaultButtonEntity } from '../../../entity/blog-slice-cta-default-button.entity';
import classes from '../blog-slice-download-cta/blog-slice-download-cta.module.scss';

interface BlogSliceCallToActionProps {
    data: BlogSliceCtaDefaultButtonEntity;
    key: any;
}

export function BlogSliceCallToAction({ data }: BlogSliceCallToActionProps) {
    const titleRows: any[] = [];
    data.primary.title.reduce((agg, value, index) => {
        value.text && agg.push(value.text) && agg.push(<br key={index} />);
        return agg;
    }, titleRows);

    const descriptionRows: any[] = [];
    data.primary.paragraph.reduce((agg, value, index) => {
        value.text &&
            agg.push(
                <Typography align={'center'} variant={'h5'} component={'p'} className={classes.subheading} key={index}>
                    {' '}
                    {value.text}{' '}
                </Typography>,
            );
        return agg;
    }, descriptionRows);

    return (
        <section className={classes.root}>
            <Container>
                <Grid container alignItems={'center'} justifyContent={'center'} flexDirection={'column'}>
                    <Typography align={'center'} variant={'h2'} component={'h3'} className={classes.heading}>
                        {titleRows}
                    </Typography>
                    {descriptionRows}
                    <br />
                    <ThemeProvider>
                        <Button
                            variant={'contained'}
                            color={'accent'}
                            href={data.primary.button_link.url}
                            target={'_blank'}
                        >
                            {data.primary.button_label}
                        </Button>
                    </ThemeProvider>
                </Grid>
            </Container>
        </section>
    );
}

export default BlogSliceCallToAction;
