import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import { RichText } from 'prismic-reactjs';
import React from 'react';
import { BlogPostEntity } from '../../../entity/blog-slice-section.entity';
import classes from './blog-slice-section.module.scss';

interface BlogSliceSectionProps {
    data: BlogPostEntity;
}

export function BlogSliceSection({ data }: BlogSliceSectionProps) {
    return (
        <Container className={classes.sectionContainer}>
            <Grid container>
                {data.primary.paragraph_poster?.url && (
                    <img src={data.primary.paragraph_poster.url} className={classes.poster} alt={'Post'} />
                )}
            </Grid>

            <Grid container className={'rich-text-content'}>
                <RichText render={data.primary.section_content} />
            </Grid>
        </Container>
    );
}

export default BlogSliceSection;
