import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import Card from '@mui/material/Card';
import CardActionArea from '@mui/material/CardActionArea';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { Link } from 'react-router-dom';
import { BlogPostEntity } from '../../../entity/blog-post.entity';
import classes from './blog-post-tile.module.scss';

interface BlogPostTileProps {
    data: BlogPostEntity;
}

export function BlogPostTile({ data }: BlogPostTileProps) {
    const thumbnailImage = data.data.thumbnail_image;
    const urlPost = `/blog/${data.uid}`;
    return (
        <Grid container item sm={12} md={4}>
            <Link to={urlPost} className={classes.tileLink}>
                <Card classes={{ root: classes.card }}>
                    <CardActionArea>
                        <CardMedia component="img" height="200" image={thumbnailImage.url} alt={thumbnailImage.alt} />
                        <CardContent
                            classes={{
                                root: classes.content,
                            }}
                        >
                            <Typography gutterBottom variant="h5" component="span" className={classes.category}>
                                {data.data.main_category.uid}
                            </Typography>
                            <Typography component="h3" className={classes.title}>
                                {data.data.preview_title}
                            </Typography>
                            <Typography variant="body2" component="p" className={classes.description}>
                                {data.data.preview_description}
                            </Typography>
                            <span className={classes.readMoreLink}>
                                Citește articolul <ChevronRightIcon height={12} />
                            </span>
                        </CardContent>
                    </CardActionArea>
                </Card>
            </Link>
        </Grid>
    );
}

export default BlogPostTile;
