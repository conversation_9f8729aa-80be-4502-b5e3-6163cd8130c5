.card {
    border-radius: 20px;
}

.category {
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 600;
    line-height: 24px;
    color: #757575;
}

.title {
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
}

.content {
    min-height: 240px;
}

.description {
    max-height: 72px;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #757575;
    line-height: 24px;
}

.readMoreLink {
    margin: 15px 0 0;
    display: flex;
    font-weight: 600;
    font-size: 14px;
    color: #2f80fb;
    line-height: 24px;
    text-decoration: none;
    position: absolute;
    bottom: 20px;
}

.tileLink {
    text-decoration: none;
}
