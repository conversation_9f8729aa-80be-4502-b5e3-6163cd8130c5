import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import classNames from 'classnames';
import { RichText } from 'prismic-reactjs';
import React from 'react';
import { BlogPostDownloadCtaEntity } from '../../../entity/blog-slice-alternative-layout.entity';
import classes from './blog-slice-alternative-grid.module.scss';

interface BlogSliceAlternativeGridProps {
    data: BlogPostDownloadCtaEntity;
}

export function BlogSliceAlternativeGrid({ data }: BlogSliceAlternativeGridProps) {
    return (
        <section className={classes.section}>
            <div className={classes.sectionBackgroundLayer}>
                <img
                    src={data.primary.optional_image.url}
                    alt={'Section background'}
                    className={classes.sectionBackgroundLayerImage}
                />
            </div>
            <Container className={classes.sectionContainer}>
                <Grid container className={classes.sectionContainerRow}>
                    <Grid
                        item
                        container
                        xs={6}
                        direction={'column'}
                        justifyContent={'center'}
                        alignItems={'flex-start'}
                        className={classes.sectionContainerColumn}
                    >
                        <Typography variant={'h1'} component={'h1'} className={classes.sectionHeading}>
                            <b className={classes.sectionHeadingBold}>BOOKR</b> Blog
                        </Typography>
                        <div className={classNames(classes.sectionContent, 'rich-text-content')}>
                            <RichText render={data.primary.description} />
                        </div>
                    </Grid>
                </Grid>
            </Container>
        </section>
    );
}

export default BlogSliceAlternativeGrid;
