import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { BlogPostDownloadCtaEntity } from '../../../entity/blog-slice-download-cta.entity';
import AppStoreButtons from '../../app-store-buttons/app-store-buttons';
import classes from './blog-slice-download-cta.module.scss';

interface BlogSliceDownloadCtaProps {
    data: BlogPostDownloadCtaEntity;
    key: any;
}

export function BlogSliceDownloadCta({ data }: BlogSliceDownloadCtaProps) {
    const titleRows: any[] = [];
    data.primary.download_title.reduce((agg, value, index) => {
        value.text && agg.push(value.text) && agg.push(<br key={index} />);
        return agg;
    }, titleRows);

    const descriptionRows: any[] = [];
    data.primary.download_description.reduce((agg, value, index) => {
        value.text &&
            agg.push(
                <Typography align={'center'} variant={'h5'} component={'p'} className={classes.subheading} key={index}>
                    {' '}
                    {value.text}{' '}
                </Typography>,
            );
        return agg;
    }, descriptionRows);

    return (
        <section className={classes.root}>
            <Container>
                <Grid container alignItems={'center'} justifyContent={'center'}>
                    <Typography align={'center'} variant={'h2'} component={'h3'} className={classes.heading}>
                        {titleRows}
                    </Typography>
                    {descriptionRows}
                    <AppStoreButtons className={classes.buttons} />
                </Grid>
            </Container>
        </section>
    );
}

export default BlogSliceDownloadCta;
