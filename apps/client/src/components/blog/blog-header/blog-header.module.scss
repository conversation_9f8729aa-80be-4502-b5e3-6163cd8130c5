@use '../../../styles/breakpoints';

.root {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    @include breakpoints.down('sm') {
        min-height: 0;
    }
}
.appBar {
    padding: 24px 24px 36px;
    z-index: 3;
    display: flex;
    justify-content: space-between;
    @include breakpoints.down('sm') {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.brand {
    display: block;
}

.brandImage {
    vertical-align: middle;
    display: block;
    height: 28px;
    margin-top: 12px;
    width: auto;
}

.storeButtons {
    @include breakpoints.down('sm') {
        display: none;
    }
}
