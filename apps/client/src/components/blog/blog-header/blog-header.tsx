import Container from '@mui/material/Container';
import React from 'react';
import darkLogo from '../../../assets/dark-logo.png';
import AppStoreButtons from '../../app-store-buttons/app-store-buttons';
import classes from './blog-header.module.scss';

interface HomeHeaderProps {
    displayDownloadApp?: boolean;
}
export function BlogHeader({ displayDownloadApp }: HomeHeaderProps) {
    return (
        <header className={classes.root}>
            <Container className={classes.appBar}>
                <a href={'/'} className={classes.brand}>
                    <img src={darkLogo} alt="Bookr" className={classes.brandImage} />
                </a>
                {displayDownloadApp && <AppStoreButtons className={classes.storeButtons} justifyContent={'flex-end'} />}
            </Container>
        </header>
    );
}

export default BlogHeader;
