import React from 'react';
import { BlogPostEntity } from '../../../entity/blog-post.entity';
import { BlogEntity } from '../../../entity/blog.entity';
import { BlogSliceAllBlogPosts } from '../blog-slice-all-blog-posts';
import { BlogSliceAlternativeGrid } from '../blog-slice-alternative-grid';
import { BlogSliceCallToAction } from '../blog-slice-call-to-action';
import { BlogSliceDownloadCta } from '../blog-slice-download-cta';
import { BlogSliceMorePosts } from '../blog-slice-more-posts';
import { BlogSlicePhoneNumberCta } from '../blog-slice-phone-number-cta';
import { BlogSliceSection } from '../blog-slice-section';
import './blog-post-layout.scss';

const SLICE_TYPES = {
    BLOG_POST_SECTION: 'blog_post_section',
    MORE_BLOG_POSTS: 'more_blog_post',
    DOWNLOAD_CTA_SLICE: 'download_cta_slice',
    CALL_TO_ACTION: 'call_to_action',
    ALTERNATIVE_GRID: 'alternate_grid',
    PHONE_NUMBER_CTA_SLICE: 'phone_number_cta_slice',
    NEWSLETTER_CTA_SLICE: 'newsletter_cta_slice',
    ALL_BLOG_POST: 'all_blog_post',
};

export function BlogPostLayout(blogPost: BlogPostEntity | BlogEntity) {
    const slices = blogPost.data.body;
    const sections = slices.map((slice, index) => {
        switch (slice.slice_type) {
            case SLICE_TYPES.BLOG_POST_SECTION:
                return <BlogSliceSection data={slice} key={index} />;
            case SLICE_TYPES.MORE_BLOG_POSTS:
                return <BlogSliceMorePosts data={slice} key={index} />;
            case SLICE_TYPES.DOWNLOAD_CTA_SLICE:
                return <BlogSliceDownloadCta data={slice} key={index} />;
            case SLICE_TYPES.CALL_TO_ACTION:
                return <BlogSliceCallToAction data={slice} key={index} />;
            case SLICE_TYPES.ALTERNATIVE_GRID:
                return <BlogSliceAlternativeGrid data={slice} key={index} />;
            case SLICE_TYPES.PHONE_NUMBER_CTA_SLICE:
                return <BlogSlicePhoneNumberCta data={slice} key={index} />;
            case SLICE_TYPES.ALL_BLOG_POST:
                return <BlogSliceAllBlogPosts key={index} />;
            default:
                return null;
        }
    });
    return <div className={'post-content'}>{sections}</div>;
}
