@use '../../../styles/theme';

.post-content .rich-text-content {
    &,
    a,
    p,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        color: rgba(0, 0, 0, 0.54);
        font-family: 'Poppins', 'React', sans-serif;
        line-height: 1.5;
        &:empty {
            display: none;
        }
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        color: #111;
        display: block;
        width: 100%;
    }

    a,
    p {
        font-weight: 400;
        font-size: 1rem;
        line-height: 1.8;
    }

    p {
        margin-top: 0;
    }

    h3 {
        font-size: 24px;
        font-weight: 500;
    }

    a:hover {
        color: theme.$primaryColor;
    }

    img {
        width: 100%;
        max-width: 100%;
        overflow: hidden;
        border-radius: 20px;
        margin-bottom: 24px;
    }
}
