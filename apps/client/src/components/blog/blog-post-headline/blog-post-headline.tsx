import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Snackbar from '@mui/material/Snackbar';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React from 'react';
import { ReactComponent as ClipBoard } from '../../../assets/icons/clip-board.svg';
import { ReactComponent as FacebookLogo } from '../../../assets/icons/facebook-logo.svg';
import { ReactComponent as LinkedInLogo } from '../../../assets/icons/linkedin-logo.svg';
import { BlogPostEntity } from '../../../entity/blog-post.entity';
import classes from './blog-post-headline.module.scss';

const StyledBackButton = styled(IconButton)(({ theme }) => ({
    width: theme.spacing(6.25),
    marginRight: theme.spacing(1.75),
    marginTop: theme.spacing(1.75),
    marginBottom: theme.spacing(1.75),
    border: '1px solid #afafaf',
    '&, & .MuiTouchRipple-root *': {
        borderRadius: '16px !important',
    },
}));

interface BlogPostHeadlineProps {
    title: string;
    category: string;
    publishData: string;
    blogPostData: BlogPostEntity;
}

export function BlogPostHeadline({ title, category, publishData, blogPostData }: BlogPostHeadlineProps) {
    const [showSnackBar, setShowSnackBar] = React.useState(false);

    const date = new Intl.DateTimeFormat('ro-RO', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
    }).format(Date.parse(publishData));
    const shareOnFacebook = () => {
        window.open(
            'https://www.facebook.com/sharer/sharer.php?u=' + window.location.href,
            'facebook-share-dialog',
            'width=800,height=600',
        );
    };

    const shareOnLinkedin = () => {
        const params = `mini=true&url=${window.location.href}&title=${encodeURIComponent(
            blogPostData.data.social_title,
        )}&summary=${encodeURIComponent(blogPostData.data.social_description)}`;
        window.open(`https://www.linkedin.com/shareArticle?${params}`, 'linked-share-dialog', 'width=800,height=600');
    };

    const copyToClipboard = () => {
        if (navigator) {
            navigator.clipboard.writeText(window.location.href);
        }
        setShowSnackBar(true);
        setTimeout(() => {
            setShowSnackBar(false);
        }, 3000);
    };

    const handleBack = () => {
        window.history.back();
    };

    return (
        <>
            <Container className={classes.headline}>
                <StyledBackButton onClick={handleBack} size={'large'}>
                    <ArrowBackIcon />
                </StyledBackButton>
                <Typography variant={'h4'} component={'h1'} className={classes.title} align={'left'}>
                    {title}
                </Typography>
                <Grid container className={classes.underTitleContainer} justifyContent={'space-between'}>
                    <Grid className={classes.left}>
                        <Typography variant={'button'} className={classes.category}>
                            {category}
                        </Typography>
                        <div className={classes.dotDivider} />
                        <Typography variant={'body1'} className={classes.date}>
                            {date}
                        </Typography>
                    </Grid>
                    <Grid className={classes.shareIcons}>
                        <Typography variant={'body1'} className={classes.label}>
                            Distribuie:
                        </Typography>
                        <a className={classes.icon} onClick={() => shareOnFacebook()}>
                            <FacebookLogo />
                        </a>
                        <a className={classes.icon} onClick={() => shareOnLinkedin()}>
                            <LinkedInLogo />
                        </a>
                        <a className={classes.icon} onClick={() => copyToClipboard()}>
                            <ClipBoard />
                        </a>
                    </Grid>
                </Grid>
            </Container>
            <Snackbar
                anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                open={showSnackBar}
                onClose={() => setShowSnackBar(false)}
                ContentProps={{
                    'aria-describedby': 'message-id',
                }}
                message={
                    <Typography variant={'body1'} id="message-id">
                        Copied!
                    </Typography>
                }
            />
        </>
    );
}

export default BlogPostHeadline;
