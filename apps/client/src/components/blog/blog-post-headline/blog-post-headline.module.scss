@use '../../../styles/breakpoints';

.headline {
}
.title {
    font-family: 'Plus Jakarta Display', Poppins, serif;
    @include breakpoints.down('sm') {
        font-size: 24px;
    }
}

.category {
    color: #2f80fb;
    font-weight: 600;
    font-size: 18px;
    text-transform: uppercase;
}

.underTitleContainer {
    display: flex;
    flex-direction: row;
    margin-top: 24px;

    .left {
        display: flex;
    }
}

.date,
.label {
    font-size: 14px;
    font-weight: 600;
    color: #757575;
    line-height: 1.75;
    padding-top: 4px;
    text-transform: capitalize;
}

.label {
    @include breakpoints.down('sm') {
        display: none;
    }
}

.dotDivider {
    width: 4px;
    height: 4px;
    border-radius: 4px;
    color: #757575;
    margin: auto 10px;
    background-color: #757575;
}

.shareIcons {
    display: flex;
    flex: 1;
    justify-content: flex-end;
    align-items: center;
    gap: 20px;
}

.icon {
    width: 20px;
    height: 20px;
    padding: 0;
    display: flex;

    &:hover {
        cursor: pointer;
    }

    &:last-child {
        padding-top: 6px;
    }
}
