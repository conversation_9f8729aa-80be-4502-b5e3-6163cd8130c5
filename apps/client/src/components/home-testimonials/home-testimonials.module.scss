@use '../../styles/breakpoints';

.root {
    background-color: #111;
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 0;

    :global(.carousel) {
        width: 100%;
    }

    :global(.carousel__inner-slide) {
        padding: 16px;
        display: flex;
        flex: 1 1 auto;
    }

    :global(.carousel__slide) {
        display: flex;
    }

    @include breakpoints.down('md') {
        min-height: 0;
    }
}

.caption {
    font-size: 16px;
    font-weight: 600;
    color: #ffc043;
    margin-bottom: 6px;
    @include breakpoints.down('sm') {
        font-size: 14px;
    }
}

.heading {
    font-size: 46px;
    font-weight: 700;
    line-height: 60px;
    color: #fff;
    @include breakpoints.down('sm') {
        font-size: 32px;
        line-height: 42px;
    }
}

.sliderButton {
    height: 30px;
    width: 30px;
    border: 1px solid #fff;
    color: #fff;
    margin-left: 26px;
    background-color: transparent;
    border-radius: 15px;
    padding: 2px;

    &:hover {
        color: #111;
        background-color: #fff;
    }

    &:first-child {
        margin-left: 0;
    }
}

.testimonials {
    margin-top: 56px;
    display: block;
    @include breakpoints.down('sm') {
        margin-top: 42px;
    }
}

.testimonial {
    background-color: #333;
    border-radius: 20px;
    padding: 34px 34px 30px;
    display: flex;
    flex-direction: column;
    flex: 1 1 auto;
}

.testimonialText {
    font-size: 16px;
    font-weight: 500;
    line-height: 30px;
    color: #e2e2e2;
    margin-top: 14px;
    flex: 1 1 auto;
}

.testimonialProfile {
    margin-top: 24px;
    display: flex;
    align-items: center;
    @include breakpoints.down('sm') {
        align-items: flex-start;
    }
}

.testimonialAvatar {
    height: 60px;
    width: 60px;
    border-radius: 30px;
    @include breakpoints.down('sm') {
        height: 50px;
        width: 50px;
        border-radius: 25px;
    }
}

.testimonialProfileInfo {
    padding-left: 20px;
}

.testimonialProfileName,
.testimonialProfilePosition {
    color: #fff;
}

.testimonialProfileName {
    font-size: 18px;
    font-weight: 700;
    line-height: 30px;
    @include breakpoints.down('sm') {
        font-size: 14px;
        line-height: 24px;
    }
}

.testimonialProfilePosition {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    color: transparentize(#fff, 0.2);
    @include breakpoints.down('sm') {
        font-size: 12px;
        line-height: 20px;
    }
}
