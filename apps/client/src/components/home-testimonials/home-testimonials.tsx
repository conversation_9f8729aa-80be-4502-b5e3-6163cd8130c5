import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { ButtonBack, ButtonNext, CarouselProvider, Slide, Slider } from 'pure-react-carousel';
import React, { useMemo } from 'react';
import pictureBedohazi from '../../assets/pictures/bedohazi.jpg';
import pictureDarius from '../../assets/pictures/darius.jpg';
import pictureMihaela from '../../assets/pictures/mihaela.jpg';
import pictureTolan from '../../assets/pictures/tolan.jpg';
import HomeTestimonialsItem from './home-testimonials-item';
import classes from './home-testimonials.module.scss';

export function HomeTestimonials() {
    const isDownSm = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));
    const controls = useMemo(
        () => (
            <Grid item xs container justifyContent={isDownSm ? 'center' : 'flex-end'}>
                <ButtonBack className={classes.sliderButton}>
                    <ChevronLeftIcon />
                </ButtonBack>

                <ButtonNext className={classes.sliderButton}>
                    <ChevronRightIcon />
                </ButtonNext>
            </Grid>
        ),
        [isDownSm],
    );

    return (
        <section className={classes.root}>
            <CarouselProvider
                totalSlides={4}
                step={isDownSm ? 1 : 2}
                visibleSlides={isDownSm ? 1 : 2}
                naturalSlideHeight={346}
                naturalSlideWidth={526}
                isIntrinsicHeight
                infinite
            >
                <Container>
                    <Grid container alignItems={'flex-end'}>
                        <Grid item xs={12} sm={6}>
                            <Typography variant={'button'} className={classes.caption}>
                                Testimoniale
                            </Typography>
                            <Typography variant={'h3'} component={'h3'} className={classes.heading}>
                                Ce spun oamenii <br /> despre BOOKR?
                            </Typography>
                        </Grid>

                        {!isDownSm ? controls : null}
                    </Grid>

                    <Grid container spacing={4} className={classes.testimonials}>
                        <Slider>
                            <Slide index={0}>
                                <HomeTestimonialsItem
                                    authorImage={pictureMihaela}
                                    authorName={'Mihaela Vodnar'}
                                    authorPosition={'Owner at Vodnar Beauty Lounge'}
                                >
                                    Aleg să folosesc aplicația BOOKR din varii motive: - profitabilitatea,
                                    productivitatea și managementul programărilor ar fi doar câteva dintre ele. Un stil
                                    de viață activ nu e întotdeauna ușor de menajat, de aceea a fi prezenți 24/7 pentru
                                    clienții noștri, e cea mai mare realizare!
                                </HomeTestimonialsItem>
                            </Slide>
                            <Slide index={1}>
                                <HomeTestimonialsItem
                                    authorImage={pictureBedohazi}
                                    authorName={'Bedőházi Szende'}
                                    authorPosition={'Owner at Stress Away Therapy'}
                                >
                                    Aplicația Bookr a devenit cel mai bun asistent pe care mi l-aș fi putut dori. Acum
                                    nu mai pierd cliente din cauza faptului că nu reușesc să răspund în timp util la
                                    mesaje. În plus, cu Bookr este extrem de ușor să îmi coordonez echipa, iar clientele
                                    noastre sunt foarte încântate de ușurința cu care își pot face programări.
                                </HomeTestimonialsItem>
                            </Slide>
                            <Slide index={2}>
                                <HomeTestimonialsItem
                                    authorImage={pictureTolan}
                                    authorName={'Tolan Horațiu'}
                                    authorPosition={'Owner at INSANE Saloon'}
                                >
                                    Vă suntem foarte recunoscători pentru această colaborare, ați făcut o treabă foarte
                                    bună și cred că toată lumea ar trebui să conștientizeze importanța unei asemena
                                    aplicații, ținând cont de stilul nostru de viață dinamic și agitat.
                                </HomeTestimonialsItem>
                            </Slide>
                            <Slide index={3}>
                                <HomeTestimonialsItem
                                    authorImage={pictureDarius}
                                    authorName={'Darius Luca'}
                                    authorPosition={'Barber at Mirror 21'}
                                >
                                    De când folosesc aplicația BOOKR, 100% dintre clienți își onorează programările
                                    datorită notificărilor reminder. Cea mai bună parte este că au libertatea de
                                    programare 24/7. Totul stă la un click distanță. Recomand - pentru un business mai
                                    organizat.
                                </HomeTestimonialsItem>
                            </Slide>
                        </Slider>
                    </Grid>

                    {isDownSm ? <Box pt={4}>{controls}</Box> : null}
                </Container>
            </CarouselProvider>
        </section>
    );
}

export default HomeTestimonials;
