import Avatar from '@mui/material/Avatar';
import Typography from '@mui/material/Typography';
import React, { PropsWithChildren } from 'react';
import { ReactComponent as QuoteIcon } from '../../assets/icons/quote.svg';
import classes from './home-testimonials.module.scss';

interface HomeTestimonialsProps {
    authorImage: string;
    authorName: string;
    authorPosition: string;
}

export function HomeTestimonialsItem({
    children,
    authorName,
    authorPosition,
    authorImage,
}: PropsWithChildren<HomeTestimonialsProps>) {
    return (
        <div className={classes.testimonial}>
            <QuoteIcon className={classes.testimonialQuoteIcon} />
            <Typography className={classes.testimonialText}>{children}</Typography>
            <div className={classes.testimonialProfile}>
                <Avatar src={authorImage} className={classes.testimonialAvatar} />

                <div className={classes.testimonialProfileInfo}>
                    <Typography className={classes.testimonialProfileName}>{authorName}</Typography>
                    <Typography className={classes.testimonialProfilePosition}>{authorPosition}</Typography>
                </div>
            </div>
        </div>
    );
}

export default HomeTestimonialsItem;
