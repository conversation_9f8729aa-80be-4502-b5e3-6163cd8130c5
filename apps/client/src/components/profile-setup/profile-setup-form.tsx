import { logEvent } from 'firebase/analytics';
import { updateProfile } from 'firebase/auth';
import { Formik } from 'formik';
import React, { useCallback, useMemo } from 'react';
import { DEFAULT_CALLING_CODE } from '@bookr-technologies/core/constants';
import { FirebaseService } from '@bookr-technologies/core/services';
import { DEFAULT_PROFILE_PICTURE_IMAGE } from '../../constants/constants';
import { UserEntity } from '../../entity/user.entity';
import { closeProfileSetupDialog, firebaseAuthentication } from '../../redux/slices/auth.slice';
import { useAppDispatch } from '../../redux/store';
import { UserService } from '../../services/user.service';
import { validationSchema } from './data';
import { FormView } from './form-view';

interface ProfileSetupFormProps {
    onCancel?: () => void;
}

export function ProfileSetupForm({ onCancel }: ProfileSetupFormProps) {
    const dispatch = useAppDispatch();
    const user = FirebaseService.auth().currentUser;

    const initialValues = useMemo(
        () => ({
            firstName: user?.displayName?.split(' ')[0] || '',
            lastName: user?.displayName?.split(' ')[1] || '',
            email: user?.email || '',
            calling: user?.phoneNumber || '',
            callingCode: DEFAULT_CALLING_CODE,
            phoneNumber: user?.phoneNumber || '',
            language: 'en-EN',
        }),
        [user?.displayName, user?.email, user?.phoneNumber],
    );

    const handleSubmit = useCallback(
        async (values: any) => {
            const auth = FirebaseService.auth();
            if (auth.currentUser) {
                await updateProfile(auth.currentUser, { displayName: values.firstName + ' ' + values.lastName });
                await updateProfile(auth.currentUser, {
                    photoURL: values.photoURL || DEFAULT_PROFILE_PICTURE_IMAGE,
                });
            }

            const data: UserEntity = {
                uid: auth.currentUser?.uid ?? '',
                displayName: values.firstName + ' ' + values.lastName,
                phoneNumber:
                    `${values.callingCode ?? ''}${values.phoneNumber ?? ''}` || auth.currentUser?.phoneNumber || '',
                accountType: 'CLIENT',
                createdAt: new Date().toISOString(),
                photoURL: values.photoURL || DEFAULT_PROFILE_PICTURE_IMAGE,
                platform: 'web',
                email: values.email,
                language: values.language,
            };
            await UserService.instance.createUser(data);
            logEvent(FirebaseService.analytics(), 'sign_up', { platform: 'web' });
            logEvent(FirebaseService.analytics(), 'user_created_on_web');
            dispatch(firebaseAuthentication(data));
            dispatch(closeProfileSetupDialog());
        },
        [dispatch],
    );

    return (
        <Formik
            enableReinitialize
            initialValues={initialValues}
            onSubmit={handleSubmit}
            validationSchema={validationSchema}
        >
            <FormView onCancel={onCancel} />
        </Formik>
    );
}
