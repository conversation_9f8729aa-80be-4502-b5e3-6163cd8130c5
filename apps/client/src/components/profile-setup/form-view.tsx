import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { PhoneAuthProvider } from 'firebase/auth';
import { Form } from 'formik';
import React from 'react';
import { FirebaseService } from '@bookr-technologies/core/services';
import { CountrySelectVariant, FormikCountrySelect } from '@bookr-technologies/ui/CountrySelect';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import classes from './form-view.module.scss';

interface FormViewProps {
    onCancel?: () => void;
}

export function FormView({ onCancel }: FormViewProps) {
    const hideCallingCode = FirebaseService.auth().currentUser?.providerData.find(
        (info) => info?.providerId === PhoneAuthProvider.PROVIDER_ID,
    );

    return (
        <ThemeProvider>
            <Grid component={Form} container direction={'column'} width={'100%'}>
                <Grid container direction={'column'}>
                    <Typography variant={'h5'} fontWeight={500}>
                        Complete your profile
                    </Typography>

                    <Grid container direction={'column'} mt={2}>
                        <Grid item xs className={classes.fieldColumnSingle}>
                            <FormikTextField name={'firstName'} label={'First name'} fullWidth />
                        </Grid>
                        <Grid item xs className={classes.fieldColumnSingle}>
                            <FormikTextField name={'lastName'} label={'Last name'} fullWidth />
                        </Grid>
                        <Grid item xs className={classes.fieldColumnSingle}>
                            <FormikTextField name={'email'} label={'Email'} fullWidth type={'email'} />
                        </Grid>
                        <Grid item xs container spacing={3} className={classes.fieldColumnSingle}>
                            {!hideCallingCode ? (
                                <Grid item xs maxWidth={'160px !important'}>
                                    <FormikCountrySelect
                                        name={'callingCode'}
                                        label={'Calling code'}
                                        variant={CountrySelectVariant.CallingCode}
                                        fullWidth
                                    />
                                </Grid>
                            ) : null}
                            <Grid item xs>
                                <FormikTextField name={'phoneNumber'} label={'Phone Number'} fullWidth />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>

                <Grid container direction={'row'} alignItems={'center'} justifyContent={'flex-end'} mt={1}>
                    {onCancel && (
                        <Button size={'small'} onClick={onCancel}>
                            Cancel
                        </Button>
                    )}
                    <FormikButton size={'small'} variant={'contained'} sx={{ ml: 3 }}>
                        Book now
                    </FormikButton>
                </Grid>
            </Grid>
        </ThemeProvider>
    );
}

export default FormView;
