import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import React, { useCallback } from 'react';
import { FirebaseService } from '@bookr-technologies/core/services';
import { closeProfileSetupDialog, firebaseAuthentication } from '../../redux/slices/auth.slice';
import { useAppDispatch, useAppSelector } from '../../redux/store';
import { ProfileSetupForm } from './profile-setup-form';

export function ProfileSetupDialog() {
    const dispatch = useAppDispatch();
    const open = useAppSelector((state) => state.auth.profileSetupDialog);

    const handleClose = useCallback(async () => {
        try {
            await FirebaseService.auth().currentUser?.delete();
        } catch (e) {
            await FirebaseService.auth().signOut();
        }

        dispatch(firebaseAuthentication(null));
        return dispatch(closeProfileSetupDialog());
    }, [dispatch]);

    return (
        <Dialog open={open} onClose={handleClose} fullWidth maxWidth={'xs'}>
            <DialogContent>
                <ProfileSetupForm onCancel={handleClose} />
            </DialogContent>
        </Dialog>
    );
}
