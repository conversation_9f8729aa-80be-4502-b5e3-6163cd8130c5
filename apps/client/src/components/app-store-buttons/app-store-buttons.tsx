import Button from '@mui/material/Button';
import Grid, { GridProps } from '@mui/material/Grid';
import React from 'react';
import appStore from '../../assets/icons/app-store.png';
import googlePlay from '../../assets/icons/google-play.png';
import classes from '../home-footer/home-footer.module.scss';

export function AppStoreButtons({ ...rest }: GridProps) {
    return (
        <Grid container alignItems={'center'} justifyContent={'center'} {...rest}>
            <Button
                href="https://play.google.com/store/apps/details?id=com.bookr.app"
                target={'_blank'}
                rel="noreferrer"
                className={classes.heroButton}
            >
                <img src={googlePlay} alt="GooglePlay" />
            </Button>

            <Button
                href="https://apps.apple.com/us/app/bookr-online-booking-app/id1547131136"
                target={'_blank'}
                rel="noreferrer"
                className={classes.heroButton}
            >
                <img src={appStore} alt="AppStore" />
            </Button>
        </Grid>
    );
}

export default AppStoreButtons;
