.buttons,
.dots {
    box-sizing: border-box;
    width: 100%;
    position: absolute;
    left: 0;
    display: flex;
    align-content: center;
    align-items: center;
}

.buttons {
    top: 50%;
    justify-content: space-between;
    transform: translate(0, -50%);
    padding: 0 16px;

    :global(.MuiIconButton-root) {
        background-color: #fff !important;
    }
}

.dots {
    bottom: 0;
    padding: 6px;
    justify-content: center;
    :global(.carousel__dot-group) {
        display: inline-flex;
    }
    :global(.carousel__dot) {
        padding: 0;
        border: none;
        display: inline-flex;
        border-radius: 5px;
        width: 10px;
        height: 10px;
        background-color: #cbcbcb;
        margin: 6px;
    }
    :global(.carousel__dot--selected) {
        background-color: #fff;
    }
}

.carouselButton {
    background-color: transparent;
    border: none;
    padding: 0;
}
