.root {
    margin-bottom: 24px;
    position: relative;

    :global(.carousel) {
        position: relative;
    }

    :global(.carousel__slider) {
        overflow: hidden;
        border-radius: 8px;
    }

    :global(.carousel__image) {
        object-fit: cover;
        object-position: center center;
    }
}

.virtualTourButton.virtualTourButton {
    font-family: '<PERSON><PERSON><PERSON>', <PERSON>o, sans-serif;
    height: 52px;
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0.005em;
    padding: 0 24px;
    color: #fff;
    background-color: #111;
    border-radius: 0 0 16px 16px;
    text-transform: none;
    &:hover {
        background-color: #444;
    }
}
