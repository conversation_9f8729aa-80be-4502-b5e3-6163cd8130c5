import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import { CarouselProvider, Image, Slide, Slider } from 'pure-react-carousel';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ReactComponent as ViewInAr } from '../../assets/icons/view-in-ar.svg';
import { BusinessGalleryControls } from './business-gallery-controls';
import classes from './business-gallery.module.scss';

interface BusinessGalleryProps {
    photos?: string[];
    virtualTour?: string;
}

export function BusinessGallery({ photos, virtualTour }: BusinessGalleryProps) {
    const { t } = useTranslation('businessPage');
    if ((!photos || photos.length === 0) && !virtualTour) {
        return null;
    }

    return (
        <section className={classes.root}>
            {!!photos && photos.length > 0 && (
                <CarouselProvider
                    naturalSlideWidth={742}
                    naturalSlideHeight={378}
                    totalSlides={photos?.length || 0}
                    step={1}
                >
                    <Slider>
                        {photos?.map((image, index) => (
                            <Slide key={index} index={index}>
                                <Image src={image} hasMasterSpinner />
                            </Slide>
                        ))}
                    </Slider>
                    <BusinessGalleryControls />
                </CarouselProvider>
            )}
            {virtualTour && (
                <Grid container justifyContent={'center'}>
                    <Button
                        variant={'contained'}
                        startIcon={<ViewInAr />}
                        className={classes.virtualTourButton}
                        href={virtualTour}
                        target={'_blank'}
                        rel={'noreferrer noopener'}
                        disableElevation
                    >
                        {t('viewVirtualTour')}
                    </Button>
                </Grid>
            )}
        </section>
    );
}
