import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import IconButton from '@mui/material/IconButton';
import { ButtonBack, ButtonNext, DotGroup } from 'pure-react-carousel';
import React from 'react';
import classes from './business-gallery-controls.module.scss';

export function BusinessGalleryControls() {
    return (
        <>
            <div className={classes.buttons}>
                <ButtonBack className={classes.carouselButton}>
                    <IconButton component={'span'}>
                        <KeyboardArrowLeft />
                    </IconButton>
                </ButtonBack>
                <ButtonNext className={classes.carouselButton}>
                    <IconButton component={'span'}>
                        <KeyboardArrowRight />
                    </IconButton>
                </ButtonNext>
            </div>

            <div className={classes.dots}>
                <DotGroup />
            </div>
        </>
    );
}
