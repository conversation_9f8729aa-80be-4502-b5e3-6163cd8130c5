import ButtonBase, { ButtonBaseProps } from '@mui/material/ButtonBase';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { PropsWithChildren } from 'react';

interface CategoryCardProps extends ButtonBaseProps {
    headline: string;
    image: string;
    href: string;
}

const Root = styled(ButtonBase)(({ theme }) => ({
    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    borderRadius: 20,
    color: '#fff',
    width: '100%',
    padding: theme.spacing(5, 5.5, 4),
    position: 'relative',
    overflow: 'hidden',
    zIndex: 1,
    '.CategoryCard-image': {
        zIndex: -1,
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        objectFit: 'cover',
        width: '100%',
        height: '100%',
    },

    '.MuiButton-root': {
        backgroundColor: '#fff',
        color: '#111',
        borderRadius: 20,
        padding: theme.spacing(2, 3),
        marginTop: theme.spacing(4),
    },

    '.CategoryCard-overlay': {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(29, 29, 29, 0.3)',
        opacity: 0,
        transform: 'translate3d(0, 0, 0)',
        backdropFilter: 'blur(43px) saturate(180%)',
        transition: theme.transitions.create('opacity'),
    },

    '.CategoryCard-contentHolder': {
        zIndex: 1,
        position: 'relative',
    },

    '.CategoryCard-content': {
        opacity: 0,
        transition: theme.transitions.create(['opacity', 'transform']),
        transform: 'translate3d(0, 10px, 0)',
    },

    '&:hover': {
        '.CategoryCard-overlay, .CategoryCard-content': {
            opacity: 1,
        },
        '.CategoryCard-content': {
            transform: 'translate3d(0, 0, 0)',
        },
    },
}));

export function CategoryCard({ image, headline, children, ...rest }: PropsWithChildren<CategoryCardProps>) {
    return (
        <Root {...rest}>
            <img className={'CategoryCard-image'} src={image} alt={'Card'} />
            <div className={'CategoryCard-overlay'} />
            <div className={'CategoryCard-contentHolder'}>
                <Typography variant={'h4'} fontWeight={700} color={'inherit'} mb={2}>
                    {headline}
                </Typography>
                <div className={'CategoryCard-content'}>{children}</div>
            </div>
        </Root>
    );
}
