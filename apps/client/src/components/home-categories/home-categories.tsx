import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import categoryBeauty from '../../assets/home-categories/beauty-card.jpg';
import categoryMedical from '../../assets/home-categories/medical-card.jpg';
import { CategoryCard } from './CategoryCard';
import classes from './home-categories.module.scss';

export function HomeCategories() {
    const { t } = useTranslation('homepage');

    return (
        <section className={classes.root} id={'business-domain'}>
            <Container>
                <Typography variant={'h4'} component={'h3'} className={classes.heading} align={'center'}>
                    {t('whichDomainIsYourBusiness')}
                </Typography>

                <Grid container spacing={2.5} justifyContent={'center'}>
                    <Grid item xs maxWidth={'420px !important'}>
                        <CategoryCard headline={'Medical'} image={categoryMedical} href={'/medical'}>
                            <Typography variant={'subtitle1'} fontWeight={500} color={'inherit'}>
                                {t('medicalCardDescription')}
                            </Typography>
                            <Button variant={'contained'} color={'inherit'} disableElevation fullWidth>
                                {t('learnMore')}
                            </Button>
                        </CategoryCard>
                    </Grid>
                    <Grid item xs maxWidth={'420px !important'}>
                        <CategoryCard headline={'Beauty'} image={categoryBeauty} href={'/beauty'}>
                            <Typography variant={'subtitle1'} fontWeight={500} color={'inherit'}>
                                {t('beautyCardDescription')}
                            </Typography>
                            <Button variant={'contained'} color={'inherit'} disableElevation fullWidth>
                                {t('learnMore')}
                            </Button>
                        </CategoryCard>
                    </Grid>
                </Grid>
            </Container>
        </section>
    );
}

export default HomeCategories;
