@use '../../styles/breakpoints';

.root {
    background-color: #eee;
    padding: 60px 0 80px;
}

.heading {
    font-size: 32px;
    font-weight: 700;
    line-height: 42px;
    margin-bottom: 48px;
    @include breakpoints.down('sm') {
        font-size: 24px;
        line-height: 32px;
        br {
            display: none;
        }
    }
}

.categories {
    min-width: 1200px;
}

.categoriesHolder {
    max-width: 100%;
    overflow-x: auto;
    padding-bottom: 14px;
}

.category {
    display: flex;
    flex: 1 1 auto;
    width: 100%;
    position: relative;
    overflow: hidden;
    background-color: #c8c8c8;
    border-radius: 16px;
    transform: translate3d(0, 0, 0);
}
.categoryHolder {
    padding: 12px;
}
.categoryImage {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    transition: transform 260ms;
    transform: translate3d(0, 0, 0) scale(1, 1);
    z-index: 1;
}

.category:hover .categoryImage {
    transform: translate3d(0, 0, 0) scale(1.2, 1.2);
}

.categoryLabel {
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 8px 16px;
    color: #fff;
    font-weight: 600;
    font-size: 16px;
    z-index: 3;
    transform: translate3d(0, 0, 0);
}

.carousel {
    max-width: 848px;
    margin: 0 auto;
    position: relative;
    & :global(.carousel__inner-slide) {
        display: flex;
        padding: 12px;
    }
}
.carouselButtons {
    position: absolute;
    top: 50%;
    left: -12px;
    right: -12px;
    transform: translate3d(0, -50%, 0);
    z-index: 10;
    height: 48px;
    @include breakpoints.down('xs') {
        height: 32px;
        left: -4px;
        right: -4px;
    }
}
.carouselButton {
    position: absolute;
    top: 0;
    background-color: #fff;
    color: #111;
    box-shadow: 0 11px 20px rgba(5, 16, 55, 0.1);
    width: 48px;
    height: 48px;
    border-radius: 24px;
    border: none;
    padding: 12px;
    &:hover {
        background-color: #eee;
    }

    @include breakpoints.down('xs') {
        width: 32px;
        height: 32px;
    }
}
.carouselButtonLeft {
    left: 0;
}
.carouselButtonRight {
    right: 0;
}
