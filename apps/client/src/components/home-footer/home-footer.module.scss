@use '../../styles/breakpoints';

.root {
    background-color: #fff;
    border-top: 1px solid #cbcbcb;
    @include breakpoints.down('sm') {
        padding-bottom: 20px;
    }
}

.topSection {
    padding: 48px 0 64px;
    @include breakpoints.down('sm') {
        padding-bottom: 12px;
        .topSectionLeft {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
        }
    }
}

.bottomSection.bottomSection {
    padding: 16px 0;
    @include breakpoints.down('sm') {
        display: flex;
        flex-direction: column-reverse;
        justify-content: center;
        align-items: flex-start;
        padding: 24px 0;
    }
}

.bottomSectionText,
.bottomSectionContact {
    color: #111;
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
}
.bottomSectionText {
    @include breakpoints.down('sm') {
        padding-top: 24px;
    }
}

.bottomSectionContact.bottomSectionContact {
    text-decoration: underline;
    @include breakpoints.down('sm') {
        margin-top: 12px;
        margin-bottom: 12px;
    }
}

.languageSelect.languageSelect {
    :global(.MuiInput-root) {
        &:before {
            display: none !important;
        }
        &:after {
            display: none !important;
        }
        width: auto !important;
    }
    :global(.MuiAutocomplete-input) {
        width: auto !important;
    }
    :global(.MuiAutocomplete-inputRoot) {
        padding-right: 0 !important ;
    }
    :global(.MuiInput-input) {
        padding-left: 0 !important;
        font-weight: 500;
        font-size: 14px;
        line-height: 24px;
        text-decoration: underline;
        cursor: pointer;
    }
    :global(.Mui-disabled) {
        -webkit-text-fill-color: #111 !important;
    }
}

.bottomSectionRight.bottomSectionRight {
    @include breakpoints.down('sm') {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        width: 100%;
    }
}

.bottomSectionLeft.bottomSectionLeft {
    @include breakpoints.down('sm') {
        width: 100%;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
    }
}

.brand {
    display: block;
    @include breakpoints.down('sm') {
        margin-bottom: 28px;
    }
}

.brandImage {
    display: block;
}

.links.links {
    padding-left: 34px;
    @include breakpoints.down('sm') {
        display: block;
        padding-left: 0;
        margin-top: 24px;
    }

    .link {
    }
}

.footerSection.footerSection {
    margin-bottom: 12px;
}

.link.link {
    font-size: 14px;
    font-weight: 500;
    line-height: 24px;
    color: #757575;
    transition: color 250ms;
    svg {
        fill: #757575;
        transition: fill 250ms;
    }
    &:hover {
        color: #111;
        svg {
            fill: #111;
        }
    }
}

.social {
    margin-top: 24px;
    @include breakpoints.down('sm') {
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
    }
    .link {
        margin: 0;
        margin-right: 30px;
    }
}

.footerSectionTitle.footerSectionTitle {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
}
