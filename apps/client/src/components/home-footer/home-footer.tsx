import LanguageIcon from '@mui/icons-material/Language';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import InputAdornment from '@mui/material/InputAdornment';
import MuiLink, { LinkBaseProps } from '@mui/material/Link';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { CountrySelect, CountrySelectVariant } from '@bookr-technologies/ui/CountrySelect';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import darkLogo from '../../assets/dark-logo.png';
import { ReactComponent as FacebookIcon } from '../../assets/icons/facebook.svg';
import { ReactComponent as InstagramIcon } from '../../assets/icons/instagram.svg';
import { ReactComponent as LinkedinIcon } from '../../assets/icons/linkedin.svg';
import classes from './home-footer.module.scss';

interface FooterSectionProps {
    title: string;
    links: {
        title: string;
        link: string;
    }[];
    linkProps?: LinkBaseProps;
}

function FooterSection({ title = '', links = [], linkProps = {} }: FooterSectionProps) {
    return (
        <Grid item xs container flexDirection={'column'} className={classes.footerSection}>
            <Typography className={classes.footerSectionTitle}>{title}</Typography>
            {links.map((link) => (
                <MuiLink
                    key={link.title}
                    component={Link}
                    className={classes.link}
                    to={link.link}
                    underline={'hover'}
                    {...linkProps}
                >
                    {link.title}
                </MuiLink>
            ))}
        </Grid>
    );
}

// TODO: move to page footer
export function HomeFooter() {
    const { t, i18n } = useTranslation('homepage');

    const handleChangeLanguage = (value: string) => {
        localStorage.setItem('language', value);
        i18n.changeLanguage(value);
    };

    return (
        <ThemeProvider>
            <footer className={classes.root}>
                <Container>
                    <Grid container className={classes.topSection}>
                        <Grid item container alignItems={'flex-start'} className={classes.topSectionLeft}>
                            <Grid>
                                <a href={'/'} className={classes.brand}>
                                    <img src={darkLogo} alt="Bookr" className={classes.brandImage} />
                                </a>
                                <Grid
                                    container
                                    alignItems={'center'}
                                    justifyContent={'flex-end'}
                                    className={classes.social}
                                >
                                    <MuiLink href={'https://www.facebook.com/BookrApp'} className={classes.link}>
                                        <FacebookIcon />
                                    </MuiLink>
                                    <MuiLink
                                        href={'https://www.linkedin.com/company/bookrapp'}
                                        className={classes.link}
                                    >
                                        <LinkedinIcon />
                                    </MuiLink>
                                    <MuiLink href={'https://www.instagram.com/bookr.ro'} className={classes.link}>
                                        <InstagramIcon />
                                    </MuiLink>
                                </Grid>
                            </Grid>

                            <Grid
                                item
                                xs
                                container
                                alignItems={'center'}
                                flexDirection={'row'}
                                justifyContent={'space-between'}
                                className={classes.links}
                            >
                                <FooterSection
                                    title={t('aboutBookr')}
                                    links={[
                                        { title: 'Blog', link: '/blog' },
                                        { title: t('comparePlans'), link: '/compare-plans' },
                                    ]}
                                />
                                <FooterSection
                                    title={t('forBusinesses')}
                                    links={[
                                        { title: 'Beauty', link: '/beauty' },
                                        { title: 'Medical', link: '/medical' },
                                    ]}
                                />
                                <FooterSection
                                    title={'Legal'}
                                    links={[
                                        { title: t('tcs'), link: '/documents/terms-and-conditions.pdf' },
                                        { title: t('privacyPolicy'), link: '/documents/privacy-policy.pdf' },
                                    ]}
                                    linkProps={{ target: '_blank', rel: 'noopener noreferrer' }}
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Container>
                <Divider variant={'fullWidth'} />
                <Container>
                    <Grid container alignItems={'center'} className={classes.bottomSection}>
                        <Grid item xs={12} sm={6} className={classes.bottomSectionLeft}>
                            <Typography variant={'button'} className={classes.bottomSectionText}>
                                &copy; {new Date().getFullYear()} BOOKR TECHNOLOGIES SA
                            </Typography>
                        </Grid>
                        <Grid
                            item
                            xs={12}
                            sm={6}
                            container
                            justifyContent={'flex-end'}
                            className={classes.bottomSectionRight}
                        >
                            <CountrySelect
                                variant={CountrySelectVariant.Language}
                                value={i18n.language || 'ro-RO'}
                                size={'small'}
                                onChange={(event, value) => {
                                    handleChangeLanguage(value?.code ?? 'ro-RO');
                                }}
                                renderInput={(params) => (
                                    <TextField
                                        {...params}
                                        placeholder={'Language'}
                                        disabled
                                        inputProps={{
                                            ...params.inputProps,
                                            inputMode: 'none',
                                            onKeyDown: (event) => {
                                                event.preventDefault();
                                            },
                                            readOnly: 'readonly',
                                        }}
                                        InputProps={{
                                            ...params.InputProps,
                                            value: i18n.language || 'ro-RO',
                                            startAdornment: (
                                                <InputAdornment position={'start'}>
                                                    <LanguageIcon color={'primary'} />
                                                </InputAdornment>
                                            ),
                                            endAdornment: undefined,
                                        }}
                                        variant={'standard'}
                                        className={classes.languageSelect}
                                    />
                                )}
                            />

                            <MuiLink
                                href={'mailto:<EMAIL>'}
                                className={classes.bottomSectionContact}
                                color={'textPrimary'}
                                underline={'hover'}
                            >
                                <EMAIL>
                            </MuiLink>
                        </Grid>
                    </Grid>
                </Container>
            </footer>
        </ThemeProvider>
    );
}

export default HomeFooter;
