@use '../../styles/mixins';

.root {
    padding-left: 90px;
    display: flex;
    align-items: center;

    &:last-child {
        padding-bottom: 0;
    }
}

.description {
    padding-left: 90px;
    padding-bottom: 18px;
    flex-direction: column;
}

.headline.headline {
    font-weight: 600;
    font-size: 16px;
    line-height: 30px;
    letter-spacing: 0.005em;
    @include mixins.mobile() {
        font-size: 14px;
    }
}

.subHeadline.subHeadline {
    font-weight: 600;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0.005em;
    color: #757575;

    display: flex;
    flex-direction: row;
    align-items: center;
}

.price,
.duration,
.dot {
    display: inline-flex;
}

.dot {
    width: 4px;
    height: 4px;
    border-radius: 2px;
    margin-left: 8px;
    margin-right: 8px;
    background-color: #757575;
}

.details {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.actions {
    display: flex;
    justify-content: flex-end;
}

.action.action {
    padding: 8px 24px;
    background-color: #e6f7f9;
    border-radius: 10px;
    font-weight: 500;
    font-size: 14px;
    line-height: 21px;
    letter-spacing: 0.0125em;
    color: #01a6ba;

    @include mixins.mobile() {
        font-size: 12px;
        padding: 6px 20px;
    }
}

.dialogBackdrop.dialogBackdrop {
    background-color: rgba(17, 17, 17, 0.8);
    backdrop-filter: blur(17px) saturate(220%);
}

.dialogContainer.dialogContainer {
    justify-content: flex-end;
    align-items: stretch;
}

.dialogPaper.dialogPaper {
    margin: 0;
    border-radius: 0;
    max-height: 100%;
    width: 100%;
    max-width: 620px;
    background-color: #eee;
}

.dialogContent.dialogContent,
.dialogActions.dialogActions {
    background-color: #fff;
}

.dialogTitleHolder.dialogTitleHolder {
    padding: 12px 36px 12px 48px;

    @include mixins.mobile() {
        padding: 8px 20px;
    }

    :global(.MuiTypography-root) {
        font-style: normal;
        font-weight: 500;
        font-size: 18px;
        line-height: 30px;
        letter-spacing: 0.005em;
        color: #000;
    }
}

.dialogContent.dialogContent {
    border-radius: 16px 16px 0 0;
    padding: 48px 48px 24px;
    @include mixins.mobile() {
        padding: 20px 20px 14px;
        display: flex;
        flex-direction: column;
    }
}

.dialogActions.dialogActions {
    padding: 24px 48px 36px;
    justify-content: space-between;
    align-items: center;
    @include mixins.mobile() {
        padding: 20px;
    }
}

.dialogAction.dialogAction {
    height: 40px;
    padding: 8px 24px;
    &:global(.MuiButton-text) {
        color: #757575;
    }

    &:global(.MuiButton-contained) {
        background-color: #111;
        color: #fff;

        &:disabled {
            background-color: #eee;
            font-weight: 500;
            font-size: 14px;
            line-height: 21px;
            letter-spacing: 0.0125em;
            color: #757575;
        }
    }
}

.calendarText.calendarText {
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.005em;
    color: #111;
    margin-bottom: 14px;
}

.calendar.calendar {
    width: 100%;
    border: none;
    margin-bottom: 24px;

    :global(.react-calendar__navigation) {
        margin-bottom: 14px;
    }

    :global(.react-calendar__month-view__weekdays) {
        margin-bottom: 36px;
        @include mixins.mobile() {
            margin-bottom: 14px;
        }
    }

    :global(.react-calendar__navigation__label) {
        pointer-events: none !important;
    }

    :global(.react-calendar__tile.react-calendar__month-view__days__day) {
        &,
        &:disabled,
        &:global(.react-calendar__tile--now) {
            &,
            &:before {
                background-color: transparent;
            }
        }

        & {
            padding: 6px;
            position: relative;

            &:before {
                content: '';
                display: block;
                width: 100%;
                height: 40px;
                border-radius: 16px;
                background-color: transparent;
                transform: scale(0.8, 0.8) translate3d(0, 0, 0);
                transition: transform 250ms, background-color 250ms;
                @include mixins.mobile() {
                    height: 32px;
                }
            }

            &:global(.react-calendar__tile--active) {
                &:before {
                    background-color: #111 !important;
                    transform: scale(1, 1) !important;
                }
            }

            &:hover {
                &:before {
                    background-color: #eee;
                    transform: scale(1, 1);
                }
            }

            abbr {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                font-weight: 600;
                font-size: 14px;
                line-height: 24px;
                text-align: center;
                letter-spacing: 0.005em;
            }
        }
    }

    :global(.react-calendar__month-view__weekdays__weekday) {
        & abbr {
            font-weight: bold;
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.005em;
            color: #111;
            text-decoration: none;
            text-transform: capitalize;
        }
    }

    :global(.react-calendar__navigation__arrow) {
        &,
        &:focus {
            background-color: transparent;
        }

        & {
            height: 44px;
            width: 44px;
            min-width: 44px;
            border-radius: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 3%;
        }
    }

    :global(.react-calendar__navigation__label__labelText) {
        font-weight: bold;
        font-size: 24px;
        line-height: 36px;
        letter-spacing: 0.0025em;
        color: #000;
        text-transform: capitalize;
        @include mixins.mobile() {
            font-size: 20px;
        }
    }
}
