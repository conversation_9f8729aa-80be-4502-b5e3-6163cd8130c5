import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Divider from '@mui/material/Divider';
import Typography from '@mui/material/Typography';
import moment from 'moment';
import React, { useCallback, useMemo, useState } from 'react';
import Calendar from 'react-calendar';
import { useTranslation } from 'react-i18next';
import { getTimezoneOffsetForDate } from '../../constants/constants';
import { TimeSlotEntity } from '../../entity/time-slot.entity';
import classes from './business-staff-service.module.scss';
import { NoSlotsAvailable } from './no-slots-available';
import { TimeSlots } from './time-slots';

interface DatePickerProps {
    businessStartingDate?: string;
    maxFutureDaysAppointment?: number;
    slot?: TimeSlotEntity | null;

    spacerRef?: React.MutableRefObject<HTMLElement | null>;

    onLoadSlots(date: Date): Promise<TimeSlotEntity[]>;

    onSelect(slot: TimeSlotEntity): any;
}

export function DatePicker(props: DatePickerProps) {
    const { t } = useTranslation('businessPage');
    const { slot, maxFutureDaysAppointment, businessStartingDate, onSelect, onLoadSlots, spacerRef } = props;
    const [date, setDate] = useState<Date>();
    const [loading, setLoading] = useState(false);
    const [slots, setSlots] = useState<TimeSlotEntity[]>([]);
    const maxDate = useMemo(
        () =>
            moment()
                .add(maxFutureDaysAppointment || 30, 'day')
                .toDate(),
        [maxFutureDaysAppointment],
    );
    const minDate = useMemo(
        () => moment.max(moment(businessStartingDate, 'YYYY-MM-DD'), moment()).toDate(),
        [businessStartingDate],
    );

    const handleChange = useCallback(
        async (date: Date) => {
            setLoading(true);
            setDate(date);
            const data = await onLoadSlots(date);
            const NOW = moment();
            const minutes = getTimezoneOffsetForDate(moment(date, 'YYYY-MM-DD'));
            setSlots(
                data.filter((slot) => {
                    const timeslot = {
                        // convert the slots to local timezone
                        start: moment(slot.start, 'HH:mm').add(-minutes, 'minutes').format('HH:mm'),
                        end: moment(slot.end, 'HH:mm').add(-minutes, 'minutes').format('HH:mm'),
                    };
                    return NOW.isBefore(
                        moment(moment(date).format('YYYY-MM-DD') + ' ' + timeslot.start, 'YYYY-MM-DD HH:mm'),
                    );
                }),
            );
            setLoading(false);
        },
        [setSlots, onLoadSlots, setLoading],
    );

    return (
        <>
            <Typography align={'center'} className={classes.calendarText}>
                {t('chooseTheRightDate')}
            </Typography>
            <Calendar
                className={classes.calendar}
                onChange={handleChange}
                value={date}
                minDate={minDate}
                maxDate={maxDate}
                view={'month'}
                showNeighboringMonth={false}
                prevLabel={<ChevronLeftIcon />}
                nextLabel={<ChevronRightIcon />}
                prev2Label={null}
                next2Label={null}
                locale={localStorage.getItem('language') ?? 'ro-RO'}
            />
            <Divider ref={spacerRef as any} />
            {loading ? (
                <Box
                    paddingTop={4}
                    display={'flex'}
                    alignItems={'center'}
                    justifyContent={'center'}
                    position={'absolute'}
                    bgcolor={'rgba(255, 255, 255, .5)'}
                    top={0}
                    left={0}
                    width={'100%'}
                    height={'100%'}
                    zIndex={3}
                >
                    <CircularProgress size={32} color={'primary'} />
                </Box>
            ) : (
                <>
                    {!!date && (!slots || slots.length === 0) && <NoSlotsAvailable />}
                    {!!date && slots?.length > 0 && (
                        <TimeSlots date={date} slots={slots} slot={slot} onSelect={onSelect} />
                    )}
                </>
            )}
        </>
    );
}
