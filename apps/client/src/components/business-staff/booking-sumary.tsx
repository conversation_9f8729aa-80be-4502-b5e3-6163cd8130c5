import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import moment from 'moment';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import AppStoreImg from '../../assets/icons/app-store.png';
import GooglePlayImg from '../../assets/icons/google-play.png';
import { ReactComponent as TaskAltIcon } from '../../assets/icons/task-alt.svg';
import { STORE_URL } from '../../constants/constants';
import { BusinessStaffServiceEntity } from '../../entity/business-staff-service.entity';
import { UserEntity } from '../../entity/user.entity';
import classes from './booking-sumary.module.scss';

interface BookingSummaryProps {
    appointment: {
        client: UserEntity;
        dateTime: string;
        id: number;
        service: BusinessStaffServiceEntity;
        staff: UserEntity;
    };
}

export function BookingSummary({ appointment }: BookingSummaryProps) {
    const { t } = useTranslation('businessPage');
    const { id, dateTime, service, staff } = appointment;
    const date$ = useMemo(() => (!dateTime ? 'N/A' : moment(dateTime).format('DD MMM')), [dateTime]);
    return (
        <>
            <Grid
                className={classes.successMessageHolder}
                container
                direction={'column'}
                alignItems={'center'}
                justifyContent={'center'}
            >
                <TaskAltIcon />
                <Typography className={classes.successMessage}>{t('appointmentSuccess')}</Typography>
            </Grid>
            <Grid
                className={classes.detailsMessageHolder}
                container
                direction={'column'}
                alignItems={'flex-start'}
                justifyContent={'flex-start'}
            >
                <Typography className={classes.detailsMessage}>{t('appointmentDetails')}:</Typography>
                <Grid
                    className={classes.detailsMessageTextHolder}
                    container
                    direction={'row'}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                >
                    <Typography className={classes.detailsMessageText}>ID:</Typography>
                    <Typography className={classes.detailsMessageText}>#{id}</Typography>
                </Grid>
                <Grid
                    className={classes.detailsMessageTextHolder}
                    container
                    direction={'row'}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                >
                    <Typography className={classes.detailsMessageText}>Staff:</Typography>
                    <Typography className={classes.detailsMessageText}>{staff?.displayName}</Typography>
                </Grid>
                <Grid
                    className={classes.detailsMessageTextHolder}
                    container
                    direction={'row'}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                >
                    <Typography className={classes.detailsMessageText}>{t('service')}:</Typography>
                    <Typography className={classes.detailsMessageText}>{service?.name}</Typography>
                </Grid>
                <Grid
                    className={classes.detailsMessageTextHolder}
                    container
                    direction={'row'}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                >
                    <Typography className={classes.detailsMessageText}>{t('dateAndTime')}:</Typography>
                    <Typography className={classes.detailsMessageText}>
                        {date$}, {moment(dateTime).format('HH:mm')}
                    </Typography>
                </Grid>
            </Grid>
            <Grid
                className={classes.appDetailsHolder}
                container
                direction={'column'}
                alignItems={'flex-start'}
                justifyContent={'flex-end'}
            >
                <Typography className={classes.appDetailsText}>{t('downloadBookrApp')}:</Typography>
                <Grid
                    className={classes.downloadImagesHolder}
                    container
                    direction={'row'}
                    alignItems={'center'}
                    justifyContent={'center'}
                >
                    <img
                        className={classes.appDetailsImage}
                        src={AppStoreImg}
                        alt="AppStore"
                        height={40}
                        onClick={() => window.open(STORE_URL.IOS)}
                    />
                    <img
                        className={classes.appDetailsImage}
                        src={GooglePlayImg}
                        alt="GooglePlay"
                        height={40}
                        onClick={() => window.open(STORE_URL.ANDROID)}
                    />
                </Grid>
            </Grid>
        </>
    );
}
