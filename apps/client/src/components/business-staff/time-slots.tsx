import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { getTimezoneOffsetForDate } from '../../constants/constants';
import { TimeSlotEntity } from '../../entity/time-slot.entity';
import classes from './date-slots.module.scss';

interface TimeSlotsProps {
    slots: TimeSlotEntity[];
    date: Date;
    slot?: TimeSlotEntity | null;

    onSelect(slot: TimeSlotEntity): any;
}

export function TimeSlots({ slots, slot, onSelect, date }: TimeSlotsProps) {
    const { t } = useTranslation('businessPage');
    const [active, setActive] = useState<TimeSlotEntity | null | undefined>();

    const handleClick = useCallback(
        (slot: any) => () => {
            setActive(slot);
            onSelect(slot);
        },
        [onSelect],
    );

    useEffect(() => {
        setActive(slot);
        // eslint-disable-next-line
    }, [slot?.start, slot?.end]);

    const minutes = getTimezoneOffsetForDate(moment(date));
    return (
        <Grid className={classes.root} container alignItems={'center'} justifyContent={'center'} direction={'column'}>
            <Typography align={'center'} className={classes.headline}>
                {t('chooseTheRightTime')}:
            </Typography>
            <Grid container alignItems={'center'} justifyContent={'center'} className={classes.slots}>
                {slots.map((slot, index) => (
                    <Button
                        key={index}
                        variant={'outlined'}
                        color={active?.start === slot.start ? 'primary' : 'inherit'}
                        className={classes.button}
                        onClick={handleClick(slot)}
                    >
                        {moment(slot.start, 'HH:mm').add(-minutes, 'minutes').format('HH:mm')}
                    </Button>
                ))}
            </Grid>
        </Grid>
    );
}
