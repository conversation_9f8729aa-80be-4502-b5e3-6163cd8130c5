import AddIcon from '@mui/icons-material/Add';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import RemoveIcon from '@mui/icons-material/Remove';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import { get } from 'lodash';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessStaffServiceEntity } from '../../entity/business-staff-service.entity';
import { BusinessStaffEntity } from '../../entity/business-staff.entity';
import { BusinessEntity } from '../../entity/business.entity';
import { useInitials } from '../../hooks/use-initials';
import classes from './business-staff-item.module.scss';
import { BusinessStaffService } from './business-staff-service';

interface BusinessStaffItemProps {
    staff: BusinessStaffEntity;
    business: BusinessEntity;
}

const SERVICES_TO_SHOW = 2;

export function BusinessStaffItem({ staff, business }: BusinessStaffItemProps) {
    const { t } = useTranslation('businessPage');
    const [isExpanded, setExpanded] = useState(true);
    const [showMore, setShowMore] = useState(false);

    const handleShowMore = useCallback(() => {
        setShowMore((prevState) => !prevState);
    }, [setShowMore]);

    const handleExpand = useCallback(
        (e: any, isExpanded: any) => {
            setExpanded(isExpanded);
        },
        [setExpanded],
    );

    const initials = useInitials(staff.displayName);

    const services = useMemo(() => get(staff, 'services', []), [staff]);
    const services$ = useMemo(() => {
        return (showMore ? services : services.slice(0, SERVICES_TO_SHOW))
            .filter((s: BusinessStaffServiceEntity) => !s.numberOfSessions)
            .map((service: BusinessStaffServiceEntity, id: number) => (
                <BusinessStaffService key={id} service={service} business={business} staff={staff} />
            ));
    }, [services, showMore, business, staff]);

    if (staff.hidden) {
        return null;
    }

    return (
        <Accordion expanded={isExpanded} onChange={handleExpand} elevation={0} className={classes.root}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box width={58}>
                    <Avatar className={classes.avatar} src={staff.photoURL}>
                        {initials}
                    </Avatar>
                </Box>
                <Box paddingLeft={2}>
                    <Typography variant={'subtitle2'} gutterBottom={false} className={classes.headline}>
                        {staff.displayName}
                    </Typography>
                    <Typography variant={'body2'} color={'textSecondary'} className={classes.subHeadline}>
                        {'BUSINESS_OWNER' === staff.accountType ? 'Owner' : 'Staff member'}
                    </Typography>
                </Box>
            </AccordionSummary>
            <AccordionDetails className={classes.details}>
                {services$}
                {services.length > SERVICES_TO_SHOW && (
                    <Button onClick={handleShowMore} startIcon={showMore ? <RemoveIcon /> : <AddIcon />}>
                        {showMore ? t('hide') : t('showAllServices')}
                    </Button>
                )}
            </AccordionDetails>
        </Accordion>
    );
}
