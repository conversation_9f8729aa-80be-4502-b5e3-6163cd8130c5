import EventBusyIcon from '@mui/icons-material/EventBusy';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import classes from './no-slots-available.module.scss';

export function NoSlotsAvailable() {
    const { t } = useTranslation('businessPage');
    return (
        <Grid className={classes.root} container alignItems={'center'} justifyContent={'center'} direction={'column'}>
            <EventBusyIcon color={'primary'} />
            <Typography align={'center'} className={classes.headline}>
                {t('noSlotsAvailable')}
            </Typography>
            <Typography align={'center'} className={classes.text}>
                {t('noSlotsAvailableText')}
            </Typography>
        </Grid>
    );
}
