.root.root {
    border-top: 1px solid #cbcbcb;
    border-radius: 0 !important;

    &:before {
        display: none !important;
    }

    :global(.MuiAccordionSummary-content.Mui-expanded) {
        margin: 12px 0;
    }

    :global(.MuiAccordionDetails-root) {
        padding-left: 0;
        padding-right: 0;
    }
}

.avatar.avatar {
    width: 58px;
    height: 58px;
}

.headline.headline {
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0.005em;
    color: #1f1f1f;
}

.subHeadline.subHeadline {
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.005em;
    color: #757575;
}

.details.details {
    display: flex;
    flex-direction: column;
}
