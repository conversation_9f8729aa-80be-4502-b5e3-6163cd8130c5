import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { BusinessStaffServiceEntity } from '../../entity/business-staff-service.entity';
import { BusinessStaffEntity } from '../../entity/business-staff.entity';
import { BusinessEntity } from '../../entity/business.entity';
import { BusinessStaffServiceDialog } from './business-staff-service-dialog';
import classes from './business-staff-service.module.scss';

export interface BusinessStaffServiceProps {
    service: BusinessStaffServiceEntity;
    staff: BusinessStaffEntity;
    business: BusinessEntity;
}

export function BusinessStaffService({ service, staff, business }: BusinessStaffServiceProps) {
    return (
        <>
            <Grid container alignItems={'center'} className={classes.root}>
                <Grid className={classes.details}>
                    <Typography className={classes.headline}>{service.name}</Typography>
                    <Typography className={classes.subHeadline}>
                        <span className={classes.price}>
                            {service.price} {service.currency}
                        </span>
                        <span className={classes.dot} />
                        <span className={classes.duration}>{service.duration} min</span>
                    </Typography>
                </Grid>
                <Grid className={classes.actions}>
                    <BusinessStaffServiceDialog service={service} staff={staff} business={business} />
                </Grid>
            </Grid>
            {service.description && (
                <Grid container className={classes.description}>
                    {service.description.split('\n').map((value, key) => (
                        <Typography key={key} fontSize={14}>
                            {value}
                        </Typography>
                    ))}
                </Grid>
            )}
        </>
    );
}
