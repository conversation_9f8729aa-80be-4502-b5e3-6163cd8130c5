import React, { useMemo } from 'react';
import { BusinessEntity } from '../../entity/business.entity';
import { BusinessStaffItem } from './business-staff-item';

interface BusinessStaffProps {
    business: BusinessEntity;
}

export function BusinessStaff({ business }: BusinessStaffProps) {
    const staff$ = useMemo(
        () => [...business.staffMembers].sort((a, b) => (a.staffRank || 0) - (b.staffRank || 0)),
        [business?.staffMembers],
    );

    return (
        <section>
            {staff$.map((value, index) => (
                <BusinessStaffItem key={index} business={business} staff={value} />
            ))}
        </section>
    );
}
