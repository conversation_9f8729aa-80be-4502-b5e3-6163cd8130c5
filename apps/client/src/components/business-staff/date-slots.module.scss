@use '../../styles/mixins';

.root {
    display: block;
    padding: 24px 18px;
    @include mixins.mobile() {
        padding: 24px 0 0 0;
    }
}

.slots {
}

.headline.headline {
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0.005em;
    color: #111;
}

.button.button {
    width: 90px;
    height: 36px;
    border-radius: 8px;
    margin: 10px;
    padding: 6px 24px;
    @include mixins.mobile() {
        margin: 6px;
    }

    & :global(.MuiButton-label) {
        font-weight: 600;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: 0.005em;
    }

    &:global(.MuiButton-outlinedPrimary) {
        background-color: #e6f7f9;
    }
}
