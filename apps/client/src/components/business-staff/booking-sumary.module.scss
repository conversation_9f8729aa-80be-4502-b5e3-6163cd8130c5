@use '../../styles/mixins';

.successMessageHolder.successMessageHolder {
    flex: 1 0 auto;
    padding-top: 24px;
    padding-bottom: 56px;
    @include mixins.mobile() {
        padding-top: 14px;
        padding-bottom: 48px;
    }

    svg {
        width: 100px;
        height: 100px;
        @include mixins.mobile() {
            width: 64px;
            height: 64px;
        }
    }
}

.successMessage.successMessage {
    margin-top: 18px;
    font-weight: 700;
    font-size: 24px;
    line-height: 36px;
    text-align: center;
    letter-spacing: 0.0025em;
    color: #1f1f1f;
    @include mixins.mobile() {
        font-size: 18px;
        line-height: 24px;
    }
}

.detailsMessageHolder.detailsMessageHolder {
    flex: 1 0 auto;
}

.detailsMessage.detailsMessage {
    font-weight: 600;
    font-size: 16px;
    line-height: 30px;
    letter-spacing: 0.005em;
    color: #111;
    margin-bottom: 12px;
    @include mixins.mobile() {
        font-size: 14px;
        margin-bottom: 0;
    }
}

.detailsMessageTextHolder.detailsMessageTextHolder {
    @include mixins.mobile() {
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        margin-bottom: 12px;
    }
}

.detailsMessageText.detailsMessageText {
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.005em;
    color: #757575;
    margin: 12px 0;
    @include mixins.mobile() {
        margin: 0;
        font-weight: 400;
        color: #111;
        &:last-child {
            color: #777;
        }
    }
}

.appDetailsHolder.appDetailsHolder {
    margin-top: 80px;
    @include mixins.mobile() {
        margin-top: 32px;
        flex: 1 0 auto;
        margin-bottom: 20px;
        & > :global(.MuiGrid-root) {
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }
}

.appDetailsText.appDetailsText {
    font-weight: 600;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0.005em;
    color: #000;
    margin-bottom: 28px;
    width: 100%;
    text-align: center;
    @include mixins.mobile() {
        text-align: center;
        margin-bottom: 14px;
    }
}

.downloadImagesHolder.downloadImagesHolder {
    //flex: 1 0 auto;
}

.appDetailsImage.appDetailsImage {
    cursor: pointer;
    margin-left: 18px;

    &:first-child {
        margin-left: 0;
    }
}
