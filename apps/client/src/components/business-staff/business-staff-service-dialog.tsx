import CloseIcon from '@mui/icons-material/Close';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { logEvent } from 'firebase/analytics';
import moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import 'react-calendar/dist/Calendar.css';
import { useTranslation } from 'react-i18next';
import { FirebaseService } from '@bookr-technologies/core/services';
import { getTimezoneOffsetForDate } from '../../constants/constants';
import { BusinessStaffServiceEntity } from '../../entity/business-staff-service.entity';
import { BusinessStaffEntity } from '../../entity/business-staff.entity';
import { BusinessEntity } from '../../entity/business.entity';
import { TimeSlotEntity } from '../../entity/time-slot.entity';
import { Defer } from '../../lib/defer';
import { useUser } from '../../redux/selectors/use-user';
import { openAuthDialog } from '../../redux/slices/auth.slice';
import { useAppDispatch } from '../../redux/store';
import { BookNowResponse, BusinessService } from '../../services/business.service';
import { BookingSummary } from './booking-sumary';
import classes from './business-staff-service.module.scss';
import { DatePicker } from './date-picker';

export interface BusinessStaffServiceDialogProps {
    business: BusinessEntity;
    staff: BusinessStaffEntity;
    service: BusinessStaffServiceEntity;
}

export function BusinessStaffServiceDialog({ business, staff, service }: BusinessStaffServiceDialogProps) {
    const { t } = useTranslation('businessPage');
    const [open, setOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const [date, setDate] = useState<Date | null>(null);
    const [error, setError] = useState<Error | null>(null);
    const [slot, setSlot] = useState<TimeSlotEntity | null>(null);
    const [summary, setSummary] = useState<any>(null);
    const authPromiseRef = useRef<Defer>(null);
    const spacerRef = useRef<HTMLElement>(null);
    const dialogContentRef = useRef<HTMLDivElement>(null);

    const dispatch = useAppDispatch();
    const user = useUser();

    const handleClick = useCallback(() => setOpen(true), [setOpen]);
    const handleClose = useCallback(() => {
        setOpen(false);
        setSlot(null);
        setSummary(null);
    }, [setOpen, setSlot, setSummary]);

    const handleLoadSlots = useCallback(
        async (date: Date) => {
            setDate(date);

            const data = await BusinessService.instance.fetchServiceTimeSlots({
                date: moment(date).format('YYYY-MM-DD'),
                staffId: staff.uid,
                serviceId: service.id,
            });

            setTimeout(() => {
                dialogContentRef.current?.scroll({
                    top: spacerRef.current?.offsetTop,
                    behavior: 'smooth',
                });
            }, 300);

            return data;
        },
        [staff.uid, service],
    );

    const handleBooking = useCallback(async () => {
        if (!slot || !date) {
            setError(new Error('Please select an available time slot'));
            return;
        }

        if (!user) {
            dispatch(openAuthDialog());
            (authPromiseRef as any).current = new Defer();
            await authPromiseRef.current!.wait();
        }

        setLoading(true);
        try {
            const data: BookNowResponse = await BusinessService.instance.bookServiceNow({
                staffId: staff.uid,
                timestamp: Math.floor(
                    moment(moment(date).format('YYYY-MM-DD') + slot.start, 'YYYY-MM-DD HH:mm')
                        .add(-getTimezoneOffsetForDate(moment(date)), 'minutes')
                        .valueOf() / 1000,
                ),
                serviceId: service.id,
                timezoneOffset: getTimezoneOffsetForDate(moment(date)),
            });
            setLoading(false);
            if (data.success) {
                setDate(moment(data.appointment.dateTime).toDate());
                logEvent(FirebaseService.analytics(), 'booking_made');
                logEvent(FirebaseService.analytics(), 'booking_made_on_web');
                setSummary(data.appointment);
                if (data.webRedirectUrl) {
                    const currentUrl = new URL(window.location.href);
                    const params = new URLSearchParams(currentUrl.search);
                    let paramsString = params.toString();
                    if (paramsString) {
                        paramsString = `?${paramsString}&appointment=${encodeURIComponent(
                            JSON.stringify(data.appointment),
                        )}`;
                    } else {
                        paramsString = `?appointment=${encodeURIComponent(JSON.stringify(data.appointment))}`;
                    }
                    const redirectUrl = `${data.webRedirectUrl}${paramsString}`;
                    window.open(redirectUrl, '_blank', 'noopener,noreferrer');
                }
            }
        } catch (e: any) {
            setError(e);
        }
    }, [slot, user, date, dispatch, staff.uid, service]);

    useEffect(() => {
        if (user && authPromiseRef.current) {
            authPromiseRef.current.resolve();
            (authPromiseRef as any).current = null;
        }
    }, [user]);

    return (
        <>
            <Button className={classes.action} onClick={handleClick}>
                {t('bookNow')}
            </Button>
            <Dialog
                open={open}
                classes={{ container: classes.dialogContainer, paper: classes.dialogPaper }}
                BackdropProps={{ className: classes.dialogBackdrop }}
                onClose={handleClose}
            >
                <Grid
                    className={classes.dialogTitleHolder}
                    container
                    alignItems={'center'}
                    justifyContent={'space-between'}
                >
                    <Typography className={classes.dialogTitle}>{t('createYourAppointment')}</Typography>
                    <IconButton onClick={handleClose}>
                        <CloseIcon />
                    </IconButton>
                </Grid>
                <DialogContent className={classes.dialogContent} ref={dialogContentRef}>
                    {summary ? (
                        <BookingSummary appointment={summary} />
                    ) : (
                        <DatePicker
                            maxFutureDaysAppointment={staff?.maxFutureDaysAppointment}
                            businessStartingDate={business?.startingDate}
                            onLoadSlots={handleLoadSlots}
                            onSelect={setSlot}
                            slot={slot}
                            spacerRef={spacerRef}
                        />
                    )}

                    {error ? (
                        <Typography variant={'body1'} color={'error'}>
                            {error.message}
                        </Typography>
                    ) : null}
                </DialogContent>
                {summary ? null : (
                    <DialogActions className={classes.dialogActions}>
                        <Button onClick={handleClose} className={classes.dialogAction}>
                            {t('cancel')}
                        </Button>
                        <Button
                            className={classes.dialogAction}
                            variant={'contained'}
                            disabled={!slot || loading}
                            onClick={handleBooking}
                            startIcon={loading ? <CircularProgress size={18} color={'inherit'} /> : null}
                        >
                            {t('bookNow')}
                        </Button>
                    </DialogActions>
                )}
            </Dialog>
        </>
    );
}
