@use '../business/shared/styles.module';

.root {
    @include styles.card();
}

.headline.headline {
    @include styles.headline();
    margin-bottom: 14px;
}

.list.list {
    list-style-type: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    margin: 0 0 4px;
}

.listItem.listItem {
    display: flex;
    width: 100%;
    align-items: flex-start;
    justify-content: flex-start;
    margin-bottom: 10px;
    &:last-child {
        margin-bottom: 0;
    }
}

.listItemLeft.listItemLeft {
    font-style: normal;
    font-weight: 600;
    font-size: 16px;
    line-height: 30px;
    letter-spacing: 0.005em;
    color: #111;

    display: inline-flex;
    flex-grow: 1;
}

.listItemRight.listItemRight {
    width: 128px;
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    letter-spacing: 0.005em;
    color: #757575;

    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
}

.listItemHour {
    display: inline-flex;
    width: 100%;
}
