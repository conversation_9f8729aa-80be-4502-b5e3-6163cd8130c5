import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { convertHoursToLocalTimezone } from '../../constants/constants';
// import {BusinessEntity} from '../../entity/business.entity';
import { Hour } from '../../interfaces/hour.interface';
import classes from './business-schedule.module.scss';

interface BusinessScheduleProps {
    // hours: BusinessEntity['hours'];
    hours: any;
}

const Days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
const format = (data: Hour[]) => {
    if (!data || data?.length === 0) {
        return 'Closed';
    }

    return data.map((hour) => (
        <span key={hour.id} className={classes.listItemHour}>
            {`${hour.start} - ${hour.end}`}
        </span>
    ));
};

export function BusinessSchedule({ hours }: BusinessScheduleProps) {
    const { t } = useTranslation('businessPage');
    const { t: commonT } = useTranslation('common');

    return (
        <div className={classes.root}>
            <Typography className={classes.headline}>{t('schedule')}</Typography>
            <ul className={classes.list}>
                {Days.map((day, index) => (
                    <li className={classes.listItem} key={index}>
                        <Typography className={classes.listItemLeft}>{commonT(day)}</Typography>
                        <Typography className={classes.listItemRight}>
                            {format(
                                convertHoursToLocalTimezone(
                                    hours.filter((hour: Hour) => hour.day === day.toUpperCase()),
                                ),
                            )}
                        </Typography>
                    </li>
                ))}
            </ul>
        </div>
    );
}
