/* eslint-disable no-unused-vars */
import Typography from '@mui/material/Typography';
import React from 'react';
import { ReactComponent as Call } from '../../assets/icons/call.svg';
import { ReactComponent as Facebook } from '../../assets/icons/facebook.svg';
import { ReactComponent as Instagram } from '../../assets/icons/instagram.svg';
import { ReactComponent as Website } from '../../assets/icons/website.svg';
import classes from './business-contact.module.scss';

interface BusinessContactProps {
    website?: string;
    phone?: string;
    facebook?: string;
    instagram?: string;
}

const format = (url: string) => {
    const [head] = url.split('?');
    const segments = `${head}`.replace(/\/$/, '').split('/');
    const last = segments[segments.length - 1];

    return last ? `/${last}` : url;
};

export function BusinessContact({ website, phone, facebook, instagram }: BusinessContactProps) {
    return (
        <div className={classes.root}>
            <Typography className={classes.headline}>Contact & Social Media</Typography>
            <ul className={classes.list}>
                {phone && (
                    <li className={classes.listItem} title={phone}>
                        <span className={classes.listItemIcon}>
                            <Call />
                        </span>
                        <Typography component={'a'} href={`tel:${phone}`} className={classes.listItemText}>
                            {phone}
                        </Typography>
                    </li>
                )}
                {website && (
                    <li className={classes.listItem} title={website}>
                        <span className={classes.listItemIcon}>
                            <Website />
                        </span>
                        <Typography component={'a'} href={website} className={classes.listItemText}>
                            {website}
                        </Typography>
                    </li>
                )}
                {instagram && (
                    <li className={classes.listItem} title={instagram}>
                        <span className={classes.listItemIcon}>
                            <Instagram />
                        </span>
                        <Typography component={'a'} href={instagram} className={classes.listItemText}>
                            {format(instagram)}
                        </Typography>
                    </li>
                )}
                {facebook && (
                    <li className={classes.listItem} title={facebook}>
                        <span className={classes.listItemIcon}>
                            <Facebook />
                        </span>
                        <Typography component={'a'} href={facebook} className={classes.listItemText}>
                            {format(facebook)}
                        </Typography>
                    </li>
                )}
            </ul>
        </div>
    );
}
