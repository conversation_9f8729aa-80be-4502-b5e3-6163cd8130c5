@use '../business/shared/styles.module';

.root {
    @include styles.card();
}

.headline.headline {
    @include styles.headline();
    margin-bottom: 14px;
}

.list.list {
    list-style-type: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    margin: 0 0 4px;
}

.listItem.listItem {
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 10px;
    &:last-child {
        margin-bottom: 0;
    }
}

.listItemIcon.listItemIcon {
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    margin-right: 14px;
}

.listItemText.listItemText {
    font-style: normal;
    font-weight: 500;
    font-size: 16px;
    line-height: 30px;
    letter-spacing: 0.005em;
    color: #545454;
    text-decoration: none;
    &:hover {
        color: #000;
    }
}
