@use '../../../styles/mixins';

.logo.logo {
    padding-left: calc(50% - 118px / 2 - 585px);
    height: 27px;
    @include mixins.mdscreen() {
        justify-content: center;
    }
}
.list.list {
    display: flex;
    flex-direction: row;
    align-content: space-around;
    @include mixins.mdscreen() {
        display: none;
    }
}
.logoContainer.logoContainer {
    display: flex;
    flex-direction: row;
    align-items: center;
    @include mixins.mdscreen() {
        width: 100%;
        align-content: center;
        justify-content: center;
    }
}
.actions.actions {
    @include mixins.mdscreen() {
        display: none;
    }
}
