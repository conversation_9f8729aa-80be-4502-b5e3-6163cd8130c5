import LanguageIcon from '@mui/icons-material/Language';
import MenuIcon from '@mui/icons-material/Menu';
import AppBar from '@mui/material/AppBar';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import IconButton, { IconButtonProps } from '@mui/material/IconButton';
import MuiLink from '@mui/material/Link';
import TextField from '@mui/material/TextField';
import Toolbar from '@mui/material/Toolbar';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import classNames from 'classnames';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { CountrySelect, CountrySelectVariant } from '@bookr-technologies/ui/CountrySelect';
import { ReactComponent as DarkLogo } from '../../../assets/icons/dark-bookr.svg';
import { ReactComponent as LightLogo } from '../../../assets/icons/light-bookr.svg';
import { ApplyNowButton } from '../../ApplyNowButton';

export interface HeaderNavigationProps {
    dark?: boolean | string;
    disableElevation?: boolean;
    onClick: IconButtonProps['onClick'];
}

const Root = styled(AppBar)(({ theme }) => ({
    backgroundColor: '#fff',
    padding: theme.spacing(2.25, 0),
    '&.dark': {
        backgroundColor: '#070707',
        boxShadow: 'none',
        '.HeaderNavigation-navLink, .toggleIcon': {
            color: '#fff',
        },
        '.HeaderNavigation-cta': {
            backgroundColor: '#fff',
            color: '#111',
        },
    },
    [theme.breakpoints.down('md')]: {
        padding: theme.spacing(0),
    },
    '.HeaderNavigation-menuIcon': {
        alignItems: 'center',
        justifyContent: 'center',
        display: 'none',
        [theme.breakpoints.down('md')]: {
            display: 'flex',
        },
    },
    '.HeaderNavigation-brand': {
        display: 'flex',
    },
    '.HeaderNavigation-nav': {
        display: 'flex',
        alignItems: 'center',
        marginLeft: theme.spacing(10),
        [theme.breakpoints.down('md')]: {
            display: 'none',
        },
    },
    '.HeaderNavigation-navLink': {
        display: 'inline-flex',
        padding: theme.spacing(1),
        margin: theme.spacing(0, 4, 0, 0),
        transition: theme.transitions.create('color'),
        '&:hover': {
            color: '#2F80FB',
        },
    },
}));

export function HeaderNavigation({ dark, disableElevation, onClick }: HeaderNavigationProps) {
    const { t, i18n } = useTranslation('homepage');
    const [languageOpen, setLanguageOpen] = useState(false);
    const isDownSm = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

    const handleChangeLanguage = (value: string) => {
        i18n.changeLanguage(value);
        setLanguageOpen(false);
    };

    return (
        <Root
            position="static"
            className={classNames({ dark })}
            sx={typeof dark === 'string' ? { '&&': { bgcolor: dark } } : {}}
            elevation={disableElevation ? 0 : undefined}
        >
            <Toolbar className={'header'}>
                <IconButton
                    edge="start"
                    color="inherit"
                    className={'HeaderNavigation-menuIcon'}
                    aria-label="menu"
                    onClick={onClick}
                >
                    <MenuIcon className={'toggleIcon'} />
                </IconButton>

                <Container sx={{ pr: 0 }}>
                    <Grid container alignItems={'center'}>
                        <a href={'/'} className={'HeaderNavigation-brand'}>
                            {dark ? <LightLogo height={28} /> : <DarkLogo height={28} />}
                        </a>

                        <nav className={'HeaderNavigation-nav'}>
                            <MuiLink
                                component={Link}
                                to={'/beauty'}
                                className={'HeaderNavigation-navLink'}
                                variant={'subtitle1'}
                                color={'secondary'}
                                fontWeight={500}
                                underline={'none'}
                            >
                                Beauty
                            </MuiLink>
                            <MuiLink
                                component={Link}
                                to={'/medical'}
                                className={'HeaderNavigation-navLink'}
                                variant={'subtitle1'}
                                color={'secondary'}
                                fontWeight={500}
                                underline={'none'}
                            >
                                Medical
                            </MuiLink>
                        </nav>

                        <LanguageIcon
                            onClick={() => setLanguageOpen((l) => !l)}
                            sx={{ ml: 'auto', color: dark ? '#fff' : '#000', cursor: 'pointer' }}
                            className={classNames({ dark })}
                        />
                        <CountrySelect
                            variant={CountrySelectVariant.Language}
                            value={i18n.language || 'ro-RO'}
                            size={'small'}
                            open={languageOpen}
                            onChange={(event, value) => {
                                handleChangeLanguage(value?.code ?? 'ro-RO');
                            }}
                            sx={{ mr: 3 }}
                            renderInput={(params) => (
                                <TextField {...params} disabled sx={{ opacity: 0, width: 10, ml: -24 }} />
                            )}
                        />

                        {!isDownSm && (
                            <ApplyNowButton
                                variant={'contained'}
                                color={'secondary'}
                                className={'HeaderNavigation-cta'}
                                sx={{ py: 1.25, px: 3, borderRadius: 2.5 }}
                            >
                                {t('startNow')}
                            </ApplyNowButton>
                        )}
                    </Grid>
                </Container>
            </Toolbar>
        </Root>
    );
}
