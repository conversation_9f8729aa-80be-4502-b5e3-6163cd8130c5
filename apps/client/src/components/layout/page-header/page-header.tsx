import CloseIcon from '@mui/icons-material/Close';
import Drawer from '@mui/material/Drawer';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import React, { Fragment, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { ReactComponent as DarkLogo } from '../../../assets/icons/dark-bookr.svg';
import { ReactComponent as LightLogo } from '../../../assets/icons/light-bookr.svg';
import { ApplyNowButton } from '../../ApplyNowButton';
import { HeaderNavigation, HeaderNavigationProps } from './header-navigation';
import styleClasses from './page-header.module.scss';

const drawerWidth = 240;

interface PageHeaderProps {
    dark?: HeaderNavigationProps['dark'];
    disableElevation?: boolean;
}

const StyledDrawer = styled(Drawer)(({ theme }) => ({
    '.buttons': {
        display: 'flex',
        justifyContent: 'center',
        padding: theme.spacing(2, 4),
        '.MuiButton-root': {
            minWidth: 0,
        },
    },
    '.drawerPaper': {
        width: drawerWidth,
        backgroundColor: '#fff',
        [theme.breakpoints.up('md')]: {
            display: 'none',
        },
    },

    '&.dark': {
        '.drawerPaper': {
            backgroundColor: '#111',
        },
        '.MuiBackdrop-root': {
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
        },
        '.MuiListItem-root, .closeIcon': {
            color: '#fff',
        },
    },
}));

export function PageHeader({ dark, disableElevation }: PageHeaderProps) {
    const { t } = useTranslation('homepage');
    const [mobileOpen, setMobileOpen] = React.useState(false);
    const handleDrawerToggle = useCallback(() => {
        setMobileOpen((previousValue) => !previousValue);
    }, [setMobileOpen]);

    return (
        <Fragment>
            <HeaderNavigation dark={dark} disableElevation={disableElevation} onClick={handleDrawerToggle} />
            <StyledDrawer
                BackdropProps={{ invisible: false }}
                variant="temporary"
                anchor={'left'}
                open={mobileOpen}
                onClose={handleDrawerToggle}
                className={classNames({ dark })}
                classes={{ paper: 'drawerPaper' }}
            >
                <Grid container direction="column">
                    <Grid item xs container alignItems={'center'} py={1} pl={3}>
                        <IconButton
                            edge="start"
                            className={styleClasses['drawer-closeIcon']}
                            onClick={handleDrawerToggle}
                            aria-label="menu"
                        >
                            <CloseIcon className={'closeIcon'} />
                        </IconButton>
                        <Grid item xs pl={1} container alignItems={'center'}>
                            {dark ? <LightLogo height={28} /> : <DarkLogo height={28} />}
                        </Grid>
                    </Grid>
                    <Grid item>
                        <List>
                            <ListItem component={Link} className={styleClasses['drawer-listItem']} to="/">
                                {t('home')}
                            </ListItem>
                            <ListItem component={Link} className={styleClasses['drawer-listItem']} to="/medical">
                                Medical
                            </ListItem>
                            <ListItem component={Link} className={styleClasses['drawer-listItem']} to="/beauty">
                                Beauty
                            </ListItem>
                        </List>
                    </Grid>
                    <Grid container className={'buttons'}>
                        <Grid item xs={12} display={'flex'} alignItems={'center'} justifyContent={'center'}>
                            <ApplyNowButton
                                variant={'contained'}
                                color={'secondary'}
                                size={'medium'}
                                sx={{
                                    backgroundColor: dark ? '#fff' : undefined,
                                    color: dark ? '#111' : '#fff',
                                }}
                            >
                                {t('startNow')}
                            </ApplyNowButton>
                        </Grid>
                    </Grid>
                </Grid>
            </StyledDrawer>
        </Fragment>
    );
}
