.root.root {
    width: 100%;
    min-height: 228px;
    border-radius: 8px;
    background-position: center bottom;
    background-size: cover;
    position: relative;
    display: flex;
    align-items: flex-end;
    box-sizing: border-box;
    padding: 72% 16px 16px;
}

.details.details {
    background-color: #ffffff;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
    border-radius: 16px;
    display: flex;
    padding-left: 14px;
    width: 100%;
}

.avatar.avatar {
    width: 52px;
    height: 52px;
    margin-top: 16px;
    margin-bottom: 16px;
}

.text.text {
    padding: 12px 24px 12px 16px;
    border-right: 1px solid #eee;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    flex-grow: 1;
}

.headline.headline {
    font-style: normal;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.005em;
}

.subHeadline.subHeadline {
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 14px;
    letter-spacing: 0.005em;
    color: #afafaf;
}

.actionHolder.actionHolder {
    min-width: 68px;
    display: flex;
    align-items: stretch;
    justify-content: center;
}

.action.action {
    color: #01a6ba;
    width: 100%;
    height: 100%;
    border-radius: 0 16px 16px 0;
}
