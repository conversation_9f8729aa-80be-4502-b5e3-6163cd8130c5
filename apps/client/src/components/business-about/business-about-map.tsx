import SendIcon from '@mui/icons-material/Send';
import Avatar from '@mui/material/Avatar';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import qs from 'query-string';
import React, { useCallback } from 'react';
import { useInitials } from '../../hooks/use-initials';
import classes from './business-about-map.module.scss';

interface BusinessAboutProps {
    businessName: string;
    businessImage: string;
    businessAddress: string;
    region?: {
        latitude: number;
        latitudeDelta: number;
        longitude: number;
        longitudeDelta: number;
    };
}

export function BusinessAboutMap({ businessName, businessImage, businessAddress, region }: BusinessAboutProps) {
    const center = region ? region.latitude + ',' + region.longitude : businessAddress;

    const params = qs.stringify({
        center,
        zoom: 13,
        size: '820x456',
        maptype: 'roadmap',
        key: 'AIzaSyABnTzuA_UCrj3poKssKiX9oGF7sJMFEiI',
        markers: `color:black|label:|${center}`,
    });
    const mapUrl = `https://maps.googleapis.com/maps/api/staticmap?${params}`;
    const initials = useInitials(businessName);

    const handleOpenMap = useCallback(() => {
        window.open('https://maps.google.com?q=' + region?.latitude + ',' + region?.longitude);
    }, [region?.latitude, region?.longitude]);

    return (
        <div className={classes.root} style={{ backgroundImage: `url(${mapUrl})` }}>
            <div className={classes.details}>
                <Avatar className={classes.avatar} src={businessImage}>
                    {initials}
                </Avatar>

                <div className={classes.text}>
                    <Typography variant={'body2'} className={classes.headline}>
                        {businessName}
                    </Typography>
                    <Typography variant={'caption'} color={'textSecondary'} className={classes.subHeadline}>
                        {businessAddress}
                    </Typography>
                </div>

                <div className={classes.actionHolder}>
                    <IconButton className={classes.action} onClick={handleOpenMap}>
                        <SendIcon />
                    </IconButton>
                </div>
            </div>
        </div>
    );
}
