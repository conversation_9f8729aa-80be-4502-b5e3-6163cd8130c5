import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { BusinessAboutMap } from './business-about-map';
import classes from './business-about.module.scss';

interface BusinessAboutProps {
    description: string;
    businessName: string;
    businessImage: string;
    businessAddress: string;
    region?: { latitude: number; latitudeDelta: number; longitude: number; longitudeDelta: number };
}

export function BusinessAbout({
    description,
    businessImage,
    businessName,
    businessAddress,
    region,
}: BusinessAboutProps) {
    const { t } = useTranslation('businessPage');
    return (
        <div className={classes.root}>
            <BusinessAboutMap
                businessImage={businessImage}
                businessName={businessName}
                businessAddress={businessAddress}
                region={region}
            />
            <Typography variant={'subtitle2'} className={classes.headline}>
                {t('aboutUs')}
            </Typography>
            <Typography variant={'body1'} color={'textSecondary'} className={classes.subHeadline}>
                {description}
            </Typography>
        </div>
    );
}
