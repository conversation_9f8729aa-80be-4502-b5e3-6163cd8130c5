import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { PeriodSwitch } from '@bookr-technologies/ui/PeriodSwitch';
import { PlanSelect } from '@bookr-technologies/ui/PlanSelect';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { PricingHero } from '../../pages/landings/PricingPage/PricingHero';

const Root = styled(Container)({
    maxWidth: 1440,
});

export function HomePricingPlans() {
    const { t } = useTranslation('pricingPlans');
    const [period, setPeriod] = useState(PeriodType.Monthly);

    return (
        <ThemeProvider>
            <Root>
                <PricingHero>
                    <PeriodSwitch value={period} onChange={setPeriod} />
                    {period === PeriodType.Monthly ? (
                        <Typography variant={'body2'} color={'accent.main'} fontWeight={500} align={'center'} mt={1}>
                            {t('promoText')}
                        </Typography>
                    ) : null}
                </PricingHero>
                <PlanSelect period={period} withCustom landings />
                <Grid container alignItems={'center'} justifyContent={'center'} pt={8} pb={5}>
                    <Link
                        href={`/compare-plans?period=${period.toLowerCase()}`}
                        color={'accent.main'}
                        target="_blank"
                        rel="noopener noreferrer"
                    >
                        {t('comparePlans')}
                    </Link>
                </Grid>
            </Root>
        </ThemeProvider>
    );
}
