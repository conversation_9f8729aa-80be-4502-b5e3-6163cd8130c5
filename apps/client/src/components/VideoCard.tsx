import PlayCircleIcon from '@mui/icons-material/PlayCircle';
import Button from '@mui/material/Button';
import Grid, { GridProps } from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';

interface VideoCardProps extends GridProps {
    preview: string;
    mini?: boolean;
}

const Root = styled(Grid)(({ theme }) => ({
    borderRadius: 20,
    paddingBottom: '49%',
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#111',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    transform: 'translate3d(0, 0, 0)',
    '.VideoCard-play': {
        zIndex: 3,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: '100%',
        '& *': {
            color: '#fff',
        },
    },
    '.VideoCard-playIcon': {
        width: 90,
        height: 90,
    },
    '.VideoCard-overlay, .VideoCard-play': {
        position: 'absolute',
        top: '-1%',
        left: '-1%',
        right: '-1%',
        bottom: '-1%',
        width: '102%',
        height: '102%',
    },
    '.VideoCard-overlay': {
        backgroundImage: 'linear-gradient(0deg, #111 0%, rgba(17, 17, 17, 0) 80%);',
        zIndex: 1,
    },

    '&.mini': {
        '.VideoCard-play': {
            padding: theme.spacing(4),
            alignItems: 'flex-end',
            justifyContent: 'flex-end',
        },
        '.VideoCard-playIcon': {
            width: 40,
            height: 40,
        },
    },
}));

export function VideoCard({ preview, sx, mini, className, ...rest }: VideoCardProps) {
    return (
        <Root
            container
            alignItems={'center'}
            justifyContent={'center'}
            className={classNames('VideoCard-root', className, { mini })}
            sx={{ ...sx, backgroundImage: `url(${preview})` }}
            {...rest}
        >
            <div className={'VideoCard-overlay'} />
            <Button className={'VideoCard-play'}>
                <PlayCircleIcon className={'VideoCard-playIcon'} />
                {!mini ? (
                    <Typography variant={'body2'} fontWeight={500} mt={0.75}>
                        Play video
                    </Typography>
                ) : null}
            </Button>
        </Root>
    );
}
