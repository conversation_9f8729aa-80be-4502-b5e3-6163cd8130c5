@use '../../styles/breakpoints';

.root {
    background-color: #111;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    @include breakpoints.down('sm') {
        min-height: 0;
    }
}

.appBar {
    padding: 30px 24px;
    z-index: 3;
    background-color: #fff;
    width: 100%;

    @include breakpoints.down('sm') {
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.appBarContainer {
    display: flex;
    align-items: center;
}

.brand {
    display: block;
}

.brandImage {
    display: block;
    height: 28px;
    width: auto;
}

.hero {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    flex: 1 1 auto;
    padding: 140px 0;

    @include breakpoints.down('sm') {
        align-items: flex-start;
        padding: 0;
    }
}

.heroBackgroundLayers {
    position: absolute;
    z-index: 1;
    top: 0;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    @include breakpoints.down(1200) {
        opacity: 0.5;
    }

    @include breakpoints.down(600) {
        opacity: 0;
    }
}

.heroBackgroundLayer {
    position: absolute;
    z-index: 1;
    top: 50%;
    height: 100%;
    object-fit: contain;
    max-height: 800px;

    @include breakpoints.down('sm') {
        max-height: 400px;
        top: auto;
        bottom: 0;
    }
}

.heroBackgroundLayerLeft {
    left: 0;
    transform: translate(0, -50%);

    @include breakpoints.down('sm') {
        left: -25%;
    }
}

.heroBackgroundLayerRight {
    right: 0;
    transform: translate(0, -50%);

    @include breakpoints.down('sm') {
        right: -25%;
    }
}

.heroInner {
    position: relative;
    z-index: 3;
    padding: 96px 24px;
    @include breakpoints.down('sm') {
        padding-bottom: 180px;
    }
    @include breakpoints.down('xs') {
        br {
            display: none;
        }
    }
}

.heroCaption {
    color: #2f80fb;
    font-weight: 700;
    font-size: 18px;
    margin-bottom: 6px;

    @include breakpoints.down('sm') {
        font-size: 14px;
    }
}

.heroHeading,
.heroSubheading {
    color: #fff;
}

.heroHeading {
    font-weight: 700;
    margin-bottom: 24px;
    max-width: 742px;

    @include breakpoints.down('sm') {
        font-size: 28px;
        line-height: 42px;
    }

    span {
        color: #f05253;
    }
}

.heroSubheading {
    font-size: 18px;
    font-weight: 500;
    line-height: 30px;
    max-width: 568px;
    @include breakpoints.down('sm') {
        font-size: 16px;
        line-height: 24px;
    }
}

.heroButtons {
    margin-top: 64px;

    @include breakpoints.down('sm') {
        display: none;
    }
}

.nav {
    display: flex;
    align-items: center;
    margin-left: 80px;
}

.navLink {
    display: inline-flex;
    padding: 8px 52px 8px 0;
}
