import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import homeHeroLeft from '../../assets/home-hero/left-categories.jpg';
import homeHeroRight from '../../assets/home-hero/right-categories.jpg';
import AppStoreButtons from '../app-store-buttons/app-store-buttons';
import classes from './home-header.module.scss';

export function HomeHeaderHero() {
    const { t } = useTranslation('homepage');
    const [offset, setOffset] = useState(0);
    const [opacity, setOpacity] = useState(1);
    useEffect(() => {
        const handleScroll = () => {
            const offset = Math.ceil(window.scrollY / 10);
            setOffset(offset);
            setOpacity(Math.max(0, 1 - Math.ceil(window.scrollY / 0.8) / 1000));
        };

        window.addEventListener('scroll', handleScroll);
        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    return (
        <section className={classes.hero}>
            <div className={classes.heroBackgroundLayers}>
                <img
                    src={homeHeroLeft}
                    alt=""
                    style={{ transform: `translate3d(-${offset}px, -50%, 0)`, opacity }}
                    className={classNames(classes.heroBackgroundLayer, classes.heroBackgroundLayerLeft)}
                />
                <img
                    src={homeHeroRight}
                    alt=""
                    style={{ transform: `translate3d(${offset}px, -50%, 0)`, opacity }}
                    className={classNames(classes.heroBackgroundLayer, classes.heroBackgroundLayerRight)}
                />
            </div>
            <Container className={classes.heroInner}>
                <Grid container alignItems={'center'} justifyContent={'center'} direction={'column'}>
                    <Typography variant={'h3'} component={'h1'} className={classes.heroHeading} align={'center'}>
                        {t('heroTitle')}
                    </Typography>
                    <Typography
                        variant={'subtitle1'}
                        component={'h2'}
                        className={classes.heroSubheading}
                        align={'center'}
                    >
                        {t('heroSubtitle')}
                    </Typography>
                </Grid>
                <AppStoreButtons className={classes.heroButtons} />
            </Container>
        </section>
    );
}

export default HomeHeaderHero;
