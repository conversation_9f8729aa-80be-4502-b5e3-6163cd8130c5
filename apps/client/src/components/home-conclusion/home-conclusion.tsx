import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { ApplyNowButton } from '../ApplyNowButton';
import classes from './home-conclusion.module.scss';

export function HomeConclusion() {
    const { t } = useTranslation('homepage');
    return (
        <section className={classes.root}>
            <Container>
                <Grid container direction={'column'} alignItems={'center'} justifyContent={'center'}>
                    <Typography align={'center'} variant={'h2'} component={'h3'} className={classes.heading}>
                        <Trans t={t} i18nKey={'conclusionTitle'} components={{ br: <br /> }} />
                    </Typography>
                    <Typography align={'center'} variant={'h5'} component={'p'} className={classes.subheading}>
                        <Trans t={t} i18nKey={'conclusionDescription'} components={{ br: <br /> }} />
                    </Typography>
                    <ThemeProvider>
                        <ApplyNowButton
                            variant={'contained'}
                            color={'accent'}
                            size={'large'}
                            disableElevation
                            sx={{ minWidth: 240 }}
                        >
                            {t('startNow')}
                        </ApplyNowButton>
                    </ThemeProvider>
                </Grid>
            </Container>
        </section>
    );
}

export default HomeConclusion;
