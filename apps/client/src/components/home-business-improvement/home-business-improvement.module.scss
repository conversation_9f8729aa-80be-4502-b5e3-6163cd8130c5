@use '../../styles/breakpoints';

.root {
    min-height: 100vh;
    background-color: #111;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    @include breakpoints.down('md') {
        min-height: 0;
        padding: 80px 0;
    }
    @include breakpoints.down('sm') {
        padding-bottom: 72px;
    }
}
.businessBadge {
    background-color: #2f80fb;
    color: #fff;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    display: inline-flex;
    padding: 8px 24px;
    border-radius: 14px;
    @include breakpoints.down('sm') {
        display: none;
    }
}
.heading {
    color: #fff;
    margin-top: 48px;
    font-weight: 700;
    font-size: 58px;
    line-height: 74px;
    @include breakpoints.down('sm') {
        margin-top: 0;
        font-size: 32px;
        line-height: 42px;
    }
    @include breakpoints.down('xs') {
        br {
            display: none;
        }
    }
}
.stats {
    margin-top: 86px;
    @include breakpoints.down('sm') {
        margin-top: 56px;
    }
}
.statsItem {
    background-color: #333;
    border-radius: 20px;
    padding-top: 48px;
    padding-bottom: 24px;
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    @include breakpoints.down('sm') {
        flex-direction: row;
        padding: 40px 14px 40px 28px;
        align-items: center;
    }
}
.statsItemValue {
    font-weight: 700;
    font-size: 48px;
    line-height: 60px;
    color: #fff;
    margin-bottom: 20px;
    @include breakpoints.down('sm') {
        margin: 0;
        font-size: 32px;
        line-height: 42px;
        width: 100px;
        text-align: left;
    }
}
.statsItemLabel {
    color: #c8c8c8;
    font-size: 18px;
    line-height: 30px;
    font-weight: 500;
    padding-left: 14px;
    padding-right: 14px;
    @include breakpoints.down('sm') {
        font-size: 14px;
        line-height: 24px;
        text-align: left;
        width: calc(100% - 100px);
        padding: 0;
    }
}
