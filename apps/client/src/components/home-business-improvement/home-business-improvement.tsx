import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import classes from './home-business-improvement.module.scss';

const data = [
    { label: 'Creștere a programărilor per client', value: 20 },
    { label: 'Creștere în interacțiune', value: 350 },
    { label: 'Creștere a profitului', value: 15 },
    { label: 'Reducere in ne-prezentări datorita notificărilor', value: 35 },
];

export function HomeBusinessImprovement() {
    return (
        <section className={classes.root}>
            <Container>
                <Grid container direction={'column'} alignItems={'center'}>
                    <Typography variant={'button'} component={'p'} className={classes.businessBadge} align={'center'}>
                        Bookr Business
                    </Typography>
                    <Typography variant={'h2'} component={'h3'} className={classes.heading} align={'center'}>
                        Cât au crescut în doar 2 luni <br />
                        cei care au ales BOOKR?
                    </Typography>
                </Grid>

                <Grid container className={classes.stats} spacing={3}>
                    {data.map(({ value, label }, index) => (
                        <Grid container item sm={12} md={3} key={index}>
                            <div className={classes.statsItem}>
                                <Typography
                                    variant={'h3'}
                                    component={'h4'}
                                    className={classes.statsItemValue}
                                    align={'center'}
                                >
                                    {`${value}%`}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    color={'textSecondary'}
                                    className={classes.statsItemLabel}
                                    align={'center'}
                                >
                                    {label}
                                </Typography>
                            </div>
                        </Grid>
                    ))}
                </Grid>
            </Container>
        </section>
    );
}

export default HomeBusinessImprovement;
