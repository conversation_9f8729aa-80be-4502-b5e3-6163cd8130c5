import { createTheme, ThemeProvider as Provider } from '@mui/material/styles';
import React from 'react';
import { PropsWithChildren } from 'react';

const theme = createTheme({
    typography: {
        fontFamily: "'Poppins', 'React', sans-serif",
        h1: {
            fontFamily: "'Plus Jakarta Display', 'Poppins', 'React', sans-serif",
        },
        h2: {
            fontFamily: "'Plus Jakarta Display', 'Poppins', 'React', sans-serif",
        },
        h3: {
            fontFamily: "'Plus Jakarta Display', 'Poppins', 'React', sans-serif",
        },
        h4: {
            fontFamily: "'Plus Jakarta Display', 'Poppins', 'React', sans-serif",
            fontWeight: 'bold',
        },
        h5: {
            fontFamily: "'Plus Jakarta Display', 'Poppins', 'React', sans-serif",
            fontSize: 12,
        },
        h6: {
            fontFamily: "'Plus Jakarta Display', 'Poppins', 'React', sans-serif",
        },
        subtitle2: {
            fontSize: 18,
        },
    },
    palette: {
        primary: {
            main: '#01A6BA',
        },
        accent: {
            main: '#01A6BA',
        },
        secondary: {
            main: '#111',
        },
        error: {
            main: '#F94630',
        },
        warning: {
            main: '#FFC043',
        },
        success: {
            main: '#05944F',
        },
        common: {
            white: '#fff',
            black: '#000',
        },
        grey: {
            '400': '#AFAFAF',
            '600': '#545454',
            '50': '#F6F6F6',
        },
    },
    components: {
        MuiListItemIcon: {
            styleOverrides: {
                root: {
                    minWidth: 40,
                },
            },
        },
        MuiDrawer: {
            styleOverrides: {
                paper: {
                    backgroundColor: '#111',
                },
            },
        },
        MuiFormControlLabel: {
            styleOverrides: {
                label: {
                    fontWeight: 500,
                },
            },
        },
        MuiBackdrop: {
            styleOverrides: {
                root: {
                    backgroundColor: 'rgba(255,255,255,0.8)',
                },
            },
        },
        MuiButton: {
            defaultProps: {
                disableElevation: true,
            },
            styleOverrides: {
                root: {
                    borderRadius: 10,
                    lineHeight: '21px',
                    fontWeight: 500,
                    fontSize: 14,
                    letterSpacing: '0.0125em',
                    textTransform: 'capitalize',
                },
                containedSizeLarge: {
                    minWidth: 200,
                    padding: '14px 40px',
                    borderRadius: 20,
                    '& $label': {
                        fontSize: 18,
                    },
                },
            },
        },
        MuiFilledInput: {
            styleOverrides: {
                root: {
                    borderTopLeftRadius: 16,
                    borderTopRightRadius: 16,
                    borderBottomLeftRadius: 16,
                    borderBottomRightRadius: 16,
                    '&:before, &:after': {
                        display: 'none',
                    },
                },
                input: {
                    paddingLeft: 24,
                },
            },
        },
        MuiInputLabel: {
            styleOverrides: {
                filled: {
                    fontWeight: 500,
                    fontSize: '16px',
                    lineHeight: '24px',
                    transform: 'translate(24px, 16px) scale(1)',
                    '&$shrink': {
                        transform: 'translate(24px, 10px) scale(0.75)',
                    },
                },
            },
        },
        MuiCircularProgress: {
            styleOverrides: {
                root: {
                    display: 'block',
                },
            },
        },
    },
});

export function ThemeProvider({ children }: PropsWithChildren<any>) {
    return <Provider theme={theme}>{children}</Provider>;
}
