import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import { EmailAuthProvider, PhoneAuthProvider } from 'firebase/auth';
import * as firebaseui from 'firebaseui';
import React, { useCallback } from 'react';
import { StyledFirebaseAuth } from 'react-firebaseui';
import { FirebaseService } from '@bookr-technologies/core/services';
import { closeAuthDialog } from '../../redux/slices/auth.slice';
import { useAppDispatch, useAppSelector } from '../../redux/store';

// Configure FirebaseUI.
const uiConfig: firebaseui.auth.Config = {
    // Popup signin flow rather than redirect flow.
    signInFlow: 'popup',

    // We will display Phone Auth provider
    signInOptions: [
        {
            provider: EmailAuthProvider.PROVIDER_ID,
        },
    ],
    callbacks: {
        // Avoid redirects after sign-in.
        signInSuccessWithAuthResult: () => false,
    },
};

export function AuthDialog() {
    const dispatch = useAppDispatch();
    const open = useAppSelector((state) => state.auth.authDialog);

    const handleClose = useCallback(() => dispatch(closeAuthDialog()), [dispatch]);

    return (
        <Dialog open={open} onClose={handleClose} fullWidth maxWidth={'xs'} classes={{ paper: 'authDialog-paper' }}>
            <DialogContent className={'authDialog-content'}>
                <StyledFirebaseAuth uiConfig={uiConfig} firebaseAuth={FirebaseService.auth()} />
            </DialogContent>
        </Dialog>
    );
}
