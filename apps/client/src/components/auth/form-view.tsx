import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { Form, useFormikContext } from 'formik';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { FirebaseService } from '@bookr-technologies/core/services';
import { INITIAL_VALUES } from './data';
import classes from './form-view.module.scss';

interface FormViewProps {
    onCancel?: () => void;
}

export function FormView({ onCancel }: FormViewProps) {
    const {
        values,
        errors,
        touched,
        handleChange,
        handleBlur,
        setFieldValue,
        isValid,
        isSubmitting,
        submitForm,
        dirty,
    } = useFormikContext<typeof INITIAL_VALUES>();
    const [recaptchaError, setRecaptchaError] = useState(null);
    const [codeDialog, setCodeDialog] = useState(false);
    const recaptchaRef = useRef<RecaptchaVerifier | null>(null);

    const showCodeDialog = useCallback(() => setCodeDialog(true), [setCodeDialog]);
    const hideCodeDialog = useCallback(() => setCodeDialog(false), [setCodeDialog]);

    const handleConfirm = useCallback(() => {
        hideCodeDialog();
        submitForm();
    }, [hideCodeDialog, submitForm]);

    const handleSendVerification = useCallback(
        async function handleSendVerification() {
            setRecaptchaError(null);
            try {
                const confirmationResult = await signInWithPhoneNumber(
                    FirebaseService.auth(),
                    `${values.countryCallingCode}${values.phoneNumber}`,
                    recaptchaRef.current!,
                );

                setFieldValue('confirmationResult', confirmationResult);
                recaptchaRef.current?.clear();

                showCodeDialog();
                return true;
            } catch (e: any) {
                // handle verification id error
                console.log('error verifying number', e);
                setRecaptchaError(e.message);
                return false;
            }
        },
        [setFieldValue, showCodeDialog, values.countryCallingCode, values.phoneNumber],
    );

    useEffect(() => {
        recaptchaRef.current = new RecaptchaVerifier('recaptcha-container', {}, FirebaseService.auth());
        recaptchaRef.current?.render();
    }, []);

    return (
        <Grid component={Form} container direction={'column'}>
            <Grid container direction={'column'}>
                <Typography variant={'subtitle2'} color={'textSecondary'} style={{ width: '100%' }}>
                    {codeDialog ? 'Enter your confirmation code' : 'Connect with your phone number'}
                </Typography>

                <Grid container style={{ padding: 0, margin: 0, width: '100%' }}>
                    {codeDialog ? (
                        <Grid item xs className={classes.fieldColumnSingle}>
                            <TextField
                                variant={'filled'}
                                label={'Code'}
                                name={'verificationCode'}
                                error={!!(touched['verificationCode'] && errors['verificationCode'])}
                                helperText={errors['verificationCode']}
                                value={values.verificationCode}
                                onChange={handleChange}
                                onBlur={handleBlur}
                                fullWidth
                            />
                        </Grid>
                    ) : (
                        <>
                            <Grid item xs={4} className={classes.fieldColumnSingle}>
                                <TextField
                                    variant={'filled'}
                                    label={'Country'}
                                    name={'countryCallingCode'}
                                    error={!!(touched['countryCallingCode'] && errors['countryCallingCode'])}
                                    helperText={errors['countryCallingCode']}
                                    value={values.countryCallingCode}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    fullWidth
                                />
                            </Grid>

                            <Grid item xs={8} className={classes.fieldColumn}>
                                <TextField
                                    variant={'filled'}
                                    label={'Mobile Number'}
                                    name={'phoneNumber'}
                                    error={!!(touched['phoneNumber'] && errors['phoneNumber'])}
                                    helperText={errors['phoneNumber']}
                                    value={values.phoneNumber}
                                    onChange={handleChange}
                                    onBlur={handleBlur}
                                    fullWidth
                                />
                            </Grid>
                        </>
                    )}
                </Grid>

                <div id={'recaptcha-container'} style={{ width: '100%' }} />
                {recaptchaError && !codeDialog ? <Typography color={'error'}>{recaptchaError}</Typography> : null}
            </Grid>

            <Grid
                className={classes.actions}
                container
                direction={'row'}
                alignItems={'center'}
                justifyContent={'flex-end'}
            >
                {onCancel && <Button onClick={onCancel}>Cancel</Button>}

                <Button
                    variant={'contained'}
                    color={'secondary'}
                    disabled={!dirty || !isValid || isSubmitting}
                    onClick={codeDialog ? handleConfirm : handleSendVerification}
                    style={{ marginLeft: 8 }}
                >
                    {!isSubmitting ? (
                        codeDialog ? (
                            'Confirm'
                        ) : (
                            'Continue'
                        )
                    ) : (
                        <CircularProgress size={20} color={'inherit'} />
                    )}
                </Button>
            </Grid>
        </Grid>
    );
}

export default FormView;
