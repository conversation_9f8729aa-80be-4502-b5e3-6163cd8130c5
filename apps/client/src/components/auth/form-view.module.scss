@use '../../styles/mixins';

.root {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
}

.backButton {
    width: 50px;
    height: 50px;
    border: 1px solid #afafaf;
    border-radius: 16px;
}

.actions {
    padding-bottom: 14px;
}

:global(#recaptcha-container) {
    & {
        padding: 24px 0;
        margin: 0;
    }
    & > div {
        display: flex;
        justify-content: center;
    }
}
.fieldColumn {
    padding: 14px;
    @include mixins.mobile() {
        padding: 14px 8px;
    }
}
.fieldColumnSingle {
    padding-top: 14px;
    padding-bottom: 14px;
}
