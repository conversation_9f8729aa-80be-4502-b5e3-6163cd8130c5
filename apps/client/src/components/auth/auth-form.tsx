import { Formik } from 'formik';
import React, { useCallback } from 'react';
import { INITIAL_VALUES, validationSchema } from './data';
import { FormView } from './form-view';

interface AuthFormProps {
    onCancel?: () => void;
}

export function AuthForm({ onCancel }: AuthFormProps) {
    const handleSubmit = useCallback(async (values: any) => {
        try {
            await values.confirmationResult.confirm(values.verificationCode);
        } catch (e) {
            console.log('error signing in in with credentials', e);
            // handle sign in recaptchaError
        }
    }, []);

    return (
        <Formik initialValues={INITIAL_VALUES} onSubmit={handleSubmit} validationSchema={validationSchema}>
            {() => <FormView onCancel={onCancel} />}
        </Formik>
    );
}
