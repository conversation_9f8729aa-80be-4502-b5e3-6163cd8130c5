import { logEvent, setCurrentScreen } from 'firebase/analytics';
import React, { useEffect } from 'react';
import ReactPixel from 'react-facebook-pixel';
import { useTranslation } from 'react-i18next';
import { Route, Routes, useLocation } from 'react-router-dom';
import { FirebaseService } from '@bookr-technologies/core/services';
import { NotFoundPage } from './pages/errors/not-found';
import PricingPage from './pages/landings/PricingPage';
import { routes } from './routes';

const BlogPage = React.lazy(() => import('./pages/blog-homepage/blog-homepage'));
const BlogPostPage = React.lazy(() => import('./pages/blog-post/blog-post'));
const BusinessViewPage = React.lazy(() => import('./pages/business/view'));
const HomePage = React.lazy(() => import('./pages/home/<USER>'));
const BeautyLandingPage = React.lazy(() => import('./pages/landings/beauty'));
const MedicalLandingPage = React.lazy(() => import('./pages/landings/medical'));

export function Root() {
    const location = useLocation();
    const { i18n } = useTranslation();

    const logScreenChange = (pathname: string) => {
        setCurrentScreen(FirebaseService.analytics(), `pageview-${pathname}`);
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        logEvent(FirebaseService.analytics(), 'screen_view', { screen_name: `pageview-${pathname}` });
        ReactPixel.pageView();
    };

    useEffect(() => {
        const savedLanguage = localStorage.getItem('language') || 'ro-RO';
        i18n.changeLanguage(savedLanguage);
    }, [i18n]);

    useEffect(() => {
        if (location.pathname === routes.en) {
            i18n.changeLanguage('en-EN');
            localStorage.setItem('language', 'en-EN');
        }
        logScreenChange(location.pathname);
    }, [i18n, location.pathname]);
    return (
        <Routes>
            <Route path={routes.notFound} element={<NotFoundPage />} />
            <Route path={routes.beautyLanding} element={<BeautyLandingPage />} />
            <Route path={routes.medicalLanding} element={<MedicalLandingPage />} />
            <Route path={routes.blogHomepage} element={<BlogPage />} />
            <Route path={routes.blogPost} element={<BlogPostPage />} />
            <Route path={routes.businessPage} element={<BusinessViewPage />} />
            <Route path={routes.comparePlans} element={<PricingPage />} />
            <Route path={routes.root} element={<HomePage />} />
            <Route path={routes.en} element={<HomePage />} />
        </Routes>
    );
}
