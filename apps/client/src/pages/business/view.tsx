import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import { businessEndpoint } from '@bookr-technologies/api';
import { BusinessAbout } from '../../components/business-about/business-about';
import { BusinessContact } from '../../components/business-contact/business-contact';
import { BusinessGallery } from '../../components/business-gallery';
import { BusinessSchedule } from '../../components/business-schedule/business-schedule';
import { BusinessStaff } from '../../components/business-staff';
import { HomeFooter } from '../../components/home-footer';
import { Loader } from '../../components/loader/Loader';
import { useBusiness } from '../../redux/selectors/use-business';
import { useUser } from '../../redux/selectors/use-user';
import classes from './view.module.scss';

const isMaintenance = false;

export function BusinessViewPage() {
    const { t } = useTranslation('businessPage');
    const params = useParams<{ slug: string }>();
    const business = useBusiness(params.slug!);
    const user = useUser();

    useEffect(() => {
        if (!isMaintenance && business?.id && user?.uid) {
            businessEndpoint.visitBusiness(business.id);
        }
    }, [business, user?.uid]);

    if (!business) {
        return <Loader />;
    }

    if (!business.id) {
        return <Typography>Not Found!</Typography>;
    }

    document.title = 'BOOKR | ' + business.name;

    if (isMaintenance) {
        return <Typography>Under maintenance! <EMAIL></Typography>;
    }

    const region = {
        latitude: business.latitude,
        latitudeDelta: business.latitudeDelta,
        longitude: business.longitude,
        longitudeDelta: business.longitudeDelta,
    };

    return (
        <>
            <Container className={classes.root}>
                <Grid container direction={'column'} justifyContent={'center'} className={classes.header}>
                    <Typography variant={'h6'} component={'h1'} className={classes.headerHeadline}>
                        {business?.name}
                    </Typography>
                    <Typography
                        variant={'subtitle1'}
                        component={'h3'}
                        color={'textSecondary'}
                        className={classes.headerSubHeadline}
                    >
                        {business?.formattedAddress}
                    </Typography>
                </Grid>

                <Grid container direction={'row'} alignItems={'flex-start'}>
                    <Grid xs={12} sm={12} md item component={'aside'} className={classes.content}>
                        <BusinessGallery photos={business.photos} virtualTour={business?.virtualTourURL} />

                        <Grid container direction={'column'}>
                            <Typography variant={'h6'} className={classes.sectionTitle}>
                                {t('services')}
                            </Typography>
                            <BusinessStaff business={business} />
                        </Grid>
                    </Grid>
                    <Grid xs={12} sm={12} md item component={'aside'} className={classes.sidebar}>
                        <BusinessAbout
                            businessImage={business.profilePicture}
                            businessName={business.name}
                            businessAddress={business.formattedAddress}
                            description={business.description}
                            region={region}
                        />

                        <BusinessContact
                            phone={business.phoneNumber}
                            facebook={business.facebookURL}
                            instagram={business.instagramURL}
                            website={business.websiteURL}
                        />

                        <BusinessSchedule hours={business.workingHours} />
                    </Grid>
                </Grid>
            </Container>
            <HomeFooter />
        </>
    );
}

export default BusinessViewPage;
