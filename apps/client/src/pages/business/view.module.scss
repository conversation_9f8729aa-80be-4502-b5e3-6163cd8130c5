$maxSidebar: 435px;
$sidebarOffset: 110px;

.root {
}

.header {
    margin-top: 26px;
    padding: 14px 0;
}

.headerHeadline.headerHeadline {
    font-weight: 600;
    font-size: 24px;
    line-height: 36px;
    letter-spacing: 0.0025em;
    color: #1f1f1f;
}

.headerSubHeadline.headerSubHeadline {
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.005em;
    color: #757575;
}

.content {
    flex-grow: 1;
    margin-bottom: 24px !important;
    margin-right: $sidebarOffset !important;
    max-width: calc(100% - #{$sidebarOffset}) !important;
    @media screen and (max-width: 1199px) {
        margin-right: 30px !important;
        max-width: calc(100% - 30px) !important;
    }
    @media screen and (max-width: 959px) {
        margin-right: 0 !important;
        max-width: 100% !important;
    }
}

.sidebar {
    width: 100%;
    max-width: $maxSidebar !important;
    @media screen and (max-width: 959px) {
        max-width: 100% !important;
    }
}

.sectionTitle.sectionTitle {
    font-style: normal;
    font-weight: 600;
    font-size: 24px;
    line-height: 36px;
    letter-spacing: 0.0025em;
    color: #1f1f1f;
}
