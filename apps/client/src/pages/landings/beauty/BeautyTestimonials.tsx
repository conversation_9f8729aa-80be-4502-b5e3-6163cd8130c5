import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React, { useCallback, useMemo, useRef } from 'react';
import { Helmet } from 'react-helmet';
import Slider from 'react-slick';
import slideImage01 from '../../../assets/beauty/slide-image-01.jpg';
import slideImage02 from '../../../assets/beauty/slide-image-02.jpg';
import { BeautyTestimonialItem } from './BeautyTestimonialItem';

const Root = styled('section')`
    width: 100%;
    overflow: hidden;
    padding: ${({ theme }) => theme.spacing(12, 0)};
    .BeautyTestimonials-heading,
    .BeautyTestimonials-sliderButton {
        color: #fff;
    }

    .BeautyTestimonials-caption {
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
        color: #ffc043;
    }
    .BeautyTestimonials-heading {
        font-weight: 700;
        font-size: 46px;
        line-height: 60px;
        ${({ theme }) => theme.breakpoints.down('sm')} {
            font-size: 32px;
            line-height: 48px;
        }
    }

    .BeautyTestimonials-sliderButton {
        width: 34px;
        height: 34px;
        border: 2px solid #fff;
        margin: 0 ${({ theme }) => theme.spacing(1)};
        transition: ${({ theme }) => theme.transitions.create(['color', 'background-color'])};
        &:hover {
            background-color: #fff;
            color: #111;
        }
    }

    .BeautyTestimonials-testimonials {
        margin-top: 0;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .slick-slider {
        width: 100%;
        @media screen and (max-width: 1920px) {
            width: 180%;
        }
        ${({ theme }) => theme.breakpoints.down('sm')} {
            width: 260%;
            .VideoCard-play {
                justify-content: flex-start;
            }
        }
    }

    .slick-track {
        display: flex;
        align-items: center;
        position: relative;
    }

    .slick-slide {
        position: relative;
        opacity: 0.2;
        display: flex !important;
        width: 33.33334%;
        padding: 40px 0;
        .VideoCard-root {
            padding-bottom: 64%;
        }

        &,
        .BeautyTestimonialItem-profileInfo,
        .VideoCard-overlay,
        .VideoCard-play {
            transition: ${({ theme }) => theme.transitions.create(['opacity'])};
        }

        .BeautyTestimonialItem-profileInfo,
        .VideoCard-overlay,
        .VideoCard-play {
            opacity: 0;
        }

        & > div {
            width: 100%;
            flex: 1 1 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 24px;
            ${({ theme }) => theme.breakpoints.down('sm')} {
                padding: 8px;
            }
        }

        &.slick-current {
            &,
            .BeautyTestimonialItem-profileInfo,
            .VideoCard-overlay,
            .VideoCard-play {
                opacity: 1;
            }
            .VideoCard-root {
                box-shadow: 0 0 124px 40px #000;
            }
        }
    }
`;

export function BeautyTestimonials() {
    const slickRef = useRef<Slider | null>(null);
    const isDownSm = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

    const handlePrevious = useCallback(() => slickRef.current?.slickPrev(), []);
    const handleNext = useCallback(() => slickRef.current?.slickNext(), []);

    const controls = useMemo(
        () => (
            <Grid item xs container justifyContent={isDownSm ? 'center' : 'flex-end'}>
                <IconButton className={'BeautyTestimonials-sliderButton'} onClick={handlePrevious}>
                    <ChevronLeftIcon color={'inherit'} />
                </IconButton>
                <IconButton className={'BeautyTestimonials-sliderButton'} onClick={handleNext}>
                    <ChevronRightIcon color={'inherit'} />
                </IconButton>
            </Grid>
        ),
        [handleNext, handlePrevious, isDownSm],
    );

    return (
        <Root>
            <Helmet>
                <link
                    rel="stylesheet"
                    type="text/css"
                    charSet="UTF-8"
                    href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
                />
                <link
                    rel="stylesheet"
                    type="text/css"
                    href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
                />
            </Helmet>
            <Container>
                <Grid container alignItems={'flex-end'} mb={6}>
                    <Grid item xs={12} sm={6}>
                        <Typography variant={'button'} className={'BeautyTestimonials-caption'}>
                            Testimoniale
                        </Typography>
                        <Typography variant={'h3'} component={'h3'} className={'BeautyTestimonials-heading'}>
                            Ce spun oamenii {!isDownSm && <br />} despre BOOKR?
                        </Typography>
                    </Grid>

                    {!isDownSm ? controls : null}
                </Grid>
            </Container>

            <div className={'BeautyTestimonials-testimonials'}>
                <Slider
                    infinite={true}
                    centerMode
                    ref={slickRef}
                    className={'center'}
                    centerPadding={'0'}
                    slidesToShow={3}
                    slidesToScroll={1}
                    arrows={false}
                    swipeToSlide={false}
                    touchMove={false}
                >
                    <BeautyTestimonialItem
                        authorName={'Băldean Florin'}
                        authorPosition={'Fondator Distinct Barbershop'}
                        poster={slideImage01}
                    />
                    <BeautyTestimonialItem
                        authorName={'Băldean Florin'}
                        authorPosition={'Fondator Distinct Barbershop'}
                        poster={slideImage02}
                    />
                    <BeautyTestimonialItem
                        authorName={'Băldean Florin'}
                        authorPosition={'Fondator Distinct Barbershop'}
                        poster={slideImage01}
                    />
                    <BeautyTestimonialItem
                        authorName={'Băldean Florin'}
                        authorPosition={'Fondator Distinct Barbershop'}
                        poster={slideImage01}
                    />
                    <BeautyTestimonialItem
                        authorName={'Băldean Florin'}
                        authorPosition={'Fondator Distinct Barbershop'}
                        poster={slideImage02}
                    />
                    <BeautyTestimonialItem
                        authorName={'Băldean Florin'}
                        authorPosition={'Fondator Distinct Barbershop'}
                        poster={slideImage01}
                    />
                </Slider>
            </div>
            <Container>{isDownSm ? <Box pt={4}>{controls}</Box> : null}</Container>
        </Root>
    );
}

export default BeautyTestimonials;
