import ViewInArIcon from '@mui/icons-material/ViewInAr';
import Chip from '@mui/material/Chip';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import dashboardView from '../../../assets/beauty/dashboard-view.jpg';
import easyToUse from '../../../assets/beauty/easy-to-use.jpg';
import { ApplyNowButton } from '../../../components/ApplyNowButton';
import { AppStoreButtons } from '../../../components/app-store-buttons/app-store-buttons';
import { FeatureCard, FeatureCardImage } from '../../../components/home-features/FeatureCard';

const Root = styled('div')(({ theme }) => ({
    padding: theme.spacing(6, 0, 8),
    backgroundColor: '#070707',
    [theme.breakpoints.down('sm')]: {
        padding: theme.spacing(0, 0, 6),
    },
    '& .FeaturedCard-root .headline': {
        [theme.breakpoints.down('sm')]: {
            fontSize: 26,
            fontWeight: 600,
        },
    },
    '& .FeaturedCard-root .subHeadline': {
        color: '#AFAFAF !important',
        [theme.breakpoints.down('sm')]: {
            fontSize: 16,
            fontWeight: 500,
        },
    },
    '.FeaturedCard-root .MuiTypography-root, .text-white': {
        color: '#fff',
    },
    '& .FeatureCardImage-root': {
        maxWidth: 362,
        '&.mobileRight': {
            marginLeft: 'auto',
            marginRight: '0 !important',
        },
        [theme.breakpoints.down('sm')]: {
            maxWidth: 240,
            margin: '0 auto',
        },
    },
    '.BeautyFeatures-content': {
        padding: theme.spacing(6.25),
        [theme.breakpoints.down('sm')]: {
            padding: theme.spacing(4, 4),
        },
    },
}));

export function BeautyFeatures() {
    const { t } = useTranslation('beauty');
    const { t: homepageT } = useTranslation('homepage');
    const isDownMd = useMediaQuery<Theme>((theme) => theme.breakpoints.down('md'));
    const isDownSm = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

    return (
        <Root>
            <Container>
                <Grid container maxWidth={'850px !important'} mx={'auto'}>
                    <Typography
                        variant={isDownMd ? 'h4' : 'h3'}
                        component={'h2'}
                        align={'center'}
                        fontWeight={700}
                        mb={8}
                        width={'100%'}
                        className={'text-white'}
                    >
                        <Trans t={homepageT} i18nKey={'digitalizeBusiness'} components={{ br: <br /> }} />
                    </Typography>
                    <FeatureCard inverted alignItems={'flex-end'} className={'BeautyFeatures-dashboardView'}>
                        <Grid item xs className={'BeautyFeatures-dashboardViewContent'} p={isDownSm ? 4 : 6.25}>
                            <Typography variant={'h3'} fontWeight={700} mb={2.5} className={'headline'}>
                                {homepageT('feature6Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={600}
                                mb={4}
                            >
                                {homepageT('feature6Description')}
                            </Typography>
                            <AppStoreButtons justifyContent={'flex-start'} />
                        </Grid>
                        <FeatureCardImage src={easyToUse} alt={homepageT('feature6Title')} />
                    </FeatureCard>
                    <Grid container direction={'row'} mt={isDownSm ? 0 : 4} spacing={4}>
                        <Grid item xs container maxWidth={isDownSm ? '100%' : '360px !important'}>
                            <FeatureCard
                                sx={{ bgcolor: '#2f80fb' }}
                                className={'BeautyFeatures-content'}
                                alignItems={'center'}
                                flex={'1 1 auto'}
                            >
                                <Typography variant={'h3'} color={'#fff'} fontWeight={700}>
                                    <Trans t={t} i18nKey={'accessToClients'} components={{ br: <br /> }} />
                                </Typography>
                            </FeatureCard>
                        </Grid>

                        <Grid item xs container>
                            <FeatureCard
                                inverted
                                className={'BeautyFeatures-tour3d BeautyFeatures-content'}
                                alignItems={'flex-start'}
                                justifyContent={'flex-start'}
                            >
                                <ViewInArIcon sx={{ width: 54, height: 54, color: '#2f80fb' }} />
                                <Typography variant={'h3'} fontWeight={700} mt={0.5} mb={2.5} className={'headline'}>
                                    {homepageT('feature8Title')}
                                </Typography>
                                <Typography
                                    variant={'subtitle1'}
                                    color={'textSecondary'}
                                    fontSize={18}
                                    className={'subHeadline'}
                                    fontWeight={700}
                                    mb={4}
                                >
                                    {homepageT('feature8Description')}
                                </Typography>
                            </FeatureCard>
                        </Grid>
                    </Grid>

                    <FeatureCard inverted className={'BeautyFeatures-dashboardView'} mt={4}>
                        <Grid item xs className={'BeautyFeatures-dashboardViewContent BeautyFeatures-content'}>
                            <Chip
                                variant={'outlined'}
                                label={t('new')}
                                sx={{ color: '#fff', minWidth: 64, borderColor: '#fff', height: 24 }}
                            />
                            <Typography variant={'h3'} fontWeight={700} mb={2.5} className={'headline'} mt={1}>
                                {homepageT('feature11Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={600}
                                mb={4}
                            >
                                {homepageT('feature11Description')}
                            </Typography>
                            <ThemeProvider>
                                <ApplyNowButton
                                    variant={'contained'}
                                    color={'accent'}
                                    size={'small'}
                                    disableElevation
                                    sx={{ minWidth: '168px' }}
                                >
                                    {homepageT('learnMore')}
                                </ApplyNowButton>
                            </ThemeProvider>
                        </Grid>
                        <FeatureCardImage src={dashboardView} alt={'Dashboard'} className={'mobileRight'} />
                    </FeatureCard>
                </Grid>
            </Container>
        </Root>
    );
}
