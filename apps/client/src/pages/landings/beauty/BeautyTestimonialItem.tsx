import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React, { PropsWithChildren } from 'react';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { VideoCard } from '../../../components/VideoCard';

interface HomeTestimonialItemProps {
    poster: string;
    authorName: string;
    authorPosition: string;
}

const Root = styled('section')`
    width: 100%;
    transform: translate3d(0, 0, 0);
    .BeautyTestimonialItem-profileName,
    .BeautyTestimonialItem-profilePosition {
        color: #fff;
    }

    .BeautyTestimonialItem-profileInfo {
        position: absolute;
        z-index: 3;
        left: 0;
        bottom: 0;
        padding: 24px;
        width: 100%;
    }
`;

export function BeautyTestimonialItem({
    children,
    authorName,
    authorPosition,
    poster,
}: PropsWithChildren<HomeTestimonialItemProps>) {
    return (
        <Root className={'BeautyTestimonialItem-root'}>
            <Typography className={'BeautyTestimonialItem-text'}>{children}</Typography>
            <div className={'BeautyTestimonialItem-profile'}>
                <VideoCard preview={poster} mini />

                <ThemeProvider>
                    <div className={'BeautyTestimonialItem-profileInfo'}>
                        <Typography
                            variant={'h5'}
                            fontWeight={700}
                            mb={1}
                            className={'BeautyTestimonialItem-profileName'}
                        >
                            {authorName}
                        </Typography>
                        <Typography
                            variant={'subtitle1'}
                            fontWeight={500}
                            className={'BeautyTestimonialItem-profilePosition'}
                        >
                            {authorPosition}
                        </Typography>
                    </div>
                </ThemeProvider>
            </div>
        </Root>
    );
}
