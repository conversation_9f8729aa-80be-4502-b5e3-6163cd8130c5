import Grid from '@mui/material/Grid';
import React from 'react';
import { createGlobalStyle } from 'styled-components';
import { HomeConclusion } from '../../../components/home-conclusion/home-conclusion';
import { HomeFooter } from '../../../components/home-footer';
import { HomePricingPlans } from '../../../components/home-pricing-plans/home-pricing-plans';
import { PageHeader } from '../../../components/layout/page-header';
import { BeautyCards } from './BeautyCards';
import { BeautyCategories } from './BeautyCategories';
import { BeautyFeatures } from './BeautyFeatures';
import { BeautyHero } from './BeautyHero';

const GlobalStyle = createGlobalStyle`
    body {
        background-color: #070707 !important;
    }
`;

export function BeautyLandingPage() {
    return (
        <>
            <GlobalStyle />
            <PageHeader dark />
            <BeautyHero />
            <BeautyCategories />
            <BeautyCards />
            {/*<BeautyTestimonials />*/}
            <BeautyFeatures />
            <Grid bgcolor={'white'}>
                <HomePricingPlans />
            </Grid>
            <HomeConclusion />
            <HomeFooter />
        </>
    );
}
