import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled, useTheme } from '@mui/material/styles';
import React from 'react';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import Slider from 'react-slick';
import categoryBarber from '../../../assets/home-categories/barber.png';
import categoryHairstyling from '../../../assets/home-categories/hairstyling.png';
import categoryMakeUp from '../../../assets/home-categories/make-up.png';
import categoryMassage from '../../../assets/home-categories/massage.png';
import categoryNails from '../../../assets/home-categories/nails.png';

const Root = styled('div')(({ theme }) => ({
    backgroundColor: '#070707',
    '.text-white': {
        color: '#fff',
    },
    '.Category-card': {
        backgroundColor: '#333',
        borderRadius: 20,
        height: 280,
        position: 'relative',
        overflow: 'hidden',
        width: '100%',
        '@media screen and (max-width: 400px)': {
            maxWidth: 300,
        },
    },
    '.Category-headline': {
        color: '#fff',
        position: 'absolute',
        top: 0,
        left: 0,
        padding: theme.spacing(1.25, 2.25),
        zIndex: 5,
    },
    '.Category-image': {
        position: 'absolute',
        left: '-1%',
        right: '-1%',
        bottom: 0,
        width: '102%',
        objectFit: 'cover',
        zIndex: 1,
    },

    '.Category-overlay': {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100%',
        backgroundImage: 'linear-gradient(0deg, #070707 0%, rgba(0, 0, 0, 0) 75%)',
        zIndex: 3,
    },
}));

const categories = [
    { label: 'hairstyling', image: categoryHairstyling },
    { label: 'manicure', image: categoryNails },
    { label: 'massage', image: categoryMassage },
    { label: 'makeup', image: categoryMakeUp },
    { label: 'barber', image: categoryBarber },
];

export function BeautyCategories() {
    const theme = useTheme();
    const { t } = useTranslation('beauty');

    return (
        <Root>
            <Helmet>
                <link
                    rel="stylesheet"
                    type="text/css"
                    charSet="UTF-8"
                    href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick.min.css"
                />
                <link
                    rel="stylesheet"
                    type="text/css"
                    href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.6.0/slick-theme.min.css"
                />
            </Helmet>
            <Container sx={{ px: 1 }}>
                <Stack>
                    <Typography
                        variant={'h4'}
                        component={'h2'}
                        align={'center'}
                        fontWeight={700}
                        mb={8}
                        className={'text-white'}
                    >
                        {t('businessesInBeauty')}
                    </Typography>

                    <Box>
                        <Slider
                            infinite
                            autoplay
                            slidesToShow={categories.length}
                            arrows={false}
                            responsive={[
                                {
                                    breakpoint: theme.breakpoints.values.lg,
                                    settings: { slidesToShow: 4, slidesToScroll: 1 },
                                },
                                {
                                    breakpoint: theme.breakpoints.values.md,
                                    settings: { slidesToShow: 3, slidesToScroll: 1 },
                                },
                                {
                                    breakpoint: 768,
                                    settings: { slidesToShow: 2, slidesToScroll: 1 },
                                },
                                {
                                    breakpoint: 400,
                                    settings: { slidesToShow: 1, slidesToScroll: 1 },
                                },
                            ]}
                        >
                            {categories.map(({ label, image }, index) => (
                                <div key={index}>
                                    <Grid item xs container justifyContent={'center'} p={1}>
                                        <div className={'Category-card'}>
                                            <img className={'Category-image'} src={image} alt={label} />
                                            <div className={'Category-overlay'} />
                                            <Typography
                                                variant={'subtitle1'}
                                                fontWeight={700}
                                                className={'Category-headline'}
                                            >
                                                {t(label)}
                                            </Typography>
                                        </div>
                                    </Grid>
                                </div>
                            ))}
                        </Slider>
                    </Box>
                </Stack>
            </Container>
        </Root>
    );
}
