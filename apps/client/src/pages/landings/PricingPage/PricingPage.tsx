import Typography from '@mui/material/Typography';
import { Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { checkoutEndpoint } from '@bookr-technologies/api/endpoints/checkoutEndpoint';
import { CookieBanner } from '@bookr-technologies/ui/CookieBanner/CookieBanner';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import { PeriodSwitch } from '@bookr-technologies/ui/PeriodSwitch';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { useUrlQueryParams } from '@bookr-technologies/ui/hooks/useUrlQueryParams';
import { HomeFooter } from '../../../components/home-footer';
import { PageHeader } from '../../../components/layout/page-header';
import { PriceTabView } from './PriceTabView';
import { PricesView } from './PricesView';
import { PricingHero } from './PricingHero';

export function PricingPage() {
    const { t } = useTranslation('pricingPlans');
    const periodQueryParams = useUrlQueryParams('period');

    const isDownLg = useMediaQuery<Theme>((theme) => theme.breakpoints.down('lg'));
    const [period, setPeriod] = useState(
        periodQueryParams ? (String(periodQueryParams).toUpperCase() as PeriodType) : PeriodType.Monthly,
    );
    const plans = useQuery('checkout/subscriptions/plans', () =>
        checkoutEndpoint.getSubscriptionPlans({ withCustom: true }),
    );

    const children = useMemo(() => {
        if (plans.isLoading) {
            return (
                <PageLoader>
                    <Typography variant={'body1'} color={'#afafaf'} pt={1}>
                        {t('loading')}
                    </Typography>
                </PageLoader>
            );
        }

        if (plans.error || !plans.data) {
            return (
                <PageLoader>
                    <Typography variant={'body1'} color={'#afafaf'} pt={1}>
                        {t('errorLoadingPrices')}
                    </Typography>
                </PageLoader>
            );
        }

        return !isDownLg ? (
            <PricesView period={period} plans={plans.data} />
        ) : (
            <PriceTabView period={period} plans={plans.data} />
        );
    }, [plans.isLoading, plans.error, plans.data, isDownLg, period, t]);

    return (
        <>
            <PageHeader disableElevation />

            <ThemeProvider>
                <PricingHero>
                    <PeriodSwitch value={period} onChange={setPeriod} />

                    {period === PeriodType.Monthly ? (
                        <Typography variant={'body2'} color={'accent.main'} fontWeight={500} align={'center'} mt={1}>
                            {t('promoText')}
                        </Typography>
                    ) : null}
                </PricingHero>

                {children}

                <HomeFooter />
                <CookieBanner />
            </ThemeProvider>
        </>
    );
}
