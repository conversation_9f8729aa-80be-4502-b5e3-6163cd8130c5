import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import { useMemo } from 'react';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { PlanFeatures } from './PlanFeatures';
import { PricesFeaturesView } from './PricesFeaturesView';
import { PricesPlanView } from './PricesPlanView';

interface Props {
    period: PeriodType;
    plans: Record<string, SubscriptionPlanModel[]>;
}

const Root = styled(Container)(({ theme }) => ({
    maxWidth: 1440,
    margin: theme.spacing(2, 'auto', 10, 'auto'),
    '.PricesView-sticky': {
        position: 'sticky',
        top: 16,
        zIndex: 3,
        '&:not(.PricesPlanView-recommended)': {
            backgroundColor: 'rgba(255, 255, 255, 0.7)',
            backdropFilter: 'blur(14px) saturate(280%)',
            top: 0,
            paddingTop: theme.spacing(7),
        },
    },
}));

export function PricesView({ plans, period }: Props) {
    const list = useMemo(() => SubscriptionPlanModel.getSortedPlansByPeriod(plans, period), [plans, period]);

    return (
        <Root>
            <Grid container className={'PricesView-sticky'} alignItems={'flex-end'}>
                <Grid item xs>
                    <PlanFeatures planType={'standard'} onlyFeatures />
                </Grid>
                {list.map(({ planName, price }) =>
                    price ? (
                        <Grid item xs maxWidth={'268px !important'} key={planName}>
                            <PricesPlanView
                                planName={planName}
                                plan={price}
                                period={period}
                                recommended={SubscriptionPlanModel.isRecommended(planName)}
                                custom={SubscriptionPlanModel.isCustom(price)}
                                className={'PricesView-sticky'}
                            />
                            <PricesFeaturesView
                                planName={planName}
                                recommended={SubscriptionPlanModel.isRecommended(planName)}
                            />
                        </Grid>
                    ) : null,
                )}
            </Grid>
        </Root>
    );
}
