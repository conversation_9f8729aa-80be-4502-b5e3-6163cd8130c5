import Grid, { GridProps } from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';

export function PricingHero({ children, ...rest }: GridProps) {
    const { t } = useTranslation('pricingPlans');

    return (
        <Grid
            container
            direction={'column'}
            alignItems={'center'}
            justifyContent={'center'}
            pt={18}
            pb={10}
            px={2}
            {...rest}
        >
            <Typography variant={'h2'} component={'h1'} fontWeight={700} align={'center'} mb={4} maxWidth={800}>
                {t('title')}
            </Typography>
            <Typography
                variant={'body1'}
                component={'h2'}
                fontWeight={500}
                align={'center'}
                color={'textSecondary'}
                maxWidth={554}
                mb={4}
            >
                {t('subtitle')}
            </Typography>

            {children}
        </Grid>
    );
}
