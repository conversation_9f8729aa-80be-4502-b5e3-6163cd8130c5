import Container from '@mui/material/Container';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { alpha } from '@mui/material/styles';
import { styled } from '@mui/material/styles';
import React, { useMemo, useState } from 'react';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { PlanFeatures } from './PlanFeatures';
import { PricesPlanView } from './PricesPlanView';

const Root = styled(Container)(({ theme }) => ({
    '.PriceTabView-sticky': {
        position: 'sticky',
        top: 0,
        backdropFilter: 'blur(14px) saturate(280%)',
        backgroundColor: 'rgba(255, 255, 255, 0.7)',
        margin: theme.spacing(0, -2),
        padding: theme.spacing(0, 2),
        width: `calc(100% + ${theme.spacing(2)})`,
    },

    '.MuiTabs-indicator': {
        display: 'none',
    },
    '.MuiTab-root': {
        padding: theme.spacing(1.5, 2.5),
        margin: theme.spacing(1, 1, 1, 0),
        minHeight: 0,
        fontWeight: 600,
    },
    '.MuiTab-root.Mui-selected': {
        backgroundColor: alpha(theme.palette.accent.main, 0.1),
        color: theme.palette.accent.main,
        borderRadius: 14,
    },
}));

interface PriceTabViewProps {
    period: PeriodType;
    plans: Record<string, SubscriptionPlanModel[]>;
}

export function PriceTabView({ period, plans }: PriceTabViewProps) {
    const [planName, setPlanName] = useState('professional');

    const handlePricingPlanTabChange = (event: React.SyntheticEvent, newValue: string) => {
        setPlanName(newValue);
    };
    const list = useMemo(() => SubscriptionPlanModel.getSortedPlansByPeriod(plans, period), [plans, period]);

    const activePlan = useMemo(
        () => list.find((plan) => String(plan.planName).toLowerCase() === String(planName).toLowerCase())?.price,
        [planName, list],
    );

    return (
        <ThemeProvider>
            <Root>
                <section className={'PriceTabView-sticky'}>
                    <Tabs variant={'scrollable'} value={planName} onChange={handlePricingPlanTabChange}>
                        <Tab disableRipple value={'free'} label={SubscriptionPlanModel.getPlanName('free')} />
                        <Tab disableRipple value={'standard'} label={SubscriptionPlanModel.getPlanName('standard')} />
                        <Tab
                            disableRipple
                            value={'professional'}
                            label={SubscriptionPlanModel.getPlanName('professional')}
                        />
                        <Tab disableRipple value={'custom'} label={SubscriptionPlanModel.getPlanName('custom')} />
                    </Tabs>
                    {activePlan ? (
                        <PricesPlanView
                            recommended={SubscriptionPlanModel.isRecommended(planName)}
                            custom={SubscriptionPlanModel.isCustom(planName)}
                            plan={activePlan}
                            period={period}
                            planName={planName}
                        />
                    ) : null}
                </section>
                <PlanFeatures planType={planName} />
            </Root>
        </ThemeProvider>
    );
}
