const allFeatures = [
    {
        title: 'booking',
        features: [
            { title: 'staffMembers', value: 'unlimited' },
            { title: 'businessListing', value: true },
            { title: 'ownWebsite', value: true },
            { title: 'accessToCommunity', value: true },
            { title: 'activityHistory', value: true },
            { title: 'unlimitedBookings', value: true },
            { title: 'googleIntegration', value: true },
            { title: 'waitingList', value: true },
        ],
    },
    {
        title: 'businessManagement',
        features: [
            { title: 'salesReports', value: true },
            { title: 'performanceIndicators', value: true },
            { title: 'topProfessionals', value: true },
            { title: 'teamManagement', value: true },
        ],
    },
    {
        title: 'clientsManagement',
        features: [
            { title: 'customerRecord', value: true },
            { title: 'importClients', value: true },
            { title: 'listClients', value: true },
            { title: 'clientsDocuments', value: true },
        ],
    },
    {
        title: 'notifications',
        features: [
            { title: 'automaticNotifications', value: true },
            { title: 'reminders', value: true },
            { title: 'personalisedNotifications', value: true },
            { title: 'smsNotifications', value: true },
        ],
    },
    {
        title: 'assistance',
        features: [{ title: 'clientSupport', value: 'anytime' }],
    },
];

export const plansData = {
    free: {
        sections: [
            {
                title: 'booking',
                features: [
                    { title: 'staffMembers', value: 1 },
                    { title: 'businessListing', value: true },
                    { title: 'ownWebsite', value: true },
                    { title: 'accessToCommunity', value: true },
                    { title: 'activityHistory', value: true },
                    { title: 'unlimitedBookings', value: true },
                    { title: 'googleIntegration', value: true },
                    { title: 'waitingList', value: false },
                ],
            },
            {
                title: 'businessManagement',
                features: [
                    { title: 'salesReports', value: false },
                    { title: 'performanceIndicators', value: false },
                    { title: 'topProfessionals', value: false },
                    { title: 'teamManagement', value: 'limited' },
                ],
            },
            {
                title: 'clientsManagement',
                features: [
                    { title: 'customerRecord', value: false },
                    { title: 'importClients', value: false },
                    { title: 'listClients', value: false },
                    { title: 'clientsDocuments', value: false },
                ],
            },
            {
                title: 'notifications',
                features: [
                    { title: 'automaticNotifications', value: true },
                    { title: 'reminders', value: true },
                    { title: 'personalisedNotifications', value: false },
                    { title: 'smsNotifications', value: true },
                ],
            },
            {
                title: 'assistance',
                features: [{ title: 'clientSupport', value: false }],
            },
        ],
    },

    standard: {
        sections: [
            {
                title: 'booking',
                features: [
                    { title: 'staffMembers', value: 'unlimited' },
                    { title: 'businessListing', value: true },
                    { title: 'ownWebsite', value: true },
                    { title: 'accessToCommunity', value: true },
                    { title: 'activityHistory', value: true },
                    { title: 'unlimitedBookings', value: true },
                    { title: 'googleIntegration', value: true },
                    { title: 'waitingList', value: true },
                ],
            },
            {
                title: 'businessManagement',
                features: [
                    { title: 'salesReports', value: 'limited' },
                    { title: 'performanceIndicators', value: 'limited' },
                    { title: 'topProfessionals', value: false },
                    { title: 'teamManagement', value: true },
                ],
            },
            {
                title: 'clientsManagement',
                features: [
                    { title: 'customerRecord', value: true },
                    { title: 'importClients', value: false },
                    { title: 'listClients', value: true },
                    { title: 'clientsDocuments', value: false },
                ],
            },
            {
                title: 'notifications',
                features: [
                    { title: 'automaticNotifications', value: true },
                    { title: 'reminders', value: true },
                    { title: 'personalisedNotifications', value: false },
                    { title: 'smsNotifications', value: true },
                ],
            },
            {
                title: 'assistance',
                features: [{ title: 'clientSupport', value: 'limited' }],
            },
        ],
    },

    professional: {
        sections: allFeatures,
    },

    custom: {
        sections: allFeatures,
    },
};
