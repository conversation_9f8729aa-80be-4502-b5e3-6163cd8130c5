import Button from '@mui/material/Button';
import Grid, { GridProps } from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import classNames from 'classnames';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';

interface Props extends GridProps {
    planName: string;
    plan: SubscriptionPlanModel;
    period: PeriodType;
    recommended: boolean;
    custom: boolean;
}

const Root = styled(Grid)(({ theme }) => ({
    position: 'relative',
    padding: theme.spacing(5, 2),
    borderRadius: theme.spacing(2, 2, 0, 0),
    [theme.breakpoints.down('lg')]: {
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'flex-end',
        justifyContent: 'space-between',
        padding: theme.spacing(2, 0, 3),
    },
    '&.PricesPlanView-recommended': {
        [theme.breakpoints.up('lg')]: {
            backgroundColor: '#f6f6f6',
        },
    },
    '.PricesPlanView-recommendedBadge': {
        height: theme.spacing(4),
        lineHeight: theme.spacing(4),
        padding: theme.spacing(0, 2),
        display: 'inline-flex',
        backgroundColor: theme.palette.accent.main,
        color: theme.palette.accent.contrastText,
        borderRadius: 9,
        boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
        [theme.breakpoints.down('lg')]: {
            marginBottom: theme.spacing(2),
        },
        [theme.breakpoints.up('lg')]: {
            position: 'absolute',
            top: 0,
            left: '50%',
            transform: 'translate(-50%, -50%)',
        },
    },
    '.PricesPlanView-content': {
        marginBottom: theme.spacing(5),
        [theme.breakpoints.down('lg')]: {
            marginBottom: theme.spacing(0),
        },
    },
    '.PricesPlanView-action': {
        [theme.breakpoints.down('lg')]: {
            padding: theme.spacing(0.5, 2),
        },
    },
}));

export function PricesPlanView({ recommended, custom, plan, period, planName, className, ...rest }: Props) {
    const { t } = useTranslation('pricingPlans');
    const clsx = classNames('PricesPlanView-root', className, { 'PricesPlanView-recommended': recommended });
    const isDownXl = useMediaQuery<Theme>((theme) => theme.breakpoints.down('xl'));

    const href = useMemo(() => {
        if (custom) {
            return 'mailto:<EMAIL>?subject=Price%20offer&';
        }

        return `${window.location.protocol}//dashboard.${window.location.hostname}`;
    }, [custom]);

    return (
        <Root
            container
            direction={'column'}
            alignItems={'center'}
            justifyContent={'flex-end'}
            className={clsx}
            {...rest}
        >
            <div className={'PricesPlanView-content'}>
                {recommended ? (
                    <Typography
                        className={'PricesPlanView-recommendedBadge'}
                        variant={'body2'}
                        color={'primary'}
                        fontWeight={600}
                    >
                        {t('recommended')}
                    </Typography>
                ) : null}

                <Typography
                    variant={isDownXl ? 'h5' : 'h4'}
                    fontWeight={700}
                    align={isDownXl ? 'left' : 'center'}
                    mb={1}
                >
                    {SubscriptionPlanModel.getPlanName(planName)}
                </Typography>
                <Typography variant={isDownXl ? 'body1' : 'h5'} fontWeight={500} align={isDownXl ? 'left' : 'center'}>
                    {SubscriptionPlanModel.getPlanPrice(plan, period)}
                </Typography>
            </div>

            <Button
                variant={'contained'}
                size={isDownXl ? 'small' : 'medium'}
                color={recommended ? 'accent' : 'inherit'}
                href={href}
                className={'PricesPlanView-action'}
            >
                {custom ? t('contactUs') : t('startNow')}
            </Button>
        </Root>
    );
}
