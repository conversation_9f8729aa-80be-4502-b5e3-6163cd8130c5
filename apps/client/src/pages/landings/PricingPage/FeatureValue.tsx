import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface Props {
    value: string | number | boolean;
}
export function FeatureValue({ value }: Props) {
    const { t } = useTranslation('pricingPlans');

    if (typeof value === 'boolean') {
        return value ? <CheckIcon color={'accent'} /> : <CloseIcon color={'error'} />;
    }

    return (
        <Typography variant={'body2'} fontWeight={500} color={'textSecondary'} minHeight={24}>
            {t(String(value))}
        </Typography>
    );
}
