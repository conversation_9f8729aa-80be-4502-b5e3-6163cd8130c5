import Stack from '@mui/material/Stack';
import { useMemo } from 'react';
import { PlanFeaturesSection } from './PlanFeaturesSection';
import { plansData } from './data';

interface Props {
    planType: string;
    onlyValues?: boolean;
    onlyFeatures?: boolean;
}

export function PlanFeatures({ planType, onlyValues, onlyFeatures }: Props) {
    const data = useMemo(() => plansData[String(planType).toLowerCase() as keyof typeof plansData], [planType]);
    if (!planType || !data) {
        return null;
    }

    return (
        <Stack>
            {data.sections.map(({ title, features }, index) => (
                <PlanFeaturesSection
                    key={index}
                    title={title}
                    features={features}
                    onlyValues={onlyValues}
                    onlyFeatures={onlyFeatures}
                />
            ))}
        </Stack>
    );
}
