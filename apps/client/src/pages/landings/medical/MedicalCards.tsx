import AssessmentIcon from '@mui/icons-material/Assessment';
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import TouchAppIcon from '@mui/icons-material/TouchApp';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import analytics from '../../../assets/medical/analytics.jpg';
import captionBackground from '../../../assets/medical/caption-background.jpg';
import clientDetails from '../../../assets/medical/client-details.jpg';
import dashboardView from '../../../assets/medical/dashboard-view.jpg';
import notifications from '../../../assets/medical/notifications.jpg';
import simpleAppointments from '../../../assets/medical/simple-appointments.jpg';
import stats from '../../../assets/medical/stats.jpg';
import whyBookr from '../../../assets/medical/why-bookr.jpg';
import { ApplyNowButton } from '../../../components/ApplyNowButton';
import { FeatureCard, FeatureCardImage } from '../../../components/home-features/FeatureCard';
import { CaptionCard } from './CaptionCard';

const Root = styled('div')(({ theme }) => ({
    padding: theme.spacing(14, 0, 5),
    '.text-white': {
        color: '#fff',
    },
    '.MedicalCards-content': {
        padding: theme.spacing(6.25),
        [theme.breakpoints.down('sm')]: {
            padding: theme.spacing(4, 4),
        },
    },
    '& .FeaturedCard-root .headline': {
        [theme.breakpoints.down('sm')]: {
            fontSize: 26,
            fontWeight: 600,
        },
    },
    '& .FeaturedCard-root .subHeadline': {
        color: '#AFAFAF !important',
        [theme.breakpoints.down('sm')]: {
            fontSize: 16,
            fontWeight: 500,
        },
    },

    '& .FeaturedCard-root': {
        marginTop: theme.spacing(6),
        [theme.breakpoints.down('sm')]: {
            marginTop: theme.spacing(4),
        },
    },
    '& .FeatureCardImage-root': {
        maxWidth: 362,
        '&.mobileRight': {
            marginLeft: 'auto',
            marginRight: '0 !important',
        },
        [theme.breakpoints.down('sm')]: {
            maxWidth: 240,
            marginLeft: 'auto',
            marginRight: 'auto',
        },
    },
    '.MedicalCards-whyBookr': {
        backgroundColor: '#111',
    },
}));

export function MedicalCards() {
    const { t: homepageT } = useTranslation('homepage');
    const { t } = useTranslation('medical');
    const isDownMd = useMediaQuery<Theme>((theme) => theme.breakpoints.down('md'));
    const isDownSm = useMediaQuery<Theme>((theme) => theme.breakpoints.down('sm'));

    return (
        <Root>
            <Container>
                <Typography variant={isDownMd ? 'h4' : 'h3'} component={'h2'} align={'center'} fontWeight={700} mb={6}>
                    <Trans t={t} i18nKey={'bookrDashboardPatients'} components={{ br: <br /> }} />
                </Typography>

                {/*<Grid container maxWidth={'1068px !important'} m={'0 auto'}>*/}
                {/*    <VideoCard preview={videoPreview} />*/}
                {/*</Grid>*/}

                <Grid container maxWidth={'850px !important'} mt={4} mx={'auto'}>
                    <FeatureCard className={'MedicalCards-dashboardView'}>
                        <Grid item xs className={'MedicalCards-dashboardViewContent MedicalCards-content'}>
                            <Typography variant={'h3'} fontWeight={700} mb={2.5} className={'headline'}>
                                {t('feature1Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={500}
                                mb={4}
                            >
                                {t('feature1Description')}
                            </Typography>
                            <ThemeProvider>
                                <ApplyNowButton
                                    variant={'contained'}
                                    color={'accent'}
                                    size={'small'}
                                    disableElevation
                                    sx={{ minWidth: '168px' }}
                                >
                                    {homepageT('startNow')}
                                </ApplyNowButton>
                            </ThemeProvider>
                        </Grid>
                        <FeatureCardImage src={dashboardView} alt={t('feature1Title')} className={'mobileRight'} />
                    </FeatureCard>

                    <FeatureCard className={'MedicalCards-clientDetails'} mt={4}>
                        <FeatureCardImage src={clientDetails} alt={t('feature2Title')} />
                        <Grid item xs className={'MedicalCards-clientDetailsContent MedicalCards-content'}>
                            <Typography variant={'h3'} fontWeight={700} mb={2.5} className={'headline'}>
                                {t('feature2Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={500}
                            >
                                {t('feature2Description')}
                            </Typography>
                        </Grid>
                    </FeatureCard>

                    <FeatureCard className={'MedicalCards-simpleAppointments'} mt={6}>
                        <Grid item xs className={'MedicalCards-simpleAppointmentsContent MedicalCards-content'}>
                            <Typography variant={'h3'} fontWeight={700} mb={2.5} className={'headline'}>
                                {t('feature3Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={500}
                                mb={4}
                            >
                                {t('feature3Description')}
                            </Typography>
                            <ThemeProvider>
                                <ApplyNowButton
                                    variant={'contained'}
                                    color={'accent'}
                                    size={'small'}
                                    disableElevation
                                    sx={{ minWidth: '168px' }}
                                >
                                    {homepageT('startNow')}
                                </ApplyNowButton>
                            </ThemeProvider>
                        </Grid>
                        <FeatureCardImage src={simpleAppointments} alt={t('feature3Title')} />
                    </FeatureCard>

                    <CaptionCard image={captionBackground} mt={4}>
                        <ThemeProvider>
                            <Typography
                                variant={'h4'}
                                fontWeight={700}
                                className={'text-white'}
                                align={'center'}
                                mb={8}
                                mt={isDownMd ? 4 : 15}
                            >
                                {t('feature4Title')}
                            </Typography>

                            <Typography variant={'h5'} fontWeight={700} className={'text-white'} align={'center'}>
                                <Trans t={t} i18nKey={'medicalPlaces'} components={{ br: <br /> }} />
                            </Typography>
                        </ThemeProvider>
                    </CaptionCard>

                    <FeatureCard className={'MedicalCards-analytics'} mt={6}>
                        <FeatureCardImage src={analytics} alt={t('feature4Title')} />

                        <Grid item xs className={'MedicalCards-analyticsContent MedicalCards-content'}>
                            <Typography variant={'h3'} fontWeight={700} mb={2.5} className={'headline'}>
                                {t('feature5Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={500}
                                mb={4}
                            >
                                {t('feature5Description')}
                            </Typography>
                            <ThemeProvider>
                                <ApplyNowButton
                                    variant={'contained'}
                                    color={'accent'}
                                    size={'small'}
                                    disableElevation
                                    sx={{ minWidth: '168px' }}
                                >
                                    {homepageT('startNow')}
                                </ApplyNowButton>
                            </ThemeProvider>
                        </Grid>
                    </FeatureCard>

                    <FeatureCard className={'MedicalCards-notifications'} mt={6}>
                        <Grid item xs className={'MedicalCards-notificationsContent MedicalCards-content'}>
                            <Typography variant={'h3'} fontWeight={700} mb={2.5} className={'headline'}>
                                {homepageT('feature4Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={500}
                                mb={4}
                            >
                                {homepageT('feature4Description')}
                            </Typography>
                        </Grid>
                        <FeatureCardImage src={notifications} alt={homepageT('feature4Title')} />
                    </FeatureCard>

                    <FeatureCard className={'MedicalCards-stats'} mt={6}>
                        <FeatureCardImage src={stats} alt={homepageT('feature3Title')} />
                        <Grid item xs className={'MedicalCards-statsContent MedicalCards-content'}>
                            <Typography
                                variant={isDownSm ? 'h4' : 'h3'}
                                fontWeight={700}
                                mb={2.5}
                                className={'headline'}
                            >
                                {homepageT('feature3Title')}
                            </Typography>
                            <Typography
                                variant={'subtitle1'}
                                color={'textSecondary'}
                                fontSize={18}
                                className={'subHeadline'}
                                fontWeight={500}
                                mb={4}
                            >
                                {homepageT('feature3Description')}
                            </Typography>

                            <ThemeProvider>
                                <ApplyNowButton
                                    variant={'contained'}
                                    color={'primary'}
                                    size={'small'}
                                    disableElevation
                                    sx={{ minWidth: '168px' }}
                                >
                                    {homepageT('startNow')}
                                </ApplyNowButton>
                            </ThemeProvider>
                        </Grid>
                    </FeatureCard>

                    <FeatureCard inverted alignItems={'center'} className={'MedicalCards-whyBookr'} mt={6}>
                        <Grid item xs={6} className={'MedicalCards-whyBookrContent MedicalCards-content'}>
                            <Typography
                                variant={isDownSm ? 'h4' : 'h3'}
                                fontWeight={700}
                                className={'headline'}
                                sx={{ color: '#757575' }}
                            >
                                {t('why')}
                            </Typography>
                            <Typography
                                variant={isDownSm ? 'h4' : 'h3'}
                                fontWeight={700}
                                className={'headline text-white'}
                            >
                                BOOKR Dashboard?
                            </Typography>
                        </Grid>
                        <Grid item xs={6} fontSize={0}>
                            <FeatureCardImage src={whyBookr} alt={`${t('why')} Bookr Dashboard?`} />
                        </Grid>
                    </FeatureCard>

                    <ThemeProvider>
                        <Grid container spacing={3} mt={2}>
                            <Grid item sm={4} container>
                                <FeatureCard
                                    container
                                    p={4}
                                    alignItems={'center'}
                                    justifyContent={'flex-start'}
                                    direction={'column'}
                                >
                                    <TouchAppIcon sx={{ color: '#2f80fb', width: 46, height: 46 }} />
                                    <Typography variant={'h5'} fontWeight={700} mt={3.5} align={'center'}>
                                        {homepageT('feature6Title')}
                                    </Typography>
                                    <Typography
                                        mt={1.25}
                                        variant={'body1'}
                                        fontWeight={500}
                                        color={'textSecondary'}
                                        align={'center'}
                                    >
                                        {homepageT('feature6Description')}
                                    </Typography>
                                </FeatureCard>
                            </Grid>
                            <Grid item sm={4} container>
                                <FeatureCard
                                    container
                                    p={4}
                                    alignItems={'center'}
                                    justifyContent={'flex-start'}
                                    direction={'column'}
                                >
                                    <AssessmentIcon sx={{ color: '#2f80fb', width: 46, height: 46 }} />
                                    <Typography variant={'h5'} fontWeight={700} mt={3.5} align={'center'}>
                                        {t('predictability')}
                                    </Typography>
                                    <Typography
                                        mt={1.25}
                                        variant={'body1'}
                                        fontWeight={500}
                                        color={'textSecondary'}
                                        align={'center'}
                                    >
                                        {t('predictabilityDescription')}
                                    </Typography>
                                </FeatureCard>
                            </Grid>
                            <Grid item sm={4} container>
                                <FeatureCard
                                    container
                                    p={4}
                                    alignItems={'center'}
                                    justifyContent={'flex-start'}
                                    direction={'column'}
                                >
                                    <RocketLaunchIcon sx={{ color: '#2f80fb', width: 46, height: 46 }} />
                                    <Typography variant={'h5'} fontWeight={700} mt={3.5} align={'center'}>
                                        {t('userExperience')}
                                    </Typography>
                                    <Typography
                                        mt={1.25}
                                        variant={'body1'}
                                        fontWeight={500}
                                        color={'textSecondary'}
                                        align={'center'}
                                    >
                                        {t('userExperienceDescription')}
                                    </Typography>
                                </FeatureCard>
                            </Grid>
                        </Grid>
                    </ThemeProvider>
                </Grid>
            </Container>
        </Root>
    );
}
