import Grid, { GridProps } from '@mui/material/Grid';
import { styled } from '@mui/material/styles';
import { PropsWithChildren } from 'react';

interface CaptionCardProps extends GridProps {
    image: string;
}

const Root = styled(Grid)(({ theme }) => ({
    borderRadius: 20,
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#111',
    backgroundSize: 'cover',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    zIndex: 1,
    '.CaptionCard-content': {},

    '.CaptionCard-overlay': {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(17, 17, 17, 0.4)',
        zIndex: -1,
    },
}));

export function CaptionCard({ image, children, sx, ...rest }: PropsWithChildren<CaptionCardProps>) {
    return (
        <Root
            container
            alignItems={'center'}
            justifyContent={'center'}
            p={10}
            sx={{ ...sx, backgroundImage: `url(${image})` }}
            {...rest}
        >
            <div className={'CaptionCard-overlay'} />
            {children}
        </Root>
    );
}
