import Grid from '@mui/material/Grid';
import React from 'react';
import { createGlobalStyle } from 'styled-components';
import { HomeFooter } from '../../../components/home-footer';
import { HomePricingPlans } from '../../../components/home-pricing-plans/home-pricing-plans';
import { PageHeader } from '../../../components/layout/page-header';
import { MedicalCards } from './MedicalCards';
import { MedicalConclusion } from './MedicalConclusion';
import { MedicalHero } from './MedicalHero';

const GlobalStyle = createGlobalStyle`
    body {
        background-color: #f6f6f6 !important;
    }
`;

export function MedicalLandingPage() {
    return (
        <>
            <GlobalStyle />
            <PageHeader />
            <MedicalHero />
            <MedicalCards />
            <Grid bgcolor={'white'}>
                <HomePricingPlans />
            </Grid>
            <MedicalConclusion />
            <HomeFooter />
        </>
    );
}
