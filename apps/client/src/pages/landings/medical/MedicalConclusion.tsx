import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { ApplyNowButton } from '../../../components/ApplyNowButton';

export function MedicalConclusion() {
    const { t: homepageT } = useTranslation('homepage');
    const { t } = useTranslation('medical');
    return (
        <Box bgcolor={'#fff'} py={14}>
            <Container>
                <Grid
                    container
                    direction={'column'}
                    alignItems={'center'}
                    justifyContent={'center'}
                    maxWidth={840}
                    mx={'auto'}
                >
                    <Typography align={'center'} variant={'h3'} component={'h3'} fontWeight={700} mb={2}>
                        {t('conclusionTitle')}
                    </Typography>
                    <Typography
                        align={'center'}
                        variant={'body1'}
                        component={'p'}
                        fontWeight={500}
                        color={'textSecondary'}
                        mb={6}
                    >
                        {t('conclusionDescription')}
                    </Typography>
                    <ThemeProvider>
                        <ApplyNowButton
                            variant={'contained'}
                            color={'accent'}
                            size={'large'}
                            disableElevation
                            sx={{ minWidth: 240 }}
                        >
                            {homepageT('startNow')}
                        </ApplyNowButton>
                    </ThemeProvider>
                </Grid>
            </Container>
        </Box>
    );
}
