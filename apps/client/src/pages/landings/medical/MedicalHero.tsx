import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useTranslation } from 'react-i18next';
import MedicalHeroImage from '../../../assets/medical/medical-hero.jpg';
import { ApplyNowButton } from '../../../components/ApplyNowButton';

const Root = styled('div')(({ theme }) => ({
    display: 'flex',
    backgroundColor: '#111',
    padding: theme.spacing(16, 0, 18, 0),

    '.Hero-headline, .Hero-subHeadline': {
        marginBottom: theme.spacing(3),
        color: '#fff',
        maxWidth: 440,
    },
    '.Hero-button': {
        backgroundColor: '#fff',
        borderRadius: 20,
        padding: theme.spacing(2, 4),
        maxWidth: 260,
    },
    '.Hero-image': {
        width: '100%',
        height: '100%',
        padding: theme.spacing(8),
    },
}));

export function MedicalHero() {
    const { t: homepageT } = useTranslation('homepage');
    const { t } = useTranslation('medical');
    const isDownMd = useMediaQuery<Theme>((theme) => theme.breakpoints.down('md'));

    return (
        <Root>
            <Container>
                <Grid container>
                    <Grid
                        item
                        xs={12}
                        md={6}
                        container
                        direction={'column'}
                        alignItems={isDownMd ? 'center' : 'flex-start'}
                        justifyContent={'center'}
                    >
                        <Typography
                            className={'Hero-headline'}
                            variant={'h2'}
                            component={'h1'}
                            fontWeight={700}
                            align={isDownMd ? 'center' : 'left'}
                        >
                            {t('heroTitle')}
                        </Typography>
                        <Typography
                            className={'Hero-subHeadline'}
                            variant={'subtitle1'}
                            fontWeight={500}
                            align={isDownMd ? 'center' : 'left'}
                        >
                            {t('heroSubtitle')}
                        </Typography>
                        <ApplyNowButton variant={'contained'} className={'Hero-button'} fullWidth>
                            {homepageT('startNow')}
                        </ApplyNowButton>
                    </Grid>
                    {!isDownMd && (
                        <Grid item xs={6} container alignItems={'center'} justifyContent={'flex-end'}>
                            <img className={'Hero-image'} src={MedicalHeroImage} alt="Medical hero" />
                        </Grid>
                    )}
                </Grid>
            </Container>
        </Root>
    );
}
