import React from 'react';
import { Helmet } from 'react-helmet';
import { BlogHeader } from '../../components/blog/blog-header';
import { BlogPostLayout } from '../../components/blog/blog-post-layout';
import { HomeFooter } from '../../components/home-footer/home-footer';
import { Loader } from '../../components/loader/Loader';
import { useBlog } from '../../redux/selectors/use-blog-post';

const BlogHomepage = () => {
    const blogData = useBlog();

    if (!blogData.data) {
        return <Loader />;
    }

    const data = blogData.data;
    return (
        <>
            <Helmet>
                <title>{data.title}</title>
                <meta name="title" content={data.seo_title} />
                <meta name="description" content={data.seo_description} />
                <meta name="keywords" content={data.seo_keywords} />
                <meta property="og:url" content={window.location.href} />
                <meta property="og:type" content="article" />
                <meta property="og:title" content={data.social_title} />
                <meta property="og:description" content={data.social_description} />
                <meta property="og:image" content={data.social_image.url} />
                <link href={data.social_image.url} rel="image_src" />
            </Helmet>
            <BlogHeader displayDownloadApp={true} />
            <BlogPostLayout {...blogData} />
            <HomeFooter />
        </>
    );
};

export default BlogHomepage;
