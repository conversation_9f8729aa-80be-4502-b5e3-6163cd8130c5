import React from 'react';
import { Helmet } from 'react-helmet';
import { useParams } from 'react-router';
import { BlogHeader } from '../../components/blog/blog-header/blog-header';
import BlogPostHeadline from '../../components/blog/blog-post-headline/blog-post-headline';
import { BlogPostLayout } from '../../components/blog/blog-post-layout';
import { HomeFooter } from '../../components/home-footer';
import { Loader } from '../../components/loader/Loader';
import { useBlogPost } from '../../redux/selectors/use-blog-post';

const BlogPost = () => {
    const params = useParams<{ slug: string }>();
    const blogPost = useBlogPost(params.slug!);
    if (!blogPost) {
        return <Loader />;
    }
    const data = blogPost.data;

    return (
        <>
            <Helmet>
                <meta name="title" content={data.seo_title} />
                <meta property="title" content={data.seo_title} />
                <meta name="description" content={data.seo_description} />
                <meta property="description" content={data.seo_description} />
                <meta name="keywords" content={data.seo_keywords} />
                <meta property="keywords" content={data.seo_keywords} />
                <meta property="og:url" content={window.location.href} />
                <meta property="og:type" content="article" />
                <meta property="og:title" content={data.social_title} />
                <meta property="og:description" content={data.social_description} />
                <meta name="og:description" content={data.social_description} />
                <meta property="og:image" content={data.social_image.url} />
                <link href={data.social_image.url} rel="image_src" />
            </Helmet>
            <BlogHeader displayDownloadApp={true} />
            <BlogPostHeadline
                title={blogPost.data.preview_title}
                category={blogPost.data.main_category.uid}
                publishData={blogPost.first_publication_date}
                blogPostData={blogPost}
            />
            <BlogPostLayout {...blogPost} />
            <HomeFooter />
        </>
    );
};

export default BlogPost;
