import { AxiosRequestConfig } from 'axios';
import { FirebaseService } from '@bookr-technologies/core/services';
import { UserEntity } from '../entity/user.entity';
import { ApiService } from './api.service';

export class UserService {
    private api: ApiService;
    private static self: UserService;

    constructor() {
        this.api = new ApiService();
    }

    static get instance() {
        if (!this.self) {
            this.self = new this();
        }

        return this.self;
    }

    async getUserInfo(): Promise<UserEntity> {
        const uid = FirebaseService.auth().currentUser?.uid;
        const { data } = await this.api.get<{ data: UserEntity }>(`users/${uid}`);
        return data?.data;
    }

    async createUser(options: UserEntity, config: AxiosRequestConfig = {}): Promise<UserEntity> {
        const { data } = await this.api.post<{ data: UserEntity }>('users', options, config);
        return data?.data;
    }
}
