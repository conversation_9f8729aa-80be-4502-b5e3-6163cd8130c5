import Axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { FirebaseService } from '@bookr-technologies/core/services';
import { env } from '@bookr-technologies/env';

export class ApiService {
    private api: AxiosInstance;

    constructor() {
        this.api = Axios.create({
            baseURL: env('apiUrl'),
        });

        this.api.interceptors.request.use(async (config) => {
            config.headers ??= {};
            if (!config.headers.Authorization) {
                const token = await FirebaseService.auth().currentUser?.getIdToken();
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
            }

            return config;
        });
    }

    async request(endpoint: string, config: AxiosRequestConfig = {}) {
        return this.api.request({
            ...config,
            url: endpoint,
        });
    }

    async post<T>(endpoint: string, data: any = {}, config: AxiosRequestConfig = {}) {
        return this.api.post<T>(endpoint, data, config);
    }

    async get<T>(endpoint: string, config: AxiosRequestConfig = {}) {
        return this.api.get<T>(endpoint, config);
    }
}
