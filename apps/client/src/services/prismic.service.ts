import * as Prismic from '@prismicio/client';

export class PrismicService {
    private prismicUrl = 'https://bookr-app-testing.prismic.io/api/v2';
    private prismicConnection;
    private static self: PrismicService;

    constructor() {
        this.prismicConnection = Prismic.createClient(this.prismicUrl);
    }

    static get instance() {
        if (!this.self) {
            this.self = new this();
        }

        return this.self;
    }
    async getDocumentById(id: string) {
        return this.prismicConnection.query(Prismic.predicate.at('document.id', id));
    }
    async getDocumentsById(id: Array<string>) {
        return this.prismicConnection.query(Prismic.predicate.any('document.id', id.filter(Boolean)));
    }
    async getDocumentByUid(type: string, uid: string) {
        return this.prismicConnection.getByUID(type, uid, {});
    }
    async getDocumentsByType(type: string) {
        return this.prismicConnection.query(Prismic.predicate.at('document.type', type));
    }
}
