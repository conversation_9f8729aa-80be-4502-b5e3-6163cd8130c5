import { AxiosRequestConfig } from 'axios';
import { BusinessEntity } from '../entity/business.entity';
import { TimeSlotEntity } from '../entity/time-slot.entity';
import { ApiService } from './api.service';

interface FetchServiceTimeSlotsOptions {
    staffId: string;
    serviceId: number;
    date: string; // yyyy-mm-dd
}

interface BookServiceNowOptions {
    timestamp: number; // in utc
    staffId: string;
    serviceId: number;
    timezoneOffset?: number;
}

export interface BookNowResponse {
    success: boolean;
    error: string;
    appointment: any;
    webRedirectUrl: string;
}

export class BusinessService {
    private api: ApiService;
    private static self: BusinessService;

    constructor() {
        this.api = new ApiService();
    }

    static get instance() {
        if (!this.self) {
            this.self = new this();
        }

        return this.self;
    }

    async fetchBySlug(slug: string): Promise<BusinessEntity> {
        const { data } = await this.api.get<{ data: BusinessEntity }>(`business/${slug}`);
        return data.data;
    }

    async fetchServiceTimeSlots(
        options: FetchServiceTimeSlotsOptions,
        config: AxiosRequestConfig = {},
    ): Promise<TimeSlotEntity[]> {
        const { data } = await this.api.get<{ data: TimeSlotEntity[] }>('appointments/generateTimeslots', {
            ...config,
            params: {
                ...(config?.params || {}),
                ...options,
            },
        });

        return data?.data;
    }

    async bookServiceNow(options: BookServiceNowOptions, config: AxiosRequestConfig = {}): Promise<any> {
        const { data } = await this.api.post<{ data: BookNowResponse }>('appointments', options, config);

        return data.data;
    }
}
