import moment, { Moment } from 'moment';
import { Hour } from '../interfaces/hour.interface';

export const DEFAULT_PROFILE_PICTURE_IMAGE = 'https://d230pchl7el2vc.cloudfront.net/default_image.png';

export enum STORE_URL {
    IOS = 'https://apps.apple.com/us/app/bookr-online-booking-app/id1547131136',
    ANDROID = 'https://play.google.com/store/apps/details?id=com.bookr.app',
}

/**
 * When doing this -> time.add(-TIMEZONE_OFFSET, 'minutes'), you display the time in the users' timezone.
 *
 * When doing this -> time.add(TIMEZONE_OFFSET, 'minutes'), you display the time in UTC.
 */

export const getTimezoneOffsetForDate = (date: Moment) => new Date(date.toDate()).getTimezoneOffset();

export const convertHoursToLocalTimezone = (hours: Hour[]) =>
    hours.map((hour) => {
        const toLocalTimezone = { ...hour };
        const offset = moment(hour.lastUpdatedAt).utcOffset();
        toLocalTimezone.start = moment(hour.start, 'HH:mm').add(offset, 'minutes').format('HH:mm');
        toLocalTimezone.end = moment(hour.end, 'HH:mm').add(offset, 'minutes').format('HH:mm');
        return toLocalTimezone;
    });
