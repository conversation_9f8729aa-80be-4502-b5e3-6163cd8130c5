/* eslint-disable camelcase */
import { BlogImageReferenceEntity } from './blog-image-reference.entity';
import { BlogRichTextArrayEntity } from './blog-rich-text.entity';

export class BlogSliceAlternativeLayoutPrimaryEntity {
    public description!: BlogRichTextArrayEntity;
    public image_side_desktop!: string;
    public image_side_mobile!: string;
    public optional_image!: BlogImageReferenceEntity;
}
