/* eslint-disable camelcase */
import { PrismicCategoryEntity } from './prismic-category.entity';
import { SliceTypeEntity } from './slice-type.entity';
import { BlogImageReferenceEntity } from './slices_subtypes/blog-image-reference.entity';

export class BlogDataEntity {
    public title!: string;
    public body!: Array<SliceTypeEntity>;
    public description!: string;
    public fav_icon!: any;
    public navigation!: Array<PrismicCategoryEntity>;
    public seo_keywords!: any;
    public seo_description!: any;
    public seo_title!: any;
    public social_title!: any;
    public social_description!: any;
    public social_image!: BlogImageReferenceEntity;
}
