/* eslint-disable camelcase */
import { PrismicEntity } from './prismic.entity';
import { SliceTypeEntity } from './slice-type.entity';
import { BlogImageReferenceEntity } from './slices_subtypes/blog-image-reference.entity';

export class BlogPostEntityData {
    public body!: Array<SliceTypeEntity>;
    public description!: string;
    public thumbnail_image!: BlogImageReferenceEntity;
    public 'main_category'!: PrismicEntity;
    public preview_description!: string;
    public preview_title!: string;
    public seo_keywords!: string;
    public seo_description!: string;
    public seo_title!: string;
    public social_image!: any;
    public social_title!: string;
    public social_description!: string;
    public first_publish: any;
}
