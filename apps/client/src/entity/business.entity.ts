import { Hour } from '../interfaces/hour.interface';
import { BusinessStaffEntity } from './business-staff.entity';
import { CategoryEntity } from './category.entity';

export class BusinessEntity {
    public websiteURL!: string;
    public id!: string;
    public description!: string;
    public name!: string;
    public phoneNumber!: string;
    public profilePicture!: string;
    public startingDate!: string;
    public categories!: CategoryEntity[];
    public photos!: string[];
    public createdAt!: string;
    public facebookURL!: string;
    public formattedAddress!: string;
    public hidden!: boolean;
    public workingHours!: Hour[];
    public instagramURL!: string;
    public virtualTourURL!: string;
    latitude!: number;
    latitudeDelta!: number;
    longitude!: number;
    longitudeDelta!: number;

    public staffMembers!: BusinessStaffEntity[];
}
