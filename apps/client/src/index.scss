@import 'pure-react-carousel/dist/react-carousel.es.css';
@import url('https://cdn.jsdelivr.net/npm/@xz/fonts@1/serve/plus-jakarta-display.min.css');

$font: '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;

body {
    font-family: '<PERSON><PERSON>s', 'Roboto', 'Helvetica', 'Arial', sans-serif;
    background-color: #fff !important;
}

.carousel__next-button,
.carousel__prev-button {
    background-color: transparent;
}

.authDialog-paper {
    border-radius: 12px !important;
}

.authDialog-content {
    padding: 0 !important;
    width: 100%;

    .firebaseui-id-password-toggle {
        margin-right: 14px;
        margin-top: -2px;
    }

    .firebaseui-phone-number-error.firebaseui-id-phone-number-error {
        margin-top: 4px;
    }

    .firebaseui-container {
        width: 100%;
        padding: 48px 34px !important;
        min-height: 0;
        max-width: 0;
        min-width: 100%;

        &.firebaseui-id-page-sign-in {
            padding-bottom: 32px !important;
        }

        &.firebaseui-id-page-phone-sign-in-start,
        &.firebaseui-id-page-phone-sign-in-finish {
            padding-bottom: 24px !important;
        }

        &.firebaseui-id-page-password-sign-in {
            padding-bottom: 14px !important;
        }

        &.firebaseui-id-page-sign-in,
        &.firebaseui-id-page-password-sign-in,
        &.firebaseui-id-page-phone-sign-in-start,
        &.firebaseui-id-page-phone-sign-in-finish {
            .firebaseui-title {
                font-family: $font;
                text-align: center;
                font-size: 24px;
                font-weight: 500;
                padding: 0;
                margin-bottom: 32px;
            }
        }
    }

    .mdl-progress {
        top: 0 !important;
    }

    .firebaseui-text-input-error {
        margin: 0 8px 24px;

        &:empty {
            margin-bottom: 0;
        }
    }

    .firebaseui-recaptcha-wrapper {
        margin: 8px auto;
    }

    .firebaseui-card-footer {
        padding: 0;
        margin: 0;

        p {
            margin: 24px 0 0 0;
        }
    }

    .firebaseui-phone-number {
        .firebaseui-country-selector {
            margin-top: 0;
            margin-bottom: 0;
            width: 120px;
            border: 1px solid #ccc;
            background-position: 90%;
        }
    }

    .firebaseui-card-content,
    .firebaseui-card-header,
    .firebaseui-card-actions {
        padding: 0;
    }

    & > form,
    .firebaseui-idp-list {
        margin: 0;
    }

    .firebaseui-idp-list > .firebaseui-list-item,
    .firebaseui-tenant-list > .firebaseui-list-item {
        margin-bottom: 18px;
    }

    .firebaseui-list-item:last-child {
        margin-bottom: 0;
    }

    .mdl-button {
        box-shadow: none;
        border-radius: 12px;
        min-height: 40px;
        letter-spacing: 1px;
        font-size: 14px;
        font-weight: 500;
        color: #222 !important;

        &.mdl-button--raised {
            background-color: #222 !important;
            color: #fff !important;
        }
    }

    .mdl-textfield {
        border: 1px solid #ccc;
        padding: 0;
        position: relative;
        transition: border-color 250ms;
        overflow: hidden;

        &.is-focused {
            border-color: #222;
        }

        &__input {
            height: 56px;
            padding: 24px 24px 8px;
            border: none;
        }

        &,
        &__label {
            border-radius: 12px;
        }

        &__label {
            left: 24px;
            top: 16px;
            height: 24px;
            line-height: 24px;
            bottom: auto;
            width: auto;
            font-size: 14px;
            font-family: $font;
            font-weight: 500;

            &:after {
                display: none;
            }
        }
    }

    .firebaseui-idp-button {
        display: flex;
        align-items: center;
        width: 100%;
        max-width: 100%;
        min-height: 48px;

        &.firebaseui-idp-phone {
            background-color: #777 !important;
        }

        .firebaseui-idp-icon-wrapper {
            position: absolute;
            width: 18px;
            height: 18px;
            top: 15px;
            left: 18px;
        }

        .firebaseui-idp-text {
            font-size: 16px;
            flex-grow: 1;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    }
}

.firebaseui-container {
    box-shadow: none !important;
    min-width: 300px;
}

// HUBSPOT CUSTOMIZATION
.leadinModal-content {
    border-radius: 12px !important;
    background: #fff !important;
}

.leadin-preview-wrapper {
    padding: 32px !important;

    & > h4 {
        font-family: 'Plus Jakarta Display', Poppins, serif !important;
        font-size: 24px !important;
        font-weight: 700 !important;
    }
}

#leadinModal-content-wrapper-17280089 .leadinModal-description-body.leadinModal-description-body > p {
    font-family: Poppins, serif !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    color: #757575 !important;
}

#leadin-content-form-wrapper > div.leadin-message-wrapper > span > p {
    font-family: Poppins, serif !important;
    font-size: 18px !important;
    font-weight: 500 !important;
    color: #757575 !important;
}

.leadin-message-wrapper {
    & > h4 {
        font-family: 'Plus Jakarta Display', Poppins, serif !important;
        font-size: 24px !important;
        font-weight: 700 !important;
        color: #111111 !important;
    }
}

.leadin-button-wrapper {
    text-align: center !important;
    justify-content: center !important;
}

.leadin-button.leadin-advance-button.leadin-button-secondary,
.leadin-button.leadin-button-primary.leadin-primary.next-button {
    font-family: Poppins, serif !important;
    font-style: normal !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    line-height: 24px !important;
    letter-spacing: 0.0125em !important;
    padding: 16px !important;
    border-radius: 12px !important;
    background: #111 !important;
    color: #fff !important;

    &:hover {
        background: #222 !important;
    }
}

.leadin-button.leadin-button-secondary.leadin-secondary.back-button {
    padding: 16px !important;
}

.leadin-button.leadin-button-primary.leadin-primary.leadin-submit.button-with-gdpr {
    background: #111 !important;
    color: #fff !important;
    border-radius: 12px !important;
    font-family: Poppins, serif !important;
    font-style: normal !important;
    font-weight: 500 !important;
    font-size: 16px !important;
    line-height: 24px !important;
    letter-spacing: 0.0125em !important;

    &:hover {
        background: #222 !important;
    }
}

.leadinModal .leadin-input-wrapper label {
    font-family: Poppins, serif !important;
    font-style: normal !important ;
    font-weight: 500 !important;
    font-size: 14px !important;
    line-height: 24px !important;
    letter-spacing: 0.005em !important;
    color: #111111 !important;
}

.leadinModal .leadin-input-wrapper input {
    border-radius: 12px !important;
    padding: 24px !important;
    background: #f6f6f6;

    &:focus {
        background: #ffffff;
        outline-color: #111111;
    }
}
