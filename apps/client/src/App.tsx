import CssBaseline from '@mui/material/CssBaseline';
import { setAnalyticsCollectionEnabled, setUserId } from 'firebase/analytics';
import { SnackbarProvider } from 'notistack';
import React, { Suspense } from 'react';
import ReactPixel from 'react-facebook-pixel';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { BreakpointProvider } from 'react-socks';
import { initializeApplication } from '@bookr-technologies/core/application';
import { FirebaseService } from '@bookr-technologies/core/services';
import { Root } from './Root';
import { AuthDialog } from './components/auth/auth-dialog';
import { Loader } from './components/loader/Loader';
import { ProfileSetupDialog } from './components/profile-setup/profile-setup-dialog';
import { ThemeProvider } from './components/theme-provider/theme-provider';
import { facebookPixelId } from './constants/firebase';
import { firebaseAuthentication, openProfileSetupDialog } from './redux/slices/auth.slice';
import { rootStore } from './redux/store';
import { UserService } from './services/user.service';

ReactPixel.init(facebookPixelId);
ReactPixel.grantConsent();

initializeApplication();
setAnalyticsCollectionEnabled(FirebaseService.analytics(), true);

FirebaseService.auth().onAuthStateChanged(async (user) => {
    if (!user) {
        // logged out
        return rootStore.dispatch(firebaseAuthentication(null));
    }
    try {
        const userInfo = await UserService.instance.getUserInfo();
        if (userInfo) {
            // if the user already exists in db, we just save it in the store
            setUserId(FirebaseService.analytics(), userInfo.uid);
            return rootStore.dispatch(firebaseAuthentication(userInfo));
        }
    } catch (e: any) {
        if (e.message.includes('404')) {
            return rootStore.dispatch(openProfileSetupDialog());
        }
    }

    return null;
});

function App() {
    return (
        <Provider store={rootStore}>
            <ThemeProvider>
                <BreakpointProvider>
                    <SnackbarProvider maxSnack={3} anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}>
                        <CssBaseline />
                        <AuthDialog />
                        <ProfileSetupDialog />
                        <Suspense fallback={<Loader />}>
                            <BrowserRouter>
                                <Root />
                            </BrowserRouter>
                        </Suspense>
                    </SnackbarProvider>
                </BreakpointProvider>
            </ThemeProvider>
        </Provider>
    );
}

export default App;
