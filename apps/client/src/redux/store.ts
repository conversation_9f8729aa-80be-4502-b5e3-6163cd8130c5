import { configureStore, Slice } from '@reduxjs/toolkit';
import { useDispatch, useSelector } from 'react-redux';
import * as slices from './slices';

type SlicesType = typeof slices;
type StateType = {
    [K in keyof SlicesType]: SlicesType[K] extends Slice<infer S> ? S : unknown;
};
type ReducersType = {
    [K in keyof SlicesType]: SlicesType[K]['reducer'];
};

function initStore(preloadedState?: Partial<StateType>) {
    return configureStore({
        preloadedState,
        devTools: true,
        reducer: Object.entries(slices).reduce(
            (prev, [key, slice]) => ({
                ...prev,
                [key as any]: slice.reducer,
            }),
            {} as ReducersType,
        ),
    });
}

export const rootStore = initStore();

export type RootState = ReturnType<typeof rootStore.getState>;
export type AppDispatch = typeof rootStore.dispatch;
export const useAppDispatch = () => useDispatch<AppDispatch>();

export function useAppSelector<S = RootState, T = unknown>(
    selector: (state: S) => T,
    equalityFn?: (left: T, right: T) => boolean,
): T {
    return useSelector<S, T>(selector, equalityFn);
}
