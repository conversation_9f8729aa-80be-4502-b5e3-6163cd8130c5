import { useEffect } from 'react';
import { fetchAllBlogPosts, fetchBlogData, fetchBlogPostById } from '../slices/blog.slice';
import { useAppDispatch, useAppSelector } from '../store';

export function useBlogPost(id: string) {
    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(fetchBlogPostById(id));
    }, [id]);

    return useAppSelector((state) => state.blog.blogPosts[id]);
}

export function useAllBlogPosts() {
    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(fetchAllBlogPosts());
    }, []);

    return useAppSelector((state) => state.blog.blogPosts);
}

export function useBlog() {
    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(fetchBlogData());
    }, []);

    return useAppSelector((state) => state.blog.blogData);
}
