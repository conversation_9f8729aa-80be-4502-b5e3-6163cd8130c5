import { useEffect } from 'react';
import { fetchBusinessById } from '../slices/business.slice';
import { useAppDispatch, useAppSelector } from '../store';

export function useBusiness(slug: string) {
    const dispatch = useAppDispatch();

    useEffect(() => {
        dispatch(fetchBusinessById(slug));
    }, [slug, dispatch]);

    return useAppSelector((state) => state.business.entities[slug]);
}
