import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { BlogPostEntity } from '../../entity/blog-post.entity';
import { BlogEntity } from '../../entity/blog.entity';
import { PrismicService } from '../../services/prismic.service';

export const fetchBlogData = createAsyncThunk('blog/fetchBlogData', async () => {
    const data = await PrismicService.instance.getDocumentsByType('blog');
    return {
        data: data.results[0],
    };
});

export const fetchBlogPostById = createAsyncThunk('blog/fetchBlogPostById', async (uid: string) => {
    const data = await PrismicService.instance.getDocumentByUid('blog_post', uid);
    return {
        data,
    };
});

export const fetchAllBlogPosts = createAsyncThunk('blog/fetchAllBlogPosts', async () => {
    const data = await PrismicService.instance.getDocumentsByType('blog_post');
    return {
        data: data.results,
    };
});

export const blogSlice = createSlice({
    name: 'blog',
    initialState: {
        blogData: {} as BlogEntity,
        blogPosts: {} as Record<string, BlogPostEntity>,
        blogPostsIds: [] as string[],
    },
    reducers: {},
    extraReducers: {
        [fetchBlogPostById.fulfilled as any]: (state, { payload }: PayloadAction<{ data: BlogPostEntity }>) => {
            state.blogPosts[payload.data.uid] = payload.data;
            state.blogPostsIds.push(payload.data.uid);
        },
        [fetchBlogData.fulfilled as any]: (state, { payload }: PayloadAction<{ data: any }>) => {
            state.blogData = payload.data;
        },
        [fetchAllBlogPosts.fulfilled as any]: (state, { payload }: PayloadAction<{ data: Array<BlogPostEntity> }>) => {
            state.blogPosts = payload.data.reduce((agg, element) => {
                agg[element.uid] = element;
                return agg;
            }, {} as Record<string, BlogPostEntity>);
        },
    },
});

export default blogSlice;
