import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { logEvent } from 'firebase/analytics';
import { FirebaseService } from '@bookr-technologies/core/services';
import { BusinessEntity } from '../../entity/business.entity';
import { BusinessService } from '../../services/business.service';

export const fetchBusinessById = createAsyncThunk('business/fetchBusinessBySlug', async (slug: string) => {
    const data = await BusinessService.instance.fetchBySlug(slug);
    if (data.id) {
        logEvent(FirebaseService.analytics(), 'business_view', { businessId: data.id });
    }

    return {
        data,
        slug,
    };
});

export const businessSlice = createSlice({
    name: 'business',
    initialState: {
        ids: [] as string[],
        entities: {} as Record<string, BusinessEntity>,
    },
    reducers: {},
    extraReducers: {
        [fetchBusinessById.fulfilled as any]: (
            state,
            { payload }: PayloadAction<{ data: BusinessEntity; slug: string }>,
        ) => {
            state.entities[payload.data.id] = payload.data;
            state.entities[payload.slug] = payload.data;
            state.ids.push(payload.data.id);
        },
    },
});

export default businessSlice;
