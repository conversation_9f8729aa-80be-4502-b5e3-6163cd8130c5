import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserEntity } from '../../entity/user.entity';

interface StateType {
    authDialog: boolean;
    authenticated: boolean;
    profileSetupDialog: boolean;
    user: UserEntity | null;
}

export const authSlice = createSlice({
    name: 'auth',
    initialState: {
        authDialog: false,
        authenticated: false,
        profileSetupDialog: false,
        user: null,
    } as StateType,
    reducers: {
        firebaseAuthentication: (state, { payload }: PayloadAction<null | UserEntity>) => {
            state.authenticated = !!payload;
            state.user = payload;
            state.authDialog = false;
            state.profileSetupDialog = false;
        },
        openAuthDialog: (state) => {
            state.authDialog = true;
        },
        closeAuthDialog: (state) => {
            state.authDialog = false;
        },
        openProfileSetupDialog: (state) => {
            state.authDialog = false;
            state.profileSetupDialog = true;
        },
        closeProfileSetupDialog: (state) => {
            state.profileSetupDialog = false;
        },
    },
});

export const {
    firebaseAuthentication,
    closeAuthDialog,
    openAuthDialog,
    openProfileSetupDialog,
    closeProfileSetupDialog,
} = authSlice.actions;
