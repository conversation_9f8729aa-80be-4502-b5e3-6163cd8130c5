{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/dashboard/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"outputPath": "dist/apps/dashboard", "index": "apps/dashboard/src/index.html", "baseHref": "/", "main": "apps/dashboard/src/main.tsx", "polyfills": "apps/dashboard/src/polyfills.ts", "tsConfig": "apps/dashboard/tsconfig.app.json", "assets": ["apps/dashboard/src/assets"], "styles": [], "scripts": [], "webpackConfig": "@nrwl/react/plugins/webpack"}, "configurations": {"production": {"fileReplacements": [{"replace": "apps/dashboard/src/environments/environment.ts", "with": "apps/dashboard/src/environments/environment.production.ts"}, {"replace": "libs/env/src/lib/config.ts", "with": "libs/env/src/lib/config.production.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}, "staging": {"fileReplacements": [{"replace": "apps/dashboard/src/environments/environment.ts", "with": "apps/dashboard/src/environments/environment.staging.ts"}, {"replace": "libs/env/src/lib/config.ts", "with": "libs/env/src/lib/config.staging.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false}, "development": {}}}, "serve": {"executor": "@nrwl/webpack:dev-server", "options": {"buildTarget": "dashboard:build:development", "hmr": true, "allowedHosts": "bookr.test"}, "configurations": {"production": {"buildTarget": "dashboard:build:production", "hmr": false}, "development": {"buildTarget": "dashboard:build:development"}}, "defaultConfiguration": "development"}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/dashboard/**/*.{ts,tsx,js,jsx}"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/dashboard"], "options": {"jestConfig": "apps/dashboard/jest.config.ts", "passWithNoTests": true}}}, "tags": []}