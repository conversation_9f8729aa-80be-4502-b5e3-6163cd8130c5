import './setup';

import CssBaseline from '@mui/material/CssBaseline';
import { SnackbarProvider } from 'notistack';
import { StrictMode } from 'react';
import 'react-dates/lib/css/_datepicker.css';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { render } from 'react-dom';
import { QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { BrowserRouter } from 'react-router-dom';
import { queryClient } from '@bookr-technologies/store/queryClient';
import { AuthResolver } from '@bookr-technologies/ui/AuthResolver';
import { GlobalConfirmationDialog } from '@bookr-technologies/ui/ConfirmationDialog';
import { ThemeProvider } from '@bookr-technologies/ui/ThemeProvider';
import { App } from './app/App';
import { detectDashboardBaseName } from './app/utils/detectBaseName';
import './app/utils/reactDates';
import './style.css';

render(
    <StrictMode>
        <AuthResolver />
        <ThemeProvider>
            <QueryClientProvider client={queryClient}>
                <ReactQueryDevtools initialIsOpen={false} />
                <BrowserRouter basename={detectDashboardBaseName()}>
                    <CssBaseline />
                    <SnackbarProvider maxSnack={3} anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}>
                        <DndProvider backend={HTML5Backend}>
                            <GlobalConfirmationDialog />
                            <App />
                        </DndProvider>
                    </SnackbarProvider>
                </BrowserRouter>
            </QueryClientProvider>
        </ThemeProvider>
    </StrictMode>,
    document.getElementById('root'),
);
