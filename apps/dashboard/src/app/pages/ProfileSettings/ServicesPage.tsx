/* eslint-disable @typescript-eslint/no-explicit-any */
import AddIcon from '@mui/icons-material/AddCircle';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { Form, Formik, FormikProps } from 'formik';
import { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AvailableCurrencies } from '@bookr-technologies/api/constants/AvailableCurrencies';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { useAuthStore } from '@bookr-technologies/store';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import { createRandomColor } from '@bookr-technologies/ui/styles';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { ServiceDialog } from '../../components/ServiceDialog';
import { ServicesManager } from '../../components/ServicesManager';
import { useProfileSettings } from '../../hooks/useProfileSettings';

const StyledButton = styled(Button)({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 12,
    fontWeight: 500,
    textTransform: 'capitalize',
    position: 'absolute',
    top: -40,
    right: 0,
    minWidth: 120,
    '.icon': {
        marginBottom: 4,
    },
});

// TODO: Do some cleanup logic for services add/edit actions
function ServicesPage() {
    const { t } = useTranslation('profileSettingsPage');
    const { t: common } = useTranslation('common');
    const [activeService, setActiveService] = useState<ServiceModel | null>(null);
    const { initialValues, handleSubmitServices } = useProfileSettings();
    const formikRef = useRef<FormikProps<any> | null>(null);
    const handleClose = useCallback(() => setActiveService(null), []);
    const resolveUser = useAuthStore((state) => state.resolveUser);

    const handleNewService = useCallback(() => {
        setActiveService({
            id: 0,
            numberOfSessions: 0,
            daysInAdvanceSessionsCanBeBooked: 0,
            serviceRank: initialValues.services.length + 1,
            breakBetweenServices: 5,
            duration: 30,
            price: 0,
            inactive: false,
            description: '',
            name: '',
            currency: AvailableCurrencies.LEI,
            color: createRandomColor(),
            hiddenFromClients: false,
            onlineEvent: false,
            onlineEventIntegrationType: null,
            acceptsOnlinePayments: false,
        });
    }, [initialValues.services.length]);

    const handleSubmitService = useCallback(
        async (data: ServiceModel): Promise<void> => {
            const services = formikRef.current?.getFieldMeta<ServiceModel[]>('services')?.value ?? [];
            services.push(data);
            formikRef.current?.setFieldValue('services', [...services]);
            await resolveUser();
        },
        [resolveUser],
    );

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <ServiceDialog
                open={!!activeService}
                service={activeService}
                onSubmit={handleSubmitService}
                onClose={handleClose}
            />
            <SettingsLayoutSection title={t('yourServices')}>
                <Formik initialValues={initialValues} onSubmit={handleSubmitServices} innerRef={formikRef}>
                    {({ values }) => (
                        <Grid component={Form} container>
                            <Grid
                                container
                                direction={'row'}
                                justifyContent={'space-between'}
                                mt={1}
                                position={'relative'}
                            >
                                <Typography variant={'body2'} color={'textSecondary'} fontWeight={500}>
                                    {t('yourServicesDescription')}
                                </Typography>
                                <StyledButton color={'accent'} onClick={handleNewService}>
                                    <AddIcon className={'icon'} />
                                    {t('newService')}
                                </StyledButton>
                            </Grid>
                            <Grid container mt={2}>
                                <ServicesManager name={'services'} onEdit={resolveUser} onDelete={resolveUser} />
                            </Grid>
                            {values.services.length > 0 && (
                                <Grid container justifyContent={'flex-end'} mt={3}>
                                    <FormikButton variant={'contained'} disableElevation>
                                        {common('saveChanges')}
                                    </FormikButton>
                                </Grid>
                            )}
                        </Grid>
                    )}
                </Formik>
            </SettingsLayoutSection>
        </SettingsLayout>
    );
}

export default ServicesPage;
