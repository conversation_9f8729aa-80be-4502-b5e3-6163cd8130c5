/* eslint-disable @typescript-eslint/no-explicit-any */
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { useEvent } from '@bookr-technologies/hooks';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';
import { SwitchCard } from '@bookr-technologies/ui/SwitchCard';
import { SettingsLayout } from '../../components/Layout';
import { AccountPaymentConfigurationStatus } from './components/AccountPaymentConfigurationStatus';

const StyledAccordion = styled(Accordion)(() => ({
    backgroundColor: '#fff',
    marginBottom: 16,
    borderRadius: 10,
    '&:before': {
        display: 'none',
    },
    '.MuiAccordionSummary-root': {
        padding: 0,
        lineHeight: 1,
        minHeight: 0,
        alignItems: 'flex-start',
        '.MuiAccordionSummary-content': {
            margin: 0,
        },
    },
    '.MuiAccordionDetails-root': {
        padding: '12px 0 0 0',
        lineHeight: 1.2,
    },
}));

function PaymentsPage() {
    const { t, i18n } = useTranslation('profileSettingsPage');
    const { t: common } = useTranslation('common');
    const user = useCurrentUser();
    const business = useBusiness(user?.business?.id);
    const notify = useSnackbar();
    const businessMultiAccount = !!business.data?.multipleStripeConnectedAccountsEnabled;
    const isOwner = user?.accountType === AccountType.BusinessOwner;
    const questions: any[] = i18n.getResource(i18n.language, 'profileSettingsPage', 'frequentQuestions.questions');
    const [active, setActive] = useState(() => !businessMultiAccount);

    const handleMultipleSelectPayment = useEvent(async (_, state) => {
        const businessId = business.data?.id;
        if (businessId) {
            await businessEndpoint.update(businessId, { multipleStripeConnectedAccountsEnabled: state }).catch(() => {
                notify.enqueueSnackbar(common('somethingWentWrong'), { variant: 'error' });
            });
            await business.refetch();
        }
    });

    useEffect(() => setActive(businessMultiAccount), [businessMultiAccount]);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <Typography variant={'subtitle1'} fontWeight={500} lineHeight={1}>
                {t('manageYourPayments')}
            </Typography>
            <Typography variant={'caption'} color={'textSecondary'} fontWeight={500}>
                {t('manageYourPaymentsDescription')}
            </Typography>

            <Grid mt={3} container flexWrap={'nowrap'}>
                <Grid item container xs={7} mr={3} flexDirection={'column'} alignItems={'flex-start'}>
                    {isOwner ? (
                        <SwitchCard
                            icon={<AccountBalanceWalletIcon />}
                            title={t('multipleSelectPayment')}
                            subtitle={t('multipleSelectPaymentDescription')}
                            onChange={handleMultipleSelectPayment}
                            checked={active}
                        />
                    ) : null}
                    <AccountPaymentConfigurationStatus user={user} t={t} />
                </Grid>

                <Divider orientation={'vertical'} flexItem />

                <Grid item xs={5} ml={3}>
                    <Typography variant={'h5'} fontWeight={700} mb={2}>
                        {t('onlinePayments')}
                    </Typography>
                    <Paper variant={'outlined'} sx={{ bgcolor: '#fff', lineHeight: 1 }}>
                        <Typography variant={'body1'} fontWeight={500} lineHeight={1.4} mb={0.5}>
                            {t('onlinePaymentsDescription')}
                        </Typography>
                        <Typography variant={'caption'} color={'textSecondary'} fontWeight={500} lineHeight={'12px'}>
                            {t('onlinePaymentsCaption')}
                        </Typography>
                    </Paper>

                    <Typography variant={'h5'} fontWeight={700} mt={3} mb={2}>
                        {t('frequentQuestions.title')}
                    </Typography>

                    {questions.map(({ title, description }, index) => (
                        <StyledAccordion key={index} variant={'outlined'} disableGutters>
                            <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls={`faq-${index}-content`}
                                id={`faq-${index}-header`}
                            >
                                <Typography variant={'body1'} fontWeight={500}>
                                    {title}
                                </Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                                <Typography variant={'caption'} color={'textSecondary'} fontWeight={500} lineHeight={1}>
                                    {description}
                                </Typography>
                            </AccordionDetails>
                        </StyledAccordion>
                    ))}
                </Grid>
            </Grid>
        </SettingsLayout>
    );
}

export default PaymentsPage;
