import ReportIcon from '@mui/icons-material/Report';
import NewReleasesIcon from '@mui/icons-material/Verified';
import LoadingButton from '@mui/lab/LoadingButton';
import Alert from '@mui/material/Alert';
import Grid from '@mui/material/Grid';
import { styled, useTheme } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import { useState } from 'react';
import { useMutation } from 'react-query';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useEvent } from '@bookr-technologies/hooks';
import { VatInformationDialog } from '../../../components/VatInformationDialog/VatInformationDialog';

interface Props {
    user: UserModel | null;
    t: (key: string) => string;
}

const StyledAlert = styled(Alert)(() => ({
    width: '100%',
    justifyContent: 'center',
    padding: 16,
    boxShadow: 'none',
    '.MuiAlert-message': {
        fontWeight: 600,
        padding: 0,
        lineHeight: '24px',
    },
    '.MuiAlert-icon': {
        padding: 0,
    },
}));

export function AccountPaymentConfigurationStatus({ user, t }: Props) {
    const requireConfiguration = !user?.stripeConnectedAccountId;
    const { enqueueSnackbar } = useSnackbar();
    const theme = useTheme();
    const createConnectedAccount = useMutation(
        'createConnectedAccount',
        async () => {
            if (!user?.stripeConnectedAccountId) {
                const connection = await paymentEndpoint.createConnectAccount({ returnUrl: window.location.href });
                return connection.url;
            }

            const path = (user?.stripeConnectedAccountId || '') + '?uid=' + user?.uid;
            const reauthorizedConnection = await paymentEndpoint.reauthorizeConnectAccount(path);

            return reauthorizedConnection.url;
        },
        {
            onSuccess(url) {
                const res = window.open(url, '_blank');
                if (res === null) {
                    // if the window/popup is blocked, then redirect to the url
                    window.location.href = url;
                }
            },
            onError() {
                enqueueSnackbar(t('somethingWentWrong'), { variant: 'error' });
            },
        },
    );
    const [vatInformationDialogVisible, setVatInformationDialogVisible] = useState(false);

    const handleCreateConnectedAccount = useEvent(() => createConnectedAccount.mutateAsync());

    if (requireConfiguration) {
        return (
            <>
                <StyledAlert
                    icon={<ReportIcon color={'inherit'} />}
                    severity="error"
                    sx={{ backgroundColor: '#FEEEEE', '&, & .MuiAlert-icon': { color: theme.palette.error.main } }}
                >
                    {t('yourAccountStatus.notConfigured')}
                </StyledAlert>
                <Grid mt={1.25} container justifyContent={'flex-end'}>
                    <LoadingButton
                        variant={'contained'}
                        size={'small'}
                        onClick={() => setVatInformationDialogVisible(true)}
                        loading={createConnectedAccount.isLoading}
                    >
                        {t('onlinePaymentsSetup')}
                    </LoadingButton>
                </Grid>
            </>
        );
    }

    return (
        <>
            <StyledAlert
                icon={<NewReleasesIcon color={'inherit'} />}
                severity="info"
                sx={{ backgroundColor: '#EAF2FF', '&, & .MuiAlert-icon': { color: theme.palette.accent.main } }}
            >
                {t('yourAccountStatus.configured')}
            </StyledAlert>
            <Grid mt={1.25} container justifyContent={'flex-end'}>
                <LoadingButton
                    variant={'contained'}
                    size={'small'}
                    onClick={() => setVatInformationDialogVisible(true)}
                    loading={createConnectedAccount.isLoading}
                >
                    {t('onlinePaymentsSetup')}
                </LoadingButton>
            </Grid>
            <VatInformationDialog
                user={user}
                open={vatInformationDialogVisible}
                onClose={() => setVatInformationDialogVisible(false)}
                onSave={handleCreateConnectedAccount}
            />
        </>
    );
}
