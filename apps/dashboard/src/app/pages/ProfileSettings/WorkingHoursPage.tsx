import Grid from '@mui/material/Grid';
import { Form, Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import { WorkingProgram } from '@bookr-technologies/ui/WorkingProgram';
import { workingProgramValidation } from '@bookr-technologies/ui/WorkingProgram/workingProgramValidation';
import { SettingsLayout } from '../../components/Layout';
import { useProfileSettings } from '../../hooks/useProfileSettings';

function WorkingHoursPage() {
    const { t } = useTranslation('profileSettingsPage');
    const { t: common } = useTranslation('common');
    const { initialValues, handleSubmitWorkingHours } = useProfileSettings();
    const validationSchema = useMemo(() => workingProgramValidation('workingHours'), []);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <Formik
                initialValues={initialValues}
                onSubmit={handleSubmitWorkingHours}
                validationSchema={validationSchema}
                validateOnMount
                validateOnChange
            >
                <Form>
                    <WorkingProgram name={'workingHours'} />
                    <Grid container justifyContent={'flex-end'} mt={3}>
                        <FormikButton variant={'contained'} disableElevation>
                            {common('saveChanges')}
                        </FormikButton>
                    </Grid>
                </Form>
            </Formik>
        </SettingsLayout>
    );
}

export default WorkingHoursPage;
