import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import FormLabel from '@mui/material/FormLabel';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { Form, Formik } from 'formik';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { ChangePasswordDialog } from '../../components/ChangePasswordDialog';
import { ImagePicker } from '../../components/ImagePicker';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { useProfileSettings } from '../../hooks/useProfileSettings';

function GeneralPage() {
    const { t } = useTranslation('profileSettingsPage');
    const { t: common } = useTranslation('common');
    const { userId, initialValues, handleSubmitGeneral } = useProfileSettings();
    const [changePasswordDialog, setChangePasswordDialog] = useState(false);
    const handleDeleteImage = useCallback(async () => {
        if (userId) {
            await usersEndpoint.update(userId, {
                photoURL: '',
            });
        }
    }, [userId]);

    const handleChangePasswordOpen = useCallback(() => setChangePasswordDialog(true), []);
    const handleChangePasswordClose = useCallback(() => setChangePasswordDialog(false), []);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <Formik initialValues={initialValues} onSubmit={handleSubmitGeneral}>
                {({ values }) => (
                    <Grid component={Form} container>
                        <Grid item xs py={2} pl={1} pr={5}>
                            <SettingsLayoutSection title={t('yourInformation')}>
                                <Grid container mt={3}>
                                    <ImagePicker name={'profilePicture'} onDelete={handleDeleteImage} />
                                </Grid>
                                <Grid container spacing={3} mt={3}>
                                    <Grid item xs={6}>
                                        <FormLabel>{t('displayName')}</FormLabel>
                                        <FormikTextField name={'displayName'} size={'small'} fullWidth />
                                    </Grid>
                                    <Grid item xs={6}>
                                        <FormLabel>{t('phoneNumber')}</FormLabel>
                                        <FormikTextField name={'phoneNumber'} size={'small'} fullWidth />
                                    </Grid>
                                    <Grid item xs={12}>
                                        <FormLabel>{t('emailAddress')}</FormLabel>
                                        <FormikTextField name={'email'} size={'small'} fullWidth />
                                    </Grid>
                                </Grid>
                            </SettingsLayoutSection>
                        </Grid>
                        <Grid item xs py={2} pl={5} pr={1}>
                            <SettingsLayoutSection title={t('yourAvailability')} maxWidth={'400px !important'}>
                                <Grid container mt={3} flexDirection={'column'}>
                                    <Box maxWidth={'214px !important'}>
                                        <FormikTextField
                                            type={'number'}
                                            name={'maxFutureDaysAppointment'}
                                            label={t('numberOfDays')}
                                            size={'small'}
                                            inputProps={{ min: 0 }}
                                        />
                                    </Box>
                                    <Typography variant={'caption'} color={'textSecondary'} mt={1} fontWeight={500}>
                                        {t('yourAvailabilityDescription', {
                                            maxFutureDaysAppointment: values.maxFutureDaysAppointment || 14,
                                        })}
                                    </Typography>
                                </Grid>
                            </SettingsLayoutSection>
                            <SettingsLayoutSection title={t('security')} mt={5}>
                                <Grid container mt={3} alignItems={'center'}>
                                    <Grid item xs>
                                        <Typography variant={'body1'} fontWeight={600} mb={0.5}>
                                            {t('changePassword')}
                                        </Typography>
                                        <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                            {t('changePasswordDescription')}
                                        </Typography>
                                    </Grid>
                                    <Grid item xs container justifyContent={'flex-end'} maxWidth={'180px !important'}>
                                        <Button
                                            variant={'contained'}
                                            color={'inherit'}
                                            size={'small'}
                                            onClick={handleChangePasswordOpen}
                                            disableElevation
                                        >
                                            {common('change')}
                                        </Button>
                                        <ChangePasswordDialog
                                            open={changePasswordDialog}
                                            onClose={handleChangePasswordClose}
                                        />
                                    </Grid>
                                </Grid>
                            </SettingsLayoutSection>
                        </Grid>

                        <Grid container item xs={12} justifyContent={'flex-end'} mt={3}>
                            <FormikButton variant={'contained'} disableElevation>
                                {common('saveChanges')}
                            </FormikButton>
                        </Grid>
                    </Grid>
                )}
            </Formik>
        </SettingsLayout>
    );
}

export default GeneralPage;
