import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { sendPasswordResetEmail, signInWithEmailAndPassword } from 'firebase/auth';
import { Form, Formik } from 'formik';
import { useSnackbar } from 'notistack';
import { MouseEvent, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate, useNavigate } from 'react-router-dom';
import * as Yup from 'yup';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { FirebaseService } from '@bookr-technologies/core/services';
import { useAuthStore } from '@bookr-technologies/store';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { useLocationState } from '@bookr-technologies/ui/hooks/useLocationState';
import authSideImage from '../../assets/authSideImage.png';
import { AuthLayout } from '../components/Layout/AuthLayout';

const StyledForm = styled(Form)({
    display: 'flex',
    flexDirection: 'column',
    flex: '1 1 auto',
});

function SignInPage() {
    const navigate = useNavigate();
    const locationState = useLocationState();
    const { enqueueSnackbar } = useSnackbar();
    const authenticated = useAuthStore((state) => state.authenticated);

    const { t } = useTranslation('signInPage');
    const { t: common } = useTranslation('common');
    const initialValues = useMemo(
        () => ({
            email: locationState.email ?? '',
            password: '',
        }),
        [locationState.email],
    );

    const validationSchema = useMemo(
        () =>
            Yup.object().shape({
                email: Yup.string().required(t('emailRequired')),
            }),
        [t],
    );

    const handleSubmit = useCallback(
        async (values: any) => {
            const metadata = await usersEndpoint.getMetadata(values.email);

            if (values.password && metadata.uid) {
                try {
                    await signInWithEmailAndPassword(FirebaseService.auth(), values.email, values.password);
                    if (!metadata.verified) {
                        navigate('/auth/sign-up/verification', {
                            state: {
                                email: values.email,
                            },
                        });
                    } else {
                        navigate(locationState?.postSignInUrl ?? '/');
                    }
                } catch (e) {
                    const message = getErrorMessage(e, 'errorSignIn');
                    enqueueSnackbar(t(message), { variant: 'error' });
                }

                return;
            }

            if (!metadata.uid) {
                navigate('/auth/sign-up', {
                    state: {
                        postSignInUrl: locationState?.postSignInUrl,
                        email: values.email,
                    },
                });
            } else {
                navigate('/auth/sign-in', {
                    state: {
                        email: values.email,
                        withPassword: true,
                    },
                });
            }
        },
        [enqueueSnackbar, locationState?.postSignInUrl, navigate, t],
    );

    const handleForgotPassword = useCallback(
        (email: string) => async (e: MouseEvent<HTMLAnchorElement>) => {
            e.preventDefault();
            try {
                // TODO: add in app reset form
                await sendPasswordResetEmail(FirebaseService.auth(), email);
                enqueueSnackbar(t('passwordResetEmailSent'), { variant: 'success' });
            } catch (e) {
                const message = getErrorMessage(e, 'errorResettingPassword');
                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [enqueueSnackbar, t],
    );

    if (authenticated === null) {
        return null;
    }

    if (authenticated) {
        return <Navigate to={locationState?.postSignInUrl ?? '/'} replace />;
    }

    return (
        <AuthLayout
            image={authSideImage}
            title={t(locationState.withPassword ? 'signInTitle' : 'title')}
            description={t(locationState.withPassword ? 'signInSubtitle' : 'subtitle')}
        >
            <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                validationSchema={validationSchema}
                validateOnBlur
            >
                {({ values }) => (
                    <StyledForm>
                        <Stack maxWidth={'380px !important'}>
                            <Grid container>
                                <FormikTextField type={'email'} name={'email'} label={common('emailField')} fullWidth />
                            </Grid>
                            {locationState.withPassword ? (
                                <Grid container direction={'column'} mt={3}>
                                    <FormikTextField
                                        type={'password'}
                                        name={'password'}
                                        label={common('passwordField')}
                                        fullWidth
                                    />
                                </Grid>
                            ) : null}
                            <Link
                                href={'#'}
                                fontWeight={500}
                                color={'accent.main'}
                                mt={1.5}
                                onClick={handleForgotPassword(values.email)}
                            >
                                {t('forgotPassword')}
                            </Link>
                        </Stack>
                        <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                            <FormikButton size={'large'} variant={'contained'} color={'primary'} disableElevation>
                                {common('continueButton')}
                            </FormikButton>
                        </Stack>
                    </StyledForm>
                )}
            </Formik>
        </AuthLayout>
    );
}

export default SignInPage;
