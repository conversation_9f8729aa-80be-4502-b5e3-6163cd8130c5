import CancelIcon from '@mui/icons-material/Cancel';
import Button from '@mui/material/Button';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useAuthStore } from '@bookr-technologies/store';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';
import { SettingsLayout } from '../../components/Layout';

const Item = ({ message }: { message: string }) => {
    return (
        <ListItem sx={{ padding: 0, mb: '19px' }}>
            <ListItemAvatar sx={{ display: 'flex', justifyContent: 'flex-start', alignItems: 'center' }}>
                <CancelIcon color={'error'} />
            </ListItemAvatar>
            <ListItemText primary={message} primaryTypographyProps={{ color: '#111111' }} />
        </ListItem>
    );
};

const StyledButton = styled(Button)`
    box-shadow: none;
    height: 50px;
    margin-top: 12px;
    background-color: #eee;

    &:hover {
        background-color: #cbcbcb;
        box-shadow: none;
    }
`;

function OthersPage() {
    const logout = useAuthStore((state) => state.logout);
    const { t } = useTranslation('applicationSettingsPage');
    const { enqueueSnackbar } = useSnackbar();
    const deleteAccountMutation = useMutation(() => usersEndpoint.deleteAccount());
    const confirm = useConfirmation();

    const handleDeleteAccount = async () => {
        const result = await confirm({
            title: `${t('deleteAccount')}?`,
            message: t('deleteAccountMessage'),
            isDestructiveAction: true,
        });

        if (result) {
            deleteAccountMutation.mutate();
        }
    };

    useEffect(() => {
        if (deleteAccountMutation.isSuccess) {
            logout();
        } else if (deleteAccountMutation.isError) {
            const message = getErrorMessage(deleteAccountMutation.error);
            enqueueSnackbar(message, { variant: 'error' });
        }
    }, [deleteAccountMutation, enqueueSnackbar, logout]);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <Typography variant={'h5'} mb={'10px'} fontWeight={'bold'}>
                {t('deleteAccountHeader')}
            </Typography>
            <Typography variant={'body1'} mb={'70px'} fontWeight={500} fontSize={14}>
                {t('deleteAccountMessage')}
            </Typography>
            <Typography fontWeight={600} color={'#000'} mb={'23px'}>
                {t('deleteAccountDetails')}:
            </Typography>

            <List>
                <Item message={t('deleteAccountAction1')} />
                <Item message={t('deleteAccountAction2')} />
                <Item message={t('deleteAccountAction3')} />
                <Item message={t('deleteAccountAction4')} />
            </List>

            <StyledButton variant={'contained'} onClick={handleDeleteAccount}>
                {t('deleteAccount')}
            </StyledButton>
        </SettingsLayout>
    );
}

export default OthersPage;
