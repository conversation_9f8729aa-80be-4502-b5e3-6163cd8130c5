import { t } from 'i18next';
import * as Yup from 'yup';

export const billingInfoValidationSchema = Yup.object().shape({
    name: Yup.string()
        .nullable()
        .required(t('errors.requiredField', { ns: 'common' })),
    language: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    taxId: Yup.string().nullable().default(''),

    shipping: Yup.object()
        .shape({
            name: Yup.string().nullable().default(''),
            phone: Yup.string().nullable().default(''),
            address: Yup.object().shape({
                city: Yup.string().nullable().default(''),
                country: Yup.string().nullable().default(''),
                state: Yup.string().nullable().default(''),
                line1: Yup.string().nullable().default(''),
                line2: Yup.string().nullable().default(''),
                postalCode: Yup.string().nullable().default(''),
            }),
        })
        .required(t('errors.requiredField', { ns: 'common' })),
});

export const initialValues = {
    name: '',
    taxId: '',
    language: 'ro-RO',
    shipping: {
        name: '',
        phone: '',
        address: {
            city: '',
            country: 'RO',
            state: '',
            line1: '',
            line2: '',
            postalCode: '',
        },
    },
};
