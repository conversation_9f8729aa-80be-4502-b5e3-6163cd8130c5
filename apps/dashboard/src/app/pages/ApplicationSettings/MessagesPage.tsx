import EmailIcon from '@mui/icons-material/MailOutline';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { AxiosError } from 'axios';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from 'react-query';
import { paymentEndpoint } from '@bookr-technologies/api/endpoints/paymentEndpoint';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { InformationDialog } from '@bookr-technologies/ui/InformationDialog';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import { useUrlQueryParams } from '@bookr-technologies/ui/hooks/useUrlQueryParams';
import { SettingsLayout, StyledPaper } from '../../components/Layout';
import RadioButton from '../../components/RadioButton/RadioButton';

const StyledBox = styled(Box)`
    display: flex;
    flex: 1 1 auto;
    width: 100%;
    flex-direction: column;
    height: 100%;

    &.wide {
        margin-right: 60px;
        height: 633px;
    }

    .messages-info {
        background: #f6f6f6;
        border-radius: 8px;
        max-width: 341px;

        padding: 18px 26px 18px 21px;

        &.danger {
            background-color: #feeeee;
        }
    }

    .icon {
        background: rgba(47, 128, 251, 0.1);
        width: 78px;
        height: 78px;
        border-radius: 39px;
        padding: 18px;
        font-size: 60px;
    }
`;

function MessagesPage() {
    const { t } = useTranslation('applicationSettingsPage');
    const { enqueueSnackbar } = useSnackbar();
    const urlQueryParams = useUrlQueryParams();
    const { data: business } = useBusiness();
    const { data: smsOptions, isLoading } = useQuery('smsOptions', () => paymentEndpoint.getSmsOptions());
    const createCheckoutSessionMutation = useMutation((amount: string) => paymentEndpoint.paySMS({ amount }));
    const [isPaymentDialogOpen, setPaymentDialogOpen] = useState(false);
    const [selectedSmsOption, setSelectedSmsOption] = React.useState<string>(smsOptions?.[0].smsAmount || '');

    const handleChange = (value: string) => {
        setSelectedSmsOption(value);
    };

    useEffect(() => {
        if (createCheckoutSessionMutation.isSuccess) {
            const { data } = createCheckoutSessionMutation;
            window.location.href = data.sessionUrl;
        } else if (createCheckoutSessionMutation.isError) {
            enqueueSnackbar((createCheckoutSessionMutation.error as AxiosError<any>).response?.data.message, {
                variant: 'error',
            });
        }
    }, [createCheckoutSessionMutation, enqueueSnackbar]);

    useEffect(() => {
        if (urlQueryParams.get('success')) {
            setPaymentDialogOpen(true);
        }
    }, [t, urlQueryParams]);

    if (isLoading || !smsOptions || !business) {
        return <PageLoader />;
    }

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')} noPaper>
            <Box display={'flex'} flexDirection={'row'} height={'100%'} width={'100%'}>
                <StyledPaper sx={{ maxHeight: '325px', maxWidth: '415px', justifySelf: 'flex-start' }}>
                    <StyledBox>
                        <Typography variant={'h5'} mb={'34px'} fontWeight={'bold'}>
                            {t('smsBalance')}
                        </Typography>
                        <Stack direction={'row'} alignItems={'center'} spacing={3}>
                            <EmailIcon color={'secondary'} className={'icon'} />
                            <Box>
                                <Typography variant={'body1'}>{t('currentBalance')}</Typography>
                                <Typography variant={'h5'} fontWeight={'bold'}>
                                    {business.totalSMS} SMS {t('remaining')}
                                </Typography>
                            </Box>
                        </Stack>
                        <Typography
                            variant={'body2'}
                            pt={'45px'}
                            fontWeight={500}
                            fontSize={14}
                            color={'textSecondary'}
                        >
                            {t('makeSureToTopup')}
                        </Typography>
                    </StyledBox>
                </StyledPaper>
                <StyledPaper sx={{ width: '60%', marginLeft: '30px' }}>
                    <Stack justifyContent={'space-between'}>
                        <StyledBox className={'wide'}>
                            <Typography variant={'h5'} mb={'14px'} fontWeight={'bold'}>
                                {t('topUp')}
                            </Typography>
                            <Typography variant={'body1'} fontSize={14} mb={2} color={'textSecondary'}>
                                {t('smsOptionsSelect')}
                            </Typography>

                            {smsOptions.map((smsOption) => (
                                <RadioButton
                                    checked={selectedSmsOption === smsOption.smsAmount}
                                    onChange={handleChange}
                                    value={smsOption.smsAmount}
                                    label={`${smsOption.smsAmount} Text Messages`}
                                    name="sms-options-radio-buttons"
                                />
                            ))}
                        </StyledBox>
                        <Stack
                            display={'flex'}
                            flexDirection={'row'}
                            justifyContent={'space-between'}
                            alignItems={'center'}
                        >
                            <div>
                                <Typography variant={'body1'} mt={2} sx={{ color: '#000' }}>
                                    Total:
                                </Typography>
                                <Typography variant={'h5'} mb={'34px'} fontWeight={'bold'}>
                                    {smsOptions.find((s) => s.smsAmount === selectedSmsOption)?.smsPrice || '0 ron'}
                                </Typography>
                            </div>
                            <Button
                                variant={'contained'}
                                onClick={() => createCheckoutSessionMutation.mutate(selectedSmsOption)}
                            >
                                {t('buyNow')}
                            </Button>
                        </Stack>
                    </Stack>
                </StyledPaper>
            </Box>
            <InformationDialog
                isOpen={isPaymentDialogOpen}
                title={t('paymentSuccessTitle')}
                message={t('paymentSuccessMessage')}
                onClose={() => setPaymentDialogOpen(false)}
            />
        </SettingsLayout>
    );
}

export default MessagesPage;
