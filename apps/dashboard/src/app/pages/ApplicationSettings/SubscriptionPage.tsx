import Grid from '@mui/material/Grid';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate } from 'react-router-dom';
import { InformationDialog } from '@bookr-technologies/ui/InformationDialog';
import { useUrlQueryParams } from '@bookr-technologies/ui/hooks/useUrlQueryParams';
import { SettingsLayout } from '../../components/Layout';
import { SubscriptionCard } from '../../components/SubscriptionCard';

function SubscriptionPage() {
    const { t } = useTranslation('applicationSettingsPage');
    const action = useUrlQueryParams('action');
    const [success, setSuccess] = useState(() => action === 'success');
    const navigate = useNavigate();
    const location = useLocation();

    const handleCloseSuccess = useCallback(() => {
        navigate({
            pathname: location.pathname,
            search: '',
        });
    }, [location.pathname, navigate]);

    useEffect((): void => {
        setSuccess(action === 'success');
    }, [action]);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')} noPaper>
            <Grid container mt={2}>
                <SubscriptionCard />
            </Grid>

            <InformationDialog
                isOpen={success}
                title={t('paymentSuccessTitle')}
                message={t('paymentSuccessMessage')}
                onClose={handleCloseSuccess}
            />
        </SettingsLayout>
    );
}

export default SubscriptionPage;
