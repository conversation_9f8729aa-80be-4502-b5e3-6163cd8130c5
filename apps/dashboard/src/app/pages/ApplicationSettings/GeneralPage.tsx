import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { AxiosError } from 'axios';
import { Form, Formik } from 'formik';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { BillingInfoModel } from '@bookr-technologies/api/models/BillingInfoModel';
import { UpsertUserDto } from '@bookr-technologies/api/types/UpsertUserDto';
import { useAuthStore } from '@bookr-technologies/store';
import { useUser } from '@bookr-technologies/store/hooks/useUser';
import { CountrySelectVariant, FormikCountrySelect } from '@bookr-technologies/ui/CountrySelect';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import { SettingsLayout } from '../../components/Layout';
import { billingInfoValidationSchema, initialValues } from './billingInfoValidationSchema';

const StyledBox = styled(Box)`
    display: flex;
    flex: 1 1 auto;
    width: 100%;
    height: 100%;
    flex-direction: column;

    h5 {
        padding-bottom: 34px;
    }

    .form {
        max-width: 364px;
    }

    .language {
        max-width: 264px;
        padding-top: 12px;
    }
`;

function GeneralPage() {
    const { t } = useTranslation('applicationSettingsPage');
    const { t: commonT } = useTranslation('common');
    const { enqueueSnackbar } = useSnackbar();
    const user = useUser();
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const { data: billingInfo, isLoading } = useQuery('billingInfo', () => businessEndpoint.getBillingInfo());
    const updateBillingInfoMutation = useMutation((billingInfo: BillingInfoModel) =>
        businessEndpoint.updateBillingInfo(billingInfo),
    );
    const updateUserMutation = useMutation((data: UpsertUserDto) => usersEndpoint.upsert(user.uid, data));

    const handleOnSubmit = useCallback(
        async (values: any) => {
            updateBillingInfoMutation.mutate({ ...values });
            updateUserMutation.mutate({ language: values.language });
        },
        [updateBillingInfoMutation, updateUserMutation],
    );

    const handleLanguageChange = useCallback(
        async (event: any) => {
            await usersEndpoint.update(user.uid, { language: event.target.value });
            await resolveUser();
        },
        [resolveUser, user.uid],
    );

    useEffect(() => {
        if (updateBillingInfoMutation.isSuccess && updateBillingInfoMutation.data && updateUserMutation.isSuccess) {
            enqueueSnackbar(t('settingsUpdated'), { variant: 'success' });
        } else if (updateBillingInfoMutation.isError) {
            enqueueSnackbar((updateBillingInfoMutation.error as AxiosError<any>).response?.data.message, {
                variant: 'error',
            });
        } else if (updateUserMutation.isError) {
            enqueueSnackbar((updateUserMutation.error as AxiosError<any>).response?.data.message, { variant: 'error' });
        }
    }, [
        enqueueSnackbar,
        t,
        updateBillingInfoMutation,
        updateUserMutation.error,
        updateUserMutation.isError,
        updateUserMutation.isSuccess,
    ]);

    if (isLoading) {
        return <PageLoader />;
    }

    console.log({
        ...initialValues,
        name: billingInfo?.name || initialValues.name,
        shipping: {
            ...initialValues.shipping,
            ...(billingInfo?.shipping ?? {}),
        },
        language: user.language || 'ro-RO',
    });

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <Formik
                initialValues={{
                    ...initialValues,
                    name: billingInfo?.name || initialValues.name,
                    shipping: {
                        ...initialValues.shipping,
                        ...(billingInfo?.shipping ?? {}),
                    },
                    language: user.language || 'ro-RO',
                }}
                onSubmit={handleOnSubmit}
                validationSchema={billingInfoValidationSchema}
                validateOnMount
            >
                {() => (
                    <Stack spacing={3}>
                        <Box display={'flex'} flexDirection={'row'} height={'100%'}>
                            <StyledBox>
                                <Typography variant={'h5'} fontWeight={'bold'}>
                                    {t('billingInfo')}
                                </Typography>

                                <Form className={'form'}>
                                    <Stack spacing={3}>
                                        <FormikTextField
                                            name={'name'}
                                            label={t('name')}
                                            fullWidth
                                            color={'secondary'}
                                        />
                                        <FormikTextField
                                            name={'shipping.address.line1'}
                                            label={t('line1')}
                                            fullWidth
                                            color={'secondary'}
                                        />
                                        <FormikTextField
                                            name={'shipping.address.line2'}
                                            label={t('line2')}
                                            fullWidth
                                            color={'secondary'}
                                        />
                                        <FormikTextField
                                            name={'shipping.address.postalCode'}
                                            label={t('postalCode')}
                                            fullWidth
                                            color={'secondary'}
                                        />
                                        <FormikCountrySelect
                                            variant={CountrySelectVariant.CountryCode}
                                            name={'shipping.address.country'}
                                            label={'Select country'}
                                        />
                                        <FormikTextField
                                            name={'shipping.address.city'}
                                            label={t('city')}
                                            fullWidth
                                            color={'secondary'}
                                        />
                                        <FormikTextField
                                            name={'taxId'}
                                            label={t('taxId')}
                                            fullWidth
                                            color={'secondary'}
                                        />
                                    </Stack>
                                </Form>
                            </StyledBox>
                            <StyledBox>
                                <Typography variant={'h5'} fontWeight={'bold'}>
                                    {commonT('language')}
                                </Typography>
                                <Typography variant={'body1'}>{t('chooseLanguage')}</Typography>
                                <FormikCountrySelect
                                    variant={CountrySelectVariant.Language}
                                    name={'language'}
                                    className={'language'}
                                    onChange={handleLanguageChange}
                                />
                            </StyledBox>
                        </Box>
                        <FormikButton type={'button'} variant={'contained'} sx={{ alignSelf: 'flex-end' }}>
                            {commonT('saveChanges')}
                        </FormikButton>
                    </Stack>
                )}
            </Formik>
        </SettingsLayout>
    );
}

export default GeneralPage;
