import { GridColDef, GridSortModel } from '@mui/x-data-grid';
import PhoneNumber, { parsePhoneNumber } from 'awesome-phonenumber';
import { TFunction } from 'i18next';
import moment from 'moment-timezone';
import { ActivityModel } from '@bookr-technologies/api/models/ActivityModel';
import { ActivityType } from '@bookr-technologies/api/models/ActivityType';
import { StyledTooltip } from './styles';

export const defaultPageSize = 10;

export const getSort = (model: GridSortModel): string => {
    return model.map((modelElement) => `${modelElement.field},${modelElement.sort}`).join('&');
};

export const enumValueToReadableValue = (activityModel: ActivityModel, t: TFunction) => {
    const isAppointmentCompleted =
        moment(activityModel.appointment.dateTime)
            .add(activityModel.appointment.service.duration, 'minutes')
            .diff(Date.now()) < 0;

    let serviceLabel;
    if (activityModel.activityType === ActivityType.AppointmentCancelled) {
        serviceLabel = 'cancelled';
    } else if (activityModel.activityType === ActivityType.AppointmentRescheduled) {
        serviceLabel = 'rescheduled';
    } else if (activityModel.activityType === ActivityType.NewAppointment && isAppointmentCompleted) {
        serviceLabel = 'completed';
    } else {
        serviceLabel = 'confirmed';
    }

    return t(serviceLabel);
};

export const formatDateTime = (dateTime: string) => {
    return moment(dateTime).format('DD-MM-YYYY');
};

export const formatPhoneNumber = (value: string) => {
    const fallback = '-';

    if (!value) {
        return fallback;
    }

    try {
        const phoneNumber = parsePhoneNumber(value);

        return phoneNumber.isValid() ? phoneNumber.getNumber('international') : value;
    } catch (error) {
        return fallback;
    }
};

export const generateColumns = (t: TFunction) => {
    return [
        {
            field: 'appointment.client.displayName',
            headerName: t('columnClientName'),
            width: 150,
            renderHeader: () => (
                <StyledTooltip title={t('columnClientName').toString()}>
                    <div className="table-cell-truncate">{t('columnClientName') as string}</div>
                </StyledTooltip>
            ),
            renderCell: (params) => (
                <StyledTooltip title={params.row.client.displayName}>
                    <div className="table-cell-truncate">{params.row.appointment.client.displayName}</div>
                </StyledTooltip>
            ),
        },
        {
            field: 'appointment.client.phoneNumber',
            headerName: t('columnPhoneNumber'),
            width: 160,
            sortable: false,
            renderHeader: () => (
                <StyledTooltip title={t('columnPhoneNumber').toString()}>
                    <div className="table-cell-truncate">{t('columnPhoneNumber') as string}</div>
                </StyledTooltip>
            ),
            renderCell: (params) => (
                <StyledTooltip title={formatPhoneNumber(params.row.appointment.client.phoneNumber)}>
                    <div className="table-cell-truncate">
                        {formatPhoneNumber(params.row.appointment.client.phoneNumber)}
                    </div>
                </StyledTooltip>
            ),
        },
        {
            field: 'appointment.client.email',
            headerName: t('columnEmail'),
            width: 200,
            renderHeader: () => (
                <StyledTooltip title={t('columnEmail').toString()}>
                    <div className="table-cell-truncate">{t('columnEmail') as string}</div>
                </StyledTooltip>
            ),
            renderCell: (params) => (
                <StyledTooltip title={params.row.client.email}>
                    <div className="table-cell-truncate">{params.row.appointment.client.email}</div>
                </StyledTooltip>
            ),
        },
        {
            field: 'appointment.service.name',
            headerName: t('columnService'),
            width: 150,
            renderHeader: () => (
                <StyledTooltip title={t('columnService').toString()}>
                    <div className="table-cell-truncate">{t('columnService') as string}</div>
                </StyledTooltip>
            ),
            renderCell: (params) => (
                <StyledTooltip title={params.row.appointment.service.name}>
                    <div className="table-cell-truncate">{params.row.appointment.service.name}</div>
                </StyledTooltip>
            ),
        },
        {
            field: 'appointment.dateTime',
            headerName: t('columnDate'),
            width: 150,
            renderHeader: () => (
                <StyledTooltip title={t('columnDate').toString()}>
                    <div className="table-cell-truncate">{t('columnDate') as string}</div>
                </StyledTooltip>
            ),
            renderCell: (params) => (
                <StyledTooltip title={formatDateTime(params.row.appointment.dateTime)}>
                    <div className="table-cell-truncate">{formatDateTime(params.row.appointment.dateTime)}</div>
                </StyledTooltip>
            ),
        },
        {
            field: 'appointment.service.price',
            headerName: t('columnAmount'),
            width: 120,
            renderHeader: () => (
                <StyledTooltip title={t('columnAmount').toString()}>
                    <div className="table-cell-truncate">{t('columnAmount') as string}</div>
                </StyledTooltip>
            ),
            renderCell: (params) => (
                <StyledTooltip title={`${params.row.appointment.service.price} RON`}>
                    <div className="table-cell-truncate">{params.row.appointment.service.price} RON</div>
                </StyledTooltip>
            ),
        },
        {
            field: 'activityType',
            headerName: t('columnStatus'),
            width: 150,
            sortable: false,
            renderHeader: () => (
                <StyledTooltip title={t('columnStatus').toString()}>
                    <div className="table-cell-truncate">{t('columnStatus') as string}</div>
                </StyledTooltip>
            ),
            renderCell: (params) => (
                <StyledTooltip title={enumValueToReadableValue(params.row, t)}>
                    <div className="table-cell-truncate">{enumValueToReadableValue(params.row, t)}</div>
                </StyledTooltip>
            ),
        },
    ] as GridColDef[];
};
