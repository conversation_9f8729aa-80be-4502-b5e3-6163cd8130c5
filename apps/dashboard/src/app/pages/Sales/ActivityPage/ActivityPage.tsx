import { GridCallbackDetails, GridSortModel } from '@mui/x-data-grid';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { activityEndpoint } from '@bookr-technologies/api/endpoints/activityEndpoint';
import { ActivityModel } from '@bookr-technologies/api/models/ActivityModel';
import { getRequestParams } from '@bookr-technologies/api/utils/requestParams';
import { Page } from '../../../components/Page';
import { StyledDataGrid, StyledTableContainer } from './styles';
import { generateColumns, getSort, defaultPageSize } from './utils';

function ActivityPage() {
    const { t } = useTranslation('activityPage');
    const [page, setPage] = useState(0);
    const [sort, setSort] = useState('');

    const activities = useQuery(`activities/list:page=${page}:pageSize=${defaultPageSize}:sort=${sort}`, () => {
        const params = getRequestParams(page, defaultPageSize, sort);
        return activityEndpoint.fetchData(params);
    });

    const handleSortModelChange = (model: GridSortModel, details: GridCallbackDetails) => {
        setSort(getSort(model));
    };

    const handlePageChange = (page: number, details: GridCallbackDetails) => {
        setPage(page);
    };

    return (
        <Page title={t('title')}>
            <StyledTableContainer>
                <StyledDataGrid
                    autoHeight
                    autoPageSize
                    disableColumnMenu
                    disableColumnFilter
                    disableSelectionOnClick
                    paginationMode={'server'}
                    sortingMode={'server'}
                    pageSize={defaultPageSize}
                    loading={activities.isLoading}
                    rowCount={activities.data?.totalElements ?? 0}
                    page={page}
                    rows={activities.data?.content ?? ([] as ActivityModel[])}
                    columns={generateColumns(t)}
                    checkboxSelection={false}
                    onSortModelChange={handleSortModelChange}
                    onPageChange={handlePageChange}
                />
            </StyledTableContainer>
        </Page>
    );
}

export default ActivityPage;
