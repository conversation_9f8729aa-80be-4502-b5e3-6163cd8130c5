import Tooltip from '@mui/material/Tooltip';
import { styled } from '@mui/material/styles';
import { DataGrid } from '@mui/x-data-grid';

export const StyledTableContainer = styled('div')`
    width: 100%;

    .MuiDataGrid-root {
        border: none !important;
    }
`;

export const StyledDataGrid = styled(DataGrid)`
    .MuiDataGrid-cell:focus,
    .MuiDataGrid-cell:focus-within,
    .MuiDataGrid-columnHeader:focus,
    .MuiDataGrid-columnHeader:focus-within {
        outline: none !important;
    }

    .MuiDataGrid-columnHeaders {
        margin-top: 20px;
        margin-left: 20px;
        margin-right: 20px;
        border-radius: 10px;
        background-color: ${({ theme }) => theme.palette.grey['200']};
    }

    .MuiDataGrid-iconSeparator {
        color: ${({ theme }) => theme.palette.grey['300']};
    }

    .MuiDataGrid-columnHeaderTitleContainer > div {
        font-weight: bold !important;
    }

    .MuiDataGrid-cell {
        border-bottom: none;
    }

    .MuiContainer-root > div > div {
        border-radius: 20px !important;
    }

    .MuiDataGrid-main {
        padding-top: 20px;
        padding-left: 20px;
        padding-right: 20px;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        background-color: white;
    }

    .MuiDataGrid-footerContainer {
        border-bottom-left-radius: 16px;
        border-bottom-right-radius: 16px;
        background-color: white;
    }

    .MuiTablePagination-displayedRows {
        font-weight: bold;
    }

    .MuiDataGrid-columnHeader:focus,
    .MuiDataGrid-iconSeparator:focus,
    .MuiDataGrid-columnHeaderDraggableContainer:focus,
    .MuiDataGrid-columnHeader:focus-visible,
    .MuiDataGrid-iconSeparator:focus-visible,
    .MuiDataGrid-columnHeaderDraggableContainer:focus-visible {
        outline: none;
    }
`;

export const StyledTooltip = styled(Tooltip)`
    .table-cell-truncate {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
`;
