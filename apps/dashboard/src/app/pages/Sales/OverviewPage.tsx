import DateRangeOutlinedIcon from '@mui/icons-material/DateRangeOutlined';
import EventBusyOutlinedIcon from '@mui/icons-material/EventBusyOutlined';
import FactCheckOutlinedIcon from '@mui/icons-material/FactCheckOutlined';
import PeopleAltIcon from '@mui/icons-material/PeopleAlt';
import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectProps } from '@mui/material/Select';
import { AxiosError } from 'axios';
import { uniq } from 'lodash';
import moment, { Moment } from 'moment';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { SalesStats } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { DateRangePicker, DateRangePickerProps } from '../../components/DateRangePicker';
import { Page } from '../../components/Page';
import { StaffMemberSelect } from '../../components/StaffMemberSelect/StaffMemberSelect';
import { StatsCard } from '../../components/StatsCard';
import { TimeBookedCard } from '../../components/TimeBookedCard';
import { TopActivitiesCard } from '../../components/TopActivitiesCard';
import { ProfileCard, TopStaffMembersCard } from '../../components/TopStaffMembersCard';
import { TotalEarningsCard } from '../../components/TotalEarningsCard';
import { calculateTotalByCurrency } from '../../utils/calculateTotalByCurrency';

function OverviewPage() {
    const { t } = useTranslation('overviewPage');

    const [defaultStartDate, defaultEndDate] = useMemo(
        () => [moment().subtract(1, 'month').startOf('day'), moment().endOf('day')],
        [],
    );
    const [currency, setCurrency] = useState('');
    const [member, setMember] = useState<UserModel | null>(null);
    const [startDate, setStartDate] = useState<Moment>(defaultStartDate);
    const [endDate, setEndDate] = useState<Moment>(defaultEndDate);
    const business = useBusiness();
    const sales = useQuery(
        `sales/${startDate.unix()}/${endDate.unix()}`,
        () => businessEndpoint.getSales(startDate, endDate),
        {
            retry: (failureCount, error: any) => {
                const { response } = error as AxiosError;
                return ![401, 403].includes(response?.status || 0) && failureCount <= 3;
            },
        },
    );

    const isLoading = business.isLoading || sales.isLoading;

    const staffStats = useMemo(
        () =>
            (sales.data?.staffStats ?? []).sort(
                (a, b) =>
                    calculateTotalByCurrency(b.totalSales, currency) - calculateTotalByCurrency(a.totalSales, currency),
            ),
        [currency, sales.data?.staffStats],
    );

    const activeStaff = useMemo(
        () => staffStats.find(({ staff }) => staff.uid === member?.uid),
        [member?.uid, staffStats],
    );

    const totalEarningsData = useMemo(
        () =>
            (sales.data?.staffStats ?? []).reduce(
                (prev, { totalEarnings }) =>
                    totalEarnings.reduce((accum, total) => {
                        const date = moment(total.date);
                        const key = date.format('YYYY-MM-DD');
                        if (!accum[key]) {
                            accum[key] = {
                                date,
                                totals: {},
                            };
                        }
                        accum[key].totals[total.currency.toUpperCase()] ||= 0;
                        accum[key].totals[total.currency.toUpperCase()] += total.amount;

                        return accum;
                    }, prev),
                {} as Record<string, { date: Moment; totals: { [key: string]: number } }>,
            ),
        [sales.data?.staffStats],
    );

    const totalEarnings = useMemo(
        () => calculateTotalByCurrency(activeStaff?.totalSales ?? sales.data?.businessStats?.totalSales, currency),
        [activeStaff?.totalSales, currency, sales.data?.businessStats?.totalSales],
    );

    const currencies = useMemo(
        () =>
            uniq(
                (sales.data?.staffStats ?? []).reduce(
                    (accum, { totalEarnings }) => [
                        ...accum,
                        ...totalEarnings.map((currencies) => currencies.currency.toUpperCase()),
                    ],
                    [] as string[],
                ),
            ),
        [sales.data?.staffStats],
    );

    const series = useMemo(() => {
        const days = endDate.diff(startDate, 'days');
        return new Array(days + 1).fill(0).map((_, i) => {
            const day = startDate.clone().add(i, 'days');
            return {
                time: startDate.clone().add(i, 'days'),
                total: calculateTotalByCurrency(totalEarningsData[day.format('YYYY-MM-DD')]?.totals, currency),
            };
        });
    }, [startDate, endDate, totalEarningsData, currency]);

    const stats = useMemo<SalesStats>(
        () => ({
            clients: activeStaff?.clients ?? sales.data?.businessStats?.clients ?? 0,
            appointments: activeStaff?.appointments ?? sales.data?.businessStats?.appointments ?? 0,
            cancelled: activeStaff?.cancelled ?? sales.data?.businessStats?.cancelled ?? 0,
            finished: activeStaff?.finished ?? sales.data?.businessStats?.finished ?? 0,
            noShows: activeStaff?.noShows ?? sales.data?.businessStats?.noShows ?? 0,
            bookingStats: activeStaff?.bookingStats ??
                sales.data?.businessStats?.bookingStats ?? {
                    percentage: 0,
                    totalBookedMinutes: 0,
                    totalWorkingMinutes: 0,
                },
        }),
        [activeStaff, sales.data?.businessStats],
    );

    const handleStaffMemberChanged = useCallback((member: UserModel | null) => setMember(member), []);
    const handleResetMember = useCallback(() => setMember(null), []);
    const handleSelectCurrency = useCallback<Exclude<SelectProps<string>['onChange'], undefined>>(
        (e) => setCurrency(e.target.value),
        [],
    );
    const handleInterval = useCallback<DateRangePickerProps['onDatesChange']>(
        ({ startDate, endDate }) => {
            setStartDate(startDate ?? defaultStartDate);
            setEndDate(endDate ?? defaultEndDate);
        },
        [defaultEndDate, defaultStartDate],
    );

    useEffect(() => {
        setCurrency((prev) => (prev ? prev : (currencies ?? [])[0]));
    }, [currencies]);

    useEffect(() => {
        if (staffStats.length === 1) {
            setMember(staffStats[0].staff);
        }
    }, [staffStats]);

    return (
        <Page title={t('title')}>
            <Grid container mb={3}>
                <Grid item xs container alignItems={'center'}>
                    <DateRangePicker startDate={startDate} endDate={endDate} onDatesChange={handleInterval} />
                    {currencies.length > 1 ? (
                        <Box ml={3}>
                            <Select variant={'filled'} value={currency} onChange={handleSelectCurrency}>
                                {currencies.map((currency) => (
                                    <MenuItem key={currency} value={currency}>
                                        {currency}
                                    </MenuItem>
                                ))}
                            </Select>
                        </Box>
                    ) : null}
                </Grid>
                <Grid item xs container justifyContent={'flex-end'}>
                    {staffStats.length > 1 ? (
                        <StaffMemberSelect
                            size={'small'}
                            staffMember={member}
                            onStaffMemberChanged={handleStaffMemberChanged}
                        />
                    ) : null}
                </Grid>
            </Grid>
            {member && activeStaff ? (
                <Grid container mb={3}>
                    <ProfileCard
                        staffStat={activeStaff}
                        hideClose={staffStats.length <= 1}
                        onClose={handleResetMember}
                        currency={currency}
                    />
                </Grid>
            ) : null}
            <Grid container spacing={3} mb={3}>
                <Grid item container xs={3}>
                    <StatsCard
                        loading={isLoading}
                        icon={<DateRangeOutlinedIcon />}
                        title={t('totalAppointments')}
                        value={stats?.appointments ?? 0}
                    />
                </Grid>
                <Grid item container xs={3}>
                    <StatsCard
                        loading={isLoading}
                        icon={<FactCheckOutlinedIcon />}
                        title={t('appointmentsFinished')}
                        value={stats?.finished ?? 0}
                    />
                </Grid>
                <Grid item container xs={3}>
                    <StatsCard
                        loading={isLoading}
                        icon={<PeopleAltIcon />}
                        title={t('totalClients')}
                        value={stats?.clients ?? 0}
                    />
                </Grid>
                <Grid item container xs={3}>
                    <StatsCard
                        loading={isLoading}
                        icon={<EventBusyOutlinedIcon />}
                        title={t('appointmentsCanceled')}
                        value={stats?.cancelled ?? 0}
                    />
                </Grid>
            </Grid>
            <Grid container mb={3}>
                <TotalEarningsCard total={totalEarnings} percent={0} series={series} currency={currency} />
            </Grid>
            <Grid container spacing={3} mb={8}>
                <Grid item xs={7} container>
                    {!member ? (
                        <TopStaffMembersCard
                            onClickMember={handleStaffMemberChanged}
                            loading={isLoading}
                            currency={currency}
                            staffStats={staffStats}
                        />
                    ) : (
                        <TopActivitiesCard
                            totalServices={activeStaff?.appointments || 0}
                            serviceSalesActivity={activeStaff?.serviceSalesActivity ?? []}
                        />
                    )}
                </Grid>
                <Grid item xs={5} container>
                    <TimeBookedCard bookingStats={stats.bookingStats} loading={isLoading} />
                </Grid>
            </Grid>
        </Page>
    );
}

export default OverviewPage;
