import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Nullable } from '@bookr-technologies/core/types';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { LazyContent } from '../../components/LazyContent';
import { MultiStaffMemberSelect } from '../../components/StaffMemberSelect/MultiStaffMemberSelect';
import { ReadOnlyServicesManager } from '../../components/ServicesManager';

const StyledGrid = styled(Grid)(({ theme }) => ({
    marginBottom: theme.spacing(2),
}));

const StaffSelectionContainer = styled(Grid)(({ theme }) => ({
    '& .MuiSelect-root': {
        minWidth: 300,
    },
}));

function ServicesPage() {
    const { t } = useTranslation('businessSettingsPage');
    const { data: business, isLoading } = useBusiness();
    const [selectedStaffMembers, setSelectedStaffMembers] = useState<UserModel[]>([]);

    const handleStaffMemberChanged = useCallback((staff: Nullable<UserModel[]>) => {
        setSelectedStaffMembers(staff || []);
    }, []);

    const totalServices = business?.staffMembers?.reduce((total, staff) => {
        return total + (staff.services?.length || 0);
    }, 0) || 0;

    const selectedStaffCount = selectedStaffMembers.length;
    const totalStaffCount = business?.staffMembers?.length || 0;

    const getSubtitleText = () => {
        if (selectedStaffCount === 0) {
            return t('allStaffServicesSubtitle', {
                count: totalServices,
                staffCount: totalStaffCount
            });
        } else if (selectedStaffCount === 1) {
            const selectedServices = selectedStaffMembers[0]?.services?.length || 0;
            return t('singleStaffServicesSubtitle', {
                count: selectedServices,
                staffName: selectedStaffMembers[0]?.displayName
            });
        } else {
            const selectedServices = selectedStaffMembers.reduce((total, staff) => {
                return total + (staff.services?.length || 0);
            }, 0);
            return t('multipleStaffServicesSubtitle', {
                count: selectedServices,
                staffCount: selectedStaffCount
            });
        }
    };

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <SettingsLayoutSection title={t('businessServices')}>
                <LazyContent loading={isLoading}>
                    <StyledGrid container direction="column">
                        <Grid item>
                            <Grid container alignItems="center" justifyContent="space-between" mb={2}>
                                <Grid item>
                                    <Typography variant="body2" color="textSecondary" fontWeight={500}>
                                        {getSubtitleText()}
                                    </Typography>
                                </Grid>
                                <StaffSelectionContainer item>
                                    <MultiStaffMemberSelect
                                        multiple
                                        onStaffMemberChanged={handleStaffMemberChanged}
                                        staffMember={selectedStaffMembers}
                                    />
                                </StaffSelectionContainer>
                            </Grid>
                        </Grid>

                        <Grid item>
                            {business?.staffMembers ? (
                                <ReadOnlyServicesManager
                                    staffMembers={business.staffMembers}
                                    selectedStaffMembers={selectedStaffMembers}
                                />
                            ) : (
                                <Grid container direction={'column'} alignItems={'center'} justifyContent={'center'} py={6}>
                                    <Typography variant={'h5'} fontWeight={500} mb={1}>
                                        {t('noStaffMembers')}
                                    </Typography>
                                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                        {t('noStaffMembersDescription')}
                                    </Typography>
                                </Grid>
                            )}
                        </Grid>
                    </StyledGrid>
                </LazyContent>
            </SettingsLayoutSection>
        </SettingsLayout>
    );
}

export default ServicesPage;
