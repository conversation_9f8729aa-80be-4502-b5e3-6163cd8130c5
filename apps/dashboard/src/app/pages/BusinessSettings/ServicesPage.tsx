import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Nullable } from '@bookr-technologies/core/types';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { LazyContent } from '../../components/LazyContent';
import { MultiStaffMemberSelect } from '../../components/StaffMemberSelect/MultiStaffMemberSelect';
import { ReadOnlyServicesManager } from '../../components/ServicesManager';

const StyledGrid = styled(Grid)(({ theme }) => ({
    marginBottom: theme.spacing(2),
}));

const StaffSelectionContainer = styled(Grid)(({ theme }) => ({
    '& .MuiSelect-root': {
        minWidth: 300,
    },
}));

const SearchContainer = styled(Grid)(({ theme }) => ({
    '& .MuiTextField-root': {
        minWidth: 250,
        maxWidth: 350,
    },
}));

const HeaderContainer = styled(Grid)(({ theme }) => ({
    marginBottom: theme.spacing(2),
}));

function ServicesPage() {
    const { t } = useTranslation('businessSettingsPage');
    const { data: business, isLoading } = useBusiness();
    const [selectedStaffMembers, setSelectedStaffMembers] = useState<UserModel[]>([]);
    const [searchTerm, setSearchTerm] = useState<string>('');

    const handleStaffMemberChanged = useCallback((staff: Nullable<UserModel[]>) => {
        setSelectedStaffMembers(staff || []);
    }, []);

    const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(event.target.value);
    }, []);

    const handleClearSearch = useCallback(() => {
        setSearchTerm('');
    }, []);

    // Calculate filtered services based on staff selection and search
    const filteredServicesData = useMemo(() => {
        const staffToShow = selectedStaffMembers.length > 0 ? selectedStaffMembers : (business?.staffMembers || []);

        let allServices: Array<{ service: any; staffMember: UserModel }> = [];

        staffToShow.forEach(staff => {
            if (staff.services && staff.services.length > 0) {
                staff.services.forEach(service => {
                    allServices.push({ service, staffMember: staff });
                });
            }
        });

        // Apply search filter
        if (searchTerm.trim()) {
            const searchLower = searchTerm.toLowerCase();
            allServices = allServices.filter(({ service }) =>
                service.name.toLowerCase().includes(searchLower) ||
                (service.description && service.description.toLowerCase().includes(searchLower))
            );
        }

        return {
            services: allServices,
            totalCount: allServices.length,
            staffCount: staffToShow.length,
            totalStaffCount: business?.staffMembers?.length || 0
        };
    }, [business?.staffMembers, selectedStaffMembers, searchTerm]);

    const getSubtitleText = () => {
        const { totalCount, staffCount, totalStaffCount } = filteredServicesData;
        const hasSearch = searchTerm.trim().length > 0;
        const selectedStaffCount = selectedStaffMembers.length;

        if (hasSearch && selectedStaffCount === 0) {
            return t('searchAllStaffServicesSubtitle', {
                count: totalCount,
                staffCount: totalStaffCount,
                searchTerm: searchTerm.trim()
            });
        } else if (hasSearch && selectedStaffCount === 1) {
            return t('searchSingleStaffServicesSubtitle', {
                count: totalCount,
                staffName: selectedStaffMembers[0]?.displayName,
                searchTerm: searchTerm.trim()
            });
        } else if (hasSearch && selectedStaffCount > 1) {
            return t('searchMultipleStaffServicesSubtitle', {
                count: totalCount,
                staffCount: selectedStaffCount,
                searchTerm: searchTerm.trim()
            });
        } else if (selectedStaffCount === 0) {
            return t('allStaffServicesSubtitle', {
                count: totalCount,
                staffCount: totalStaffCount
            });
        } else if (selectedStaffCount === 1) {
            return t('singleStaffServicesSubtitle', {
                count: totalCount,
                staffName: selectedStaffMembers[0]?.displayName
            });
        } else {
            return t('multipleStaffServicesSubtitle', {
                count: totalCount,
                staffCount: selectedStaffCount
            });
        }
    };

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <SettingsLayoutSection
                header={
                    <HeaderContainer container alignItems="center" justifyContent="space-between">
                        <Grid item>
                            <Typography
                                variant="h5"
                                fontWeight={600}
                                fontSize={24}
                                lineHeight="32px"
                                color="#333"
                            >
                                {t('businessServices')}
                            </Typography>
                        </Grid>
                        <SearchContainer item>
                            <TextField
                                size="small"
                                placeholder={t('searchServicesPlaceholder')}
                                value={searchTerm}
                                onChange={handleSearchChange}
                                InputProps={{
                                    startAdornment: (
                                        <InputAdornment position="start">
                                            <SearchIcon color="action" />
                                        </InputAdornment>
                                    ),
                                    endAdornment: searchTerm && (
                                        <InputAdornment position="end">
                                            <IconButton
                                                size="small"
                                                onClick={handleClearSearch}
                                                edge="end"
                                            >
                                                <ClearIcon />
                                            </IconButton>
                                        </InputAdornment>
                                    ),
                                }}
                            />
                        </SearchContainer>
                        <StaffSelectionContainer item>
                            <MultiStaffMemberSelect
                                multiple
                                onStaffMemberChanged={handleStaffMemberChanged}
                                staffMember={selectedStaffMembers}
                            />
                        </StaffSelectionContainer>
                    </HeaderContainer>
                }
            >
                <LazyContent loading={isLoading}>
                    <StyledGrid container direction="column">
                        <Grid item>
                            <Typography variant="body2" color="textSecondary" fontWeight={500} mb={2}>
                                {getSubtitleText()}
                            </Typography>
                        </Grid>

                        <Grid item>
                            {business?.staffMembers ? (
                                <ReadOnlyServicesManager
                                    staffMembers={business.staffMembers}
                                    selectedStaffMembers={selectedStaffMembers}
                                    searchTerm={searchTerm}
                                />
                            ) : (
                                <Grid container direction={'column'} alignItems={'center'} justifyContent={'center'} py={6}>
                                    <Typography variant={'h5'} fontWeight={500} mb={1}>
                                        {t('noStaffMembers')}
                                    </Typography>
                                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                        {t('noStaffMembersDescription')}
                                    </Typography>
                                </Grid>
                            )}
                        </Grid>
                    </StyledGrid>
                </LazyContent>
            </SettingsLayoutSection>
        </SettingsLayout>
    );
}

export default ServicesPage;
