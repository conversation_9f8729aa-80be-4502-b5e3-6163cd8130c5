import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { Form, Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { categoriesEndpoint } from '@bookr-technologies/api/endpoints/categoriesEndpoint';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import { FormikCheckbox } from '@bookr-technologies/ui/Fields/FormikCheckbox';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { LazyContent } from '../../components/LazyContent';
import { useBusinessSettings } from '../../hooks/useBusinessSettings';
import { signUpBusinessSchema } from '../../validators/signUpBusinessValidators';

const StyledStack = styled(Stack)({
    '.MuiFormControlLabel-label': {
        fontWeight: 500,
    },
});

function CategoriesPage() {
    const { t } = useTranslation('businessSettingsPage');
    const { t: common } = useTranslation('common');
    const { initialValues, isLoading, handleSubmitCategories } = useBusinessSettings();

    const categories = useQuery('availableCategories', () => categoriesEndpoint.fetchAvailableCategories());

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <SettingsLayoutSection title={t('allCategories')}>
                <LazyContent loading={isLoading}>
                    <Formik
                        initialValues={initialValues}
                        onSubmit={handleSubmitCategories}
                        validationSchema={signUpBusinessSchema.categoriesPage}
                        validateOnMount
                        validateOnChange
                        validateOnBlur
                    >
                        <Form>
                            {categories.isLoading ? (
                                <Grid container alignItems={'center'} justifyContent={'center'} p={2}>
                                    <CircularProgress />
                                </Grid>
                            ) : null}
                            <StyledStack pt={2}>
                                {categories.data?.map(({ code }) => (
                                    <FormikCheckbox
                                        key={code}
                                        name={'categories'}
                                        value={code}
                                        label={common(`categories.${code.toLowerCase()}`)}
                                        color={'accent'}
                                    />
                                ))}
                            </StyledStack>
                            <Grid container justifyContent={'flex-end'} mt={3}>
                                <FormikButton variant={'contained'} disableElevation>
                                    {common('saveChanges')}
                                </FormikButton>
                            </Grid>
                        </Form>
                    </Formik>
                </LazyContent>
            </SettingsLayoutSection>
        </SettingsLayout>
    );
}

export default CategoriesPage;
