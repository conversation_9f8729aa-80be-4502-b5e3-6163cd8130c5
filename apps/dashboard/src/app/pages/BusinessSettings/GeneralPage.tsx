import ShareLocationIcon from '@mui/icons-material/ShareLocation';
import Divider from '@mui/material/Divider';
import FormLabel from '@mui/material/FormLabel';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import { Form, Formik, FormikProps } from 'formik';
import { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { LocationDialog } from '@bookr-technologies/ui/LocationDialog';
import { LocationDialogValues } from '@bookr-technologies/ui/LocationDialog/LocationDialog';
import { ImagePicker } from '../../components/ImagePicker';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { LazyContent } from '../../components/LazyContent';
import { useBusinessSettings } from '../../hooks/useBusinessSettings';

function GeneralPage() {
    const { t } = useTranslation('businessSettingsPage');
    const { t: common } = useTranslation('common');
    const [locationDialogOpen, setLocationDialogOpen] = useState(false);
    const { initialValues, isLoading, handleSubmitGeneral, businessId } = useBusinessSettings();
    const formikRef = useRef<FormikProps<typeof initialValues> | null>(null);

    const handleOpenLocationDialog = useCallback(
        (e: any) => {
            e.preventDefault();
            setLocationDialogOpen(true);
        },
        [setLocationDialogOpen],
    );

    const handleCloseLocationDialog = useCallback(() => setLocationDialogOpen(false), [setLocationDialogOpen]);

    const handleDeleteImage = useCallback(async () => {
        if (businessId) {
            await businessEndpoint.removeProfilePicture(businessId);
        }
    }, [businessId]);

    const handleLocationSubmit = useCallback(({ location, name }: LocationDialogValues) => {
        formikRef.current?.setFieldValue('location', location);
        formikRef.current?.setFieldValue('locationName', name);
    }, []);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <LazyContent loading={isLoading}>
                <Formik initialValues={initialValues} onSubmit={handleSubmitGeneral} innerRef={formikRef}>
                    {({ values }) => (
                        <Grid component={Form} container>
                            <Grid item xs py={2} pl={1} pr={5}>
                                <Grid container>
                                    <ImagePicker name={'profilePicture'} onDelete={handleDeleteImage} />
                                </Grid>
                                <SettingsLayoutSection title={t('accountDetails')} mt={4}>
                                    <Grid container spacing={3} mt={0}>
                                        <Grid item xs>
                                            <FormLabel>{t('businessName')}</FormLabel>
                                            <FormikTextField name={'businessName'} size={'small'} fullWidth />
                                        </Grid>
                                        <Grid item xs>
                                            <FormLabel>{t('phoneNumber')}</FormLabel>
                                            <FormikTextField name={'phoneNumber'} size={'small'} fullWidth />
                                        </Grid>
                                    </Grid>
                                    <Grid container mt={3}>
                                        <FormLabel>{t('businessDescription')}</FormLabel>
                                        <FormikTextField
                                            name={'businessDescription'}
                                            size={'small'}
                                            fullWidth
                                            multiline
                                            minRows={4}
                                            maxRows={8}
                                        />
                                    </Grid>
                                </SettingsLayoutSection>
                            </Grid>
                            <Divider orientation={'vertical'} variant={'middle'} flexItem />
                            <Grid item xs py={2} pl={5} pr={1}>
                                <SettingsLayoutSection title={t('socialNetworks')}>
                                    <Grid container mt={3}>
                                        <FormLabel>{t('facebook')}</FormLabel>
                                        <FormikTextField name={'facebook'} size={'small'} fullWidth />
                                    </Grid>
                                    <Grid container mt={3}>
                                        <FormLabel>{t('instagram')}</FormLabel>
                                        <FormikTextField name={'instagram'} size={'small'} fullWidth />
                                    </Grid>
                                    <Grid container mt={3}>
                                        <FormLabel>{t('website')}</FormLabel>
                                        <FormikTextField name={'website'} size={'small'} fullWidth />
                                    </Grid>
                                </SettingsLayoutSection>
                                <SettingsLayoutSection title={t('location')} mt={3}>
                                    <Grid container alignItems={'center'} mt={2}>
                                        <ShareLocationIcon fontSize={'large'} />
                                        <Link
                                            onClick={handleOpenLocationDialog}
                                            underline={'none'}
                                            color={'accent.main'}
                                            fontWeight={500}
                                            marginLeft={2}
                                            href={'#'}
                                        >
                                            {t('changeLocation')}
                                        </Link>
                                    </Grid>

                                    <LocationDialog
                                        locationName={values.locationName}
                                        location={values.location}
                                        title={t('changeLocation')}
                                        open={locationDialogOpen}
                                        onClose={handleCloseLocationDialog}
                                        onSubmit={handleLocationSubmit}
                                    />
                                </SettingsLayoutSection>
                                <Grid container justifyContent={'flex-end'} mt={3}>
                                    <FormikButton variant={'contained'} disableElevation>
                                        {common('saveChanges')}
                                    </FormikButton>
                                </Grid>
                            </Grid>
                        </Grid>
                    )}
                </Formik>
            </LazyContent>
        </SettingsLayout>
    );
}

export default GeneralPage;
