import ViewInAr from '@mui/icons-material/ViewInAr';
import LoadingButton from '@mui/lab/LoadingButton';
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { virtualTourEndpoint } from '@bookr-technologies/api/endpoints/virtualTourEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';
import { SettingsLayout } from '../../components/Layout';

const StyledAvatar = styled(Avatar)({
    backgroundColor: 'rgba(47, 128, 251, 0.1)',
    width: 100,
    height: 100,
    marginBottom: 26,
    '.icon': {
        fontSize: 48,
    },
});

function VirtualTourPage() {
    const { t } = useTranslation('businessSettingsPage');
    const user = useCurrentUser();
    const { enqueueSnackbar } = useSnackbar();
    const [loading, setLoading] = useState(false);

    const handleRequest = useCallback(async () => {
        setLoading(true);
        try {
            if (user?.business?.id) {
                await virtualTourEndpoint.requestVirtualTour(user?.business?.id);
                enqueueSnackbar(t('virtualTourRequestMessage'), { variant: 'success' });
            }
        } catch (e) {
            const message = getErrorMessage(e, 'errorRequestingVirtualTour');
            enqueueSnackbar(t(message), { variant: 'error' });
        }
        setLoading(false);
    }, [enqueueSnackbar, user?.business?.id, t]);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <Grid container direction={'column'} alignItems={'center'} justifyContent={'center'} py={22}>
                <StyledAvatar>
                    <ViewInAr className={'icon'} fontSize={'large'} color={'accent'} />
                </StyledAvatar>

                <Typography variant={'h6'} fontWeight={700} mb={1.25}>
                    {t('add3dVirtualTour')}
                </Typography>
                <Typography
                    variant={'body2'}
                    color={'textSecondary'}
                    align={'center'}
                    maxWidth={'360px !important'}
                    mb={3}
                >
                    {t('add3dVirtualTourDescription')}
                </Typography>

                <LoadingButton variant={'contained'} disableElevation onClick={handleRequest} loading={loading}>
                    {t('requestAccess')}
                </LoadingButton>
            </Grid>
        </SettingsLayout>
    );
}

export default VirtualTourPage;
