import LoadingButton from '@mui/lab/LoadingButton';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';
import {
    IntegrationTypeEnum,
    useGetAuthorizationUrlMutation,
    useIntegrationsQuery,
} from '@bookr-technologies/api/integrations';
import { SettingsLayout } from '../../components/Layout';
import { LazyContent } from '../../components/LazyContent';

function IntegrationsPage() {
    const { t } = useTranslation('businessSettingsPage');
    const { t: common } = useTranslation('common');
    const integrationsQuery = useIntegrationsQuery();
    const getAuthorizationUrlMutation = useGetAuthorizationUrlMutation();

    const handleIntegrationEnable = (integrationType: IntegrationTypeEnum) => () =>
        getAuthorizationUrlMutation.mutate(integrationType, {
            onSuccess: (data) => {
                window.location.href = data;
            },
        });

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <Typography variant={'h5'} fontWeight={700}>
                Integrations
            </Typography>
            <Typography variant={'caption'} color={'textSecondary'} fontWeight={500}>
                Manage your integrations. You can enable or disable them at any time.
            </Typography>

            <LazyContent loading={integrationsQuery.isLoading}>
                <Grid container flexWrap={'wrap'} mt={3}>
                    {integrationsQuery.data
                        ?.filter((i) => i.active)
                        ?.map((integration) => (
                            <Grid item xs={6} key={integration.id} p={1}>
                                <Card sx={{ p: 2 }}>
                                    <Grid
                                        direction={'row'}
                                        container
                                        alignItems={'center'}
                                        justifyContent={'space-between'}
                                        mb={2}
                                    >
                                        <Grid xs item container alignItems={'center'} direction={'row'}>
                                            <Avatar
                                                variant={'rounded'}
                                                src={integration.photoURL}
                                                sx={{ bgcolor: '#fafafa', boxShadow: 1, width: 54, height: 54 }}
                                            />
                                            <Stack ml={2}>
                                                <Typography variant={'body1'} fontWeight={600}>
                                                    {integration.name}
                                                </Typography>
                                                <Link
                                                    variant={'caption'}
                                                    fontWeight={600}
                                                    href={integration.website}
                                                    target={'_blank'}
                                                    rel={'noreferrer noopener'}
                                                    color={'secondary'}
                                                >
                                                    {common('website')}
                                                </Link>
                                            </Stack>
                                        </Grid>
                                        <LoadingButton
                                            variant={'contained'}
                                            color={'primary'}
                                            disabled={integration.enabled}
                                            size={'small'}
                                            onClick={handleIntegrationEnable(integration.integrationType)}
                                            loading={getAuthorizationUrlMutation.isLoading}
                                        >
                                            {integration.enabled ? common('enabled') : common('enable')}
                                        </LoadingButton>
                                    </Grid>

                                    {integration.integrationType === IntegrationTypeEnum.Zoom && (
                                        <Typography variant={'caption'} fontWeight={500} color={'textSecondary'}>
                                            Enable Zoom integration to generate online meetings for your online
                                            services.
                                        </Typography>
                                    )}
                                    {integration.integrationType === IntegrationTypeEnum.GoogleCalendar && (
                                        <Typography variant={'caption'} fontWeight={500} color={'textSecondary'}>
                                            Enable Google Meet integration to generate online meetings for your online
                                            services.
                                        </Typography>
                                    )}
                                </Card>
                            </Grid>
                        ))}
                </Grid>
            </LazyContent>
        </SettingsLayout>
    );
}

export default IntegrationsPage;
