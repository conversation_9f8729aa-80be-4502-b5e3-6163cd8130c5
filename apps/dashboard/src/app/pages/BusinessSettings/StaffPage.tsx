import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography, { TypographyProps } from '@mui/material/Typography';
import { Form, Formik } from 'formik';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { EmployeeManager } from '../../components/EmployeeManager';
import { SettingsLayout, SettingsLayoutSection, StyledPaper } from '../../components/Layout';
import { LazyContent } from '../../components/LazyContent';
import { useBusinessSettings } from '../../hooks/useBusinessSettings';

function StaffPage() {
    const { t } = useTranslation('businessSettingsPage');
    const { t: common } = useTranslation('common');

    const { initialValues, isLoading, handleSubmitStaff } = useBusinessSettings();

    const titleProps = useMemo<TypographyProps>(
        () => ({
            fontWeight: '700',
            fontSize: 18,
            lineHeight: '30px',
            color: '#111',
        }),
        [],
    );

    const handleDelete = useCallback(async (uid: string) => {
        await businessEndpoint.removeStaffMember(uid);
    }, []);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')} noPaper>
            <LazyContent loading={isLoading}>
                <Formik initialValues={initialValues} onSubmit={handleSubmitStaff}>
                    <Form>
                        <Grid container alignItems={'flex-start'} spacing={3}>
                            <Grid item xs>
                                <StyledPaper>
                                    <SettingsLayoutSection title={t('yourEmployees')} titleProps={titleProps}>
                                        <Grid container mt={3} width={'100%'} minHeight={90}>
                                            <EmployeeManager name={'staffMembers'} onDelete={handleDelete} />
                                        </Grid>

                                        <Grid container justifyContent={'flex-end'} mt={3}>
                                            <FormikButton variant={'contained'} disableElevation>
                                                {common('saveChanges')}
                                            </FormikButton>
                                        </Grid>
                                    </SettingsLayoutSection>
                                </StyledPaper>
                            </Grid>
                            <Grid item xs>
                                <StyledPaper>
                                    <SettingsLayoutSection title={t('addNewEmployee')} titleProps={titleProps} mt={0}>
                                        <Typography variant={'body1'} color={'textSecondary'} fontWeight={500}>
                                            {t('addNewEmployeeDescription')}
                                        </Typography>

                                        <Stack direction={'row'} spacing={3} mt={3}>
                                            <Grid item xs maxWidth={'250px !important'}>
                                                <FormikTextField
                                                    name={'email'}
                                                    type={'email'}
                                                    label={t('Email')}
                                                    fullWidth
                                                />
                                            </Grid>
                                        </Stack>

                                        <Grid container justifyContent={'flex-end'} mt={3}>
                                            <FormikButton variant={'contained'} disableElevation>
                                                {t('sendInvite')}
                                            </FormikButton>
                                        </Grid>
                                    </SettingsLayoutSection>
                                </StyledPaper>
                            </Grid>
                        </Grid>
                    </Form>
                </Formik>
            </LazyContent>
        </SettingsLayout>
    );
}

export default StaffPage;
