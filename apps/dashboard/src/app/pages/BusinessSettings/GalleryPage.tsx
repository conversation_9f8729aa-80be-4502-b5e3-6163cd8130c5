import Grid from '@mui/material/Grid';
import { Form, Formik } from 'formik';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import { GalleryGrid } from '../../components/GalleryGrid';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { LazyContent } from '../../components/LazyContent';
import { useBusinessSettings } from '../../hooks/useBusinessSettings';

function GalleryPage() {
    const { t } = useTranslation('businessSettingsPage');
    const { t: common } = useTranslation('common');

    const { initialValues, isLoading, handleSubmitGallery } = useBusinessSettings();

    const handleDelete = useCallback(async (image: any) => {
        await businessEndpoint.removeWorkplacePicture(image);
    }, []);

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')}>
            <SettingsLayoutSection title={t('yourGallery')}>
                <LazyContent loading={isLoading}>
                    <Formik initialValues={initialValues} onSubmit={handleSubmitGallery}>
                        <Form>
                            <GalleryGrid name={'gallery'} marginTop={0.5} onDelete={handleDelete} />
                            <Grid container justifyContent={'flex-end'} mt={3}>
                                <FormikButton variant={'contained'} disableElevation>
                                    {common('saveChanges')}
                                </FormikButton>
                            </Grid>
                        </Form>
                    </Formik>
                </LazyContent>
            </SettingsLayoutSection>
        </SettingsLayout>
    );
}

export default GalleryPage;
