/* eslint-disable @typescript-eslint/no-explicit-any */
import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import MenuItem from '@mui/material/MenuItem';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { Form, Formik } from 'formik';
import { useSnackbar } from 'notistack';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { notificationsEndpoint } from '@bookr-technologies/api/endpoints/notificationEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { useUser } from '@bookr-technologies/store/hooks/useUser';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import FormikSelect from '@bookr-technologies/ui/Fields/FormikSelect';
import { SettingsLayout, SettingsLayoutSection } from '../../components/Layout';
import { PreviewTemplateEditor } from '../../components/PreviewTemplateEditor';
import { TemplateEditor } from '../../components/TemplateEditor';

const Root = styled(Paper)(({ theme }) => ({
    marginTop: theme.spacing(2),
}));

function NotificationsPage() {
    const [isSendingTestMessage, setIsSendingTestMessage] = useState(false);
    const { t } = useTranslation('businessSettingsPage');
    const business = useBusiness();
    const confirm = useConfirmation();
    const { enqueueSnackbar } = useSnackbar();
    const user = useUser();
    const phoneNumber = user?.phoneNumber;

    const initialValues = useMemo(
        () => ({
            selectedTemplateType: 'templateAppointmentCreated',
            message: '',
        }),
        [],
    );

    const handleSubmit = useCallback(
        async ({ selectedTemplateType, message }: any) => {
            if (business.data?.id) {
                await businessEndpoint.update(business.data?.id, {
                    [selectedTemplateType]: message,
                });
            }
        },
        [business.data?.id],
    );

    const handleSendTestMessage = useCallback(
        (template: string) => async () => {
            const result = await confirm({
                title: t('sendTestMessageConfirmationTitle', { phoneNumber }),
                message: t('sendTestMessageConfirmationMessage'),
            });

            if (result) {
                setIsSendingTestMessage(true);
                try {
                    await notificationsEndpoint.sendTestMessage(template);
                    enqueueSnackbar(t('testMessageSent'), { variant: 'success' });
                } catch (error: any) {
                    const message = getErrorMessage(error, 'errorSendingTestMessage');
                    enqueueSnackbar(t(message), { variant: 'error' });
                }
                setIsSendingTestMessage(false);
            }
        },
        [confirm, t, phoneNumber, enqueueSnackbar],
    );

    return (
        <SettingsLayout title={t('title')} subtitle={t('subtitle')} noPaper>
            <Root>
                <Formik initialValues={initialValues} onSubmit={handleSubmit}>
                    {({ values }) => (
                        <Form>
                            <SettingsLayoutSection title={t('personalizeYourMessages')} py={3} px={4.5}>
                                <Typography variant={'body2'} fontWeight={500} color={'textSecondary'} mt={1} mb={3}>
                                    {t('personalizeYourMessagesDescription')}
                                </Typography>

                                <Paper variant={'outlined'} sx={{ mb: 5 }}>
                                    <Grid container alignItems={'center'}>
                                        <Grid item xs={9}>
                                            <Typography variant={'body1'} fontWeight={600} mb={1}>
                                                {t('selectTemplateType')}
                                            </Typography>
                                            <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                                {t(`selectTemplateTypeDescription.${values.selectedTemplateType}`)}
                                            </Typography>
                                        </Grid>
                                        <Grid item xs={3}>
                                            <FormikSelect name={'selectedTemplateType'} variant={'filled'} fullWidth>
                                                <MenuItem value={'templateAppointmentReminder'}>
                                                    {t('templateNames.templateAppointmentReminder')}
                                                </MenuItem>
                                                <MenuItem value={'templateAppointmentCreated'}>
                                                    {t('templateNames.templateAppointmentCreated')}
                                                </MenuItem>
                                                <MenuItem value={'templateAppointmentCancelled'}>
                                                    {t('templateNames.templateAppointmentCancelled')}
                                                </MenuItem>
                                                <MenuItem value={'templateAppointmentRescheduled'}>
                                                    {t('templateNames.templateAppointmentRescheduled')}
                                                </MenuItem>
                                            </FormikSelect>
                                        </Grid>
                                    </Grid>
                                </Paper>

                                <TemplateEditor
                                    name={'message'}
                                    defaultValue={
                                        business.data ? (business.data as any)[values.selectedTemplateType] : ''
                                    }
                                />
                            </SettingsLayoutSection>

                            <SettingsLayoutSection title={t('previewMessage')} titleProps={{ mb: 2 }} pt={3} px={4.5}>
                                <PreviewTemplateEditor name={'message'} />
                            </SettingsLayoutSection>

                            <Grid container alignItems={'center'} justifyContent={'flex-end'} p={4.5}>
                                <LoadingButton
                                    onClick={handleSendTestMessage(values.message)}
                                    variant={'contained'}
                                    color={'inherit'}
                                    disableElevation
                                    sx={{ mr: 3.25 }}
                                    loading={isSendingTestMessage}
                                >
                                    {t('sendTestMessage')}
                                </LoadingButton>
                                <FormikButton variant={'contained'} color={'primary'} disableElevation>
                                    {t('saveMessage')}
                                </FormikButton>
                            </Grid>
                        </Form>
                    )}
                </Formik>
            </Root>
        </SettingsLayout>
    );
}

export default NotificationsPage;
