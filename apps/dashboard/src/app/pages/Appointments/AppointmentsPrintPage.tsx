import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import moment from 'moment';
import { useState } from 'react';
import { useQuery } from 'react-query';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { AppointmentDocumentItem, AppointmentsDocument } from '@bookr-technologies/exports';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';
import { useUrlQueryParams } from '@bookr-technologies/ui/hooks/useUrlQueryParams';
import { getBusinessWithAppointments } from '../../utils/appointments';

function AppointmentsPrintPage() {
    const uid = useUrlQueryParams('uid');
    const date = useUrlQueryParams('date') ?? new Date();
    const print = useUrlQueryParams('print');

    const user = useCurrentUser();
    const [staffMember, setStaffMember] = useState<UserModel | null>(null);

    const businessId = user?.business?.id;

    const events = useQuery(
        ['exports', 'appointments', uid, date],
        async (): Promise<AppointmentDocumentItem[]> => {
            if (!businessId) {
                return [];
            }

            const business = await getBusinessWithAppointments(
                businessId,
                moment(date).startOf('day').unix(),
                moment(date).endOf('day').unix(),
            );
            const staffMember = business.staffMembers.find((staff) => staff.uid === uid);
            const appointments = staffMember?.appointmentsAsStaff ?? [];
            const breaks = staffMember?.breaks ?? [];

            setStaffMember(staffMember ?? null);

            return [
                ...appointments.map((item) => ({ ...item, type: 'appointment' as const })),
                ...breaks.map((item) => ({ ...item, type: 'break' as const })),
            ];
        },
        {
            enabled: !!businessId,
        },
    );

    if (events.isLoading) {
        return (
            <Box height={'100vh'} width={'100%'} display={'flex'} alignItems={'center'} justifyContent={'center'}>
                <CircularProgress />
            </Box>
        );
    }

    return (
        <AppointmentsDocument
            date={moment(date).toDate()}
            items={events.data ?? []}
            staffMember={staffMember}
            print={Boolean(print)}
        />
    );
}

export default AppointmentsPrintPage;
