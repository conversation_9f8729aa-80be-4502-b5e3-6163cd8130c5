import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import PrintIcon from '@mui/icons-material/Print';
import ViewDayIcon from '@mui/icons-material/ViewDay';
import ViewWeekIcon from '@mui/icons-material/ViewWeek';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import moment, { Moment } from 'moment-timezone';
import { useSnackbar } from 'notistack';
import { useCallback, useMemo, useRef, useState } from 'react';
import { emitCustomEvent, useCustomEventListener } from 'react-custom-events';
import { useTranslation } from 'react-i18next';
import FullCalendar, { FormatterInput } from '@fullcalendar/react';
import { EventInput } from '@fullcalendar/common';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin, { DateClickArg } from '@fullcalendar/interaction';
import resourceTimeGridPlugin from '@fullcalendar/resource-timegrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { getWeekDayIndex } from '@bookr-technologies/core/datetime/WeekDays';
import { Nullable } from '@bookr-technologies/core/types';
import { ExportsDialog } from '@bookr-technologies/exports';
import { useAuthStore } from '@bookr-technologies/store';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';
import { UserProfile } from '@bookr-technologies/ui/UserProfile';
import { useIntervalAsync } from '../../../../../../libs/hooks/src/lib/use-interval-async';
import { CalendarEventContent } from '../../components/CalendarEventContent/CalendarEventContent';
import { Page } from '../../components/Page';
import { MultiStaffMemberSelect } from '../../components/StaffMemberSelect/MultiStaffMemberSelect';
import WeekPicker from '../../components/WeekPicker/WeekPicker';
import { CustomEvent } from '../../enums/CustomEvent';
import {
    getBusinessWithAppointments,
    mapBreakAppointmentsToCalendarEvents,
    mapStaffAppointmentsToCalendarEvents,
} from '../../utils/appointments';

const StyledPage = styled(Page)`
    &.isFullscreen {
        .Page-container {
            background-color: #eee;
            padding-top: 24px;
        }
    }

    .Page-container {
        display: flex;
        flex-direction: column;

        .fc-view-harness.fc-view-harness-active {
            height: 100% !important;
        }

        .fc.fc-media-screen {
            display: flex;
            flex-direction: column;
            flex: 1 1 auto;
            height: 100%;
        }

        .toolbarIconButton {
            background-color: #fff;
            margin-left: 24px;
        }
    }
`;

const StyledPaper = styled(Paper)`
    background: #fff;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
    border-radius: 16px;
    flex: 1 1 auto;
    margin-bottom: 24px;
    display: flex;
    flex-direction: column;
    && {
        .fc-timegrid-now-indicator-line {
            border: none;

            &:after {
                content: '';
                position: absolute;
                top: 0;
                width: 100%;
                border-bottom: 1px solid red;
            }

            &:before {
                content: '';
                display: block;
                width: 12px;
                height: 12px;
                border-radius: 7px;
                background-color: red;
                position: absolute;
                top: -6px;
                left: -7px;
            }
        }

        .fc-resourceTimeGridWeek-view {
            .fc-timegrid-now-indicator-line {
                border: none;

                &:after {
                    width: 705%;
                }
            }
        }

        .fc-scrollgrid-section-header .fc-scroller {
            overflow: hidden !important;
        }
        .fc-col-header-cell-cushion {
            text-transform: capitalize;
            color: #111111;
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.005em;
        }

        .fc-col-header {
            background: #eeeeee;
            border-radius: 16px;
            height: 50px;
            align-items: center;
            margin-bottom: 24px;
        }

        .fc-scrollgrid-sync-inner {
            height: 100%;
            justify-content: center;
            align-items: center;
            display: flex;
        }

        .fc-scrollgrid {
            border: 0;
        }

        .fc-col-header-cell-cushion {
            width: 100%;
            display: block;
            padding: 6px 8px;
        }

        th {
            border: 0;
        }

        .fc-timegrid-slot {
            height: 2.5em;
        }

        .fc-timegrid-slot-lane {
            //border-top: 0;
        }

        .fc-timegrid-slot-label-cushion {
            font-weight: 600;
            font-size: 14px;
            line-height: 24px;
            color: #757575;
            letter-spacing: 0.005em;
        }

        .fc-timegrid-slot-minor {
            border: 0;
        }

        .fc-theme-standard > td {
            border-top: 1px solid #e2e2e2;
        }

        .fc-timegrid-slot-label {
            border-top: 0;
            padding-right: 50px;
            vertical-align: baseline;
        }

        .fc-timegrid-slot-label-frame {
            position: relative;
        }

        .fc-timegrid-now-indicator-container {
            background: #fff;
            overflow: unset;
        }

        .fc-timegrid-now-indicator-arrow {
            display: none;
        }

        .fc-header-toolbar {
            display: none;
        }

        .fc-event {
            border-width: 0;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            padding-left: 2px;
            padding-right: 2px;
        }

        .fc-day-today {
            .fc-col-header-cell-cushion {
                & > div {
                    background: #ffffff;
                    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
                    border-radius: 12px;
                    padding-top: 8px;
                    padding-bottom: 8px;
                }
            }
        }
    }

    .fc-license-message.fc-license-message {
        display: none !important;
    }
`;

const StyledDayContent = styled('div')`
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0;
    padding: 0 14px;
    width: 100%;
`;

const DayContent = ({ text = '', date = new Date() }) => (
    <StyledDayContent>
        <Typography variant={'body2'} fontWeight={600}>
            {text}
        </Typography>
        <Typography variant={'caption'} color={'textSecondary'}>
            {moment(date).format('DD MMM')}
        </Typography>
    </StyledDayContent>
);

// TODO fix this hack when the implementation will be available on the backend
const HIDE_CALENDAR_FOR_BUSINESSES = [
    '5694e78d-4725-4914-b3f1-5b5ef46b28a2', // Clinica Optimum
    '6d76ff1b-08d6-4974-bf6e-81a2309f5190', // Bookr test
];

function AppointmentsPage() {
    const { t } = useTranslation('appointmentsPage');
    const { t: commonT } = useTranslation('common');
    const user = useAuthStore((state) => state.user);
    const { data: business, isLoading, isError } = useBusiness();
    const calendarRef = useRef<FullCalendar | null>(null);
    const pageRef = useRef<HTMLDivElement | null>(null);
    const [staffMembers, setStaffMembers] = useState<UserModel[]>([]);
    const { enqueueSnackbar } = useSnackbar();
    const confirm = useConfirmation();
    const handleStaffMemberChanged = useCallback((staff: Nullable<UserModel[]>) => setStaffMembers(staff || []), []);
    const [currentView, setCurrentView] = useState('resourceTimeGridDay');
    const [currentDate, setCurrentDate] = useState(new Date());
    const [isFullscreen, setIsFullscreen] = useState(false);
    const [isExportsDialogOpen, setIsExportsDialogOpen] = useState(false);

    const views = useMemo(
        () => ({
            resourceTimeGridWeek: {
                type: 'resourceTimeGrid',
                buttonText: commonT('week'),
                duration: { days: 7 },
            },
            resourceTimeGridDay: {
                type: 'resourceTimeGrid',
                buttonText: commonT('day'),
                duration: { days: 1 },
            },
        }),
        [commonT],
    );

    function getWorkingHours(staffMember: UserModel) {
        return staffMember.workingHours.map((hour) => {
            const offset = moment(hour.lastUpdatedAt).utcOffset();
            return {
                daysOfWeek: [getWeekDayIndex(hour.day)],
                startTime: moment(hour.start, 'HH:mm').add(offset, 'minutes').format('HH:mm'),
                endTime: moment(hour.end, 'HH:mm').add(offset, 'minutes').format('HH:mm'),
            };
        });
    }

    const handleToggleFullscreen = useCallback(() => setIsFullscreen((prevState) => !prevState), []);
    const handleFullscreen = useCallback((value: boolean) => setIsFullscreen(value), []);
    const handleCloseExportsDialog = useCallback(() => setIsExportsDialogOpen(false), []);

    const handleChangeView = useCallback(() => {
        setCurrentView((prevState) => {
            const nextView = prevState === 'resourceTimeGridWeek' ? 'resourceTimeGridDay' : 'resourceTimeGridWeek';
            const date = moment(currentDate);

            calendarRef.current?.getApi()?.changeView(
                nextView,
                nextView === 'resourceTimeGridDay'
                    ? date.toDate()
                    : {
                          start: moment(date).startOf('week').toDate(),
                          end: moment(date).endOf('week').toDate(),
                      },
            );

            setImmediate(() => {
                calendarRef.current
                    ?.getApi()
                    ?.gotoDate(
                        nextView === 'resourceTimeGridDay' ? date.toDate() : moment(date).startOf('week').toDate(),
                    );
            });

            return nextView;
        });
    }, [currentDate]);

    const handleEvents = useCallback(
        async (fetchInfo: any, successCallback: any) => {
            if (business && business.id) {
                const timestampFrom = moment(fetchInfo.start).startOf('day').valueOf() / 1000;
                const timestampTo = moment(fetchInfo.end).startOf('day').valueOf() / 1000;
                const businessModel = await getBusinessWithAppointments(business.id, timestampFrom, timestampTo);
                const result = businessModel.staffMembers.reduce(
                    (accum, { uid, appointmentsAsStaff }: UserModel) => ({
                        ...accum,
                        [uid]: appointmentsAsStaff,
                    }),
                    {} as Record<string, AppointmentModel[]>,
                );

                let events: EventInput[] = [];

                if (staffMembers.length > 0) {
                    staffMembers.forEach((staffMember) => {
                        events = [
                            ...events,
                            ...mapStaffAppointmentsToCalendarEvents(result[staffMember.uid]),
                            ...mapBreakAppointmentsToCalendarEvents(
                                businessModel.staffMembers.find((s) => s.uid === staffMember.uid)?.breaks ?? [],
                                staffMember.uid,
                            ),
                        ];
                    });
                } else {
                    events = [
                        ...Object.values(result).flatMap((staffAppointments) =>
                            mapStaffAppointmentsToCalendarEvents(staffAppointments),
                        ),
                        ...businessModel.staffMembers.flatMap((staff) =>
                            mapBreakAppointmentsToCalendarEvents(staff.breaks, staff.uid),
                        ),
                    ];
                }

                events = events.flat();

                successCallback?.(events);

                return events;
            }

            return [];
        },
        [business, staffMembers],
    );

    const refetchEvents = useCallback(() => calendarRef.current?.getApi()?.refetchEvents(), [calendarRef]);

    useIntervalAsync(refetchEvents, 1000 * 60); // 1 minute polling for new events

    const handleDateChanged = useCallback(
        (date: Moment) => {
            setCurrentDate(date.toDate());

            calendarRef.current
                ?.getApi()
                ?.gotoDate(currentView === 'resourceTimeGridWeek' ? date.startOf('week').toDate() : date.toDate());
        },
        [currentView],
    );

    const handleLoadingCalendarData = useCallback(
        (isLoading: boolean) => emitCustomEvent(CustomEvent.SHOW_HIDE_PAGE_LOADER, isLoading),
        [],
    );

    const handlePrintAppointments = useCallback(() => setIsExportsDialogOpen(true), []);

    const handleEventEdit = useCallback(
        async ({ event, revert }: any) => {
            const eventId = event.id;
            const timestamp = moment(event.start).tz('UTC').unix();
            const { isBreak, appointment } = event.extendedProps;

            if (isBreak) {
                revert();
                return;
            }

            const clientName = appointment.client.displayName;

            const result = await confirm({
                title: t('moveAppointment'),
                message: t('moveAppointmentMessage', {
                    clientName,
                    dateTime: moment(event.start).format('DD MMM YYYY, HH:mm'),
                }),
                cancelText: commonT('cancel'),
                submitText: t('moveAppointmentButton'),
            });

            if (result) {
                try {
                    const data = await appointmentsEndpoint.isTimeslotFree(
                        appointment.staff.uid,
                        timestamp,
                        appointment.service.id,
                    );

                    if (data.free) {
                        await appointmentsEndpoint.update(eventId, { timestamp });
                    } else {
                        setTimeout(async () => {
                            const { appointment } = data;
                            const result = await confirm({
                                title: commonT('slotExpired'),
                                message: commonT('slotExpiredMessage', {
                                    start: moment(appointment.dateTime).format('HH:mm'),
                                    end: moment(appointment.dateTime)
                                        .add(appointment.service.duration, 'minutes')
                                        .format('HH:mm'),
                                    displayName: appointment.client.displayName,
                                }),
                            });

                            if (result) {
                                try {
                                    await appointmentsEndpoint.update(eventId, { timestamp });
                                } catch (e) {
                                    enqueueSnackbar(t('appointmentUpdateError'), { variant: 'error' });
                                }
                                refetchEvents();
                            }
                        }, 200);
                    }
                } catch (e) {
                    enqueueSnackbar(t('appointmentUpdateError'), { variant: 'error' });
                }
            }

            refetchEvents();
        },
        [commonT, confirm, enqueueSnackbar, refetchEvents, t],
    );

    const handleDateClick = useCallback((info: DateClickArg) => {
        if (!info.resource) {
            return;
        }
        const timestamp = moment(info.date).tz('UTC').unix() * 1000;
        emitCustomEvent(CustomEvent.HIDE_SHOW_NEW_APPOINTMENT_DIALOG, { timestamp, staffId: info.resource.id });
    }, []);

    useCustomEventListener(CustomEvent.REFETCH_CALENDAR_EVENTS, () => refetchEvents());

    if (isLoading || isError) {
        return null;
    }

    const shouldNotDisplayAllStaffMembers =
        HIDE_CALENDAR_FOR_BUSINESSES.includes(business?.id || '') && user?.accountType !== AccountType.BusinessOwner;

    return (
        <StyledPage
            title={t('title')}
            className={classNames({ isFullscreen })}
            ref={pageRef}
            fullscreen={isFullscreen}
            onFullscreen={handleFullscreen}
            fluid
        >
            <Grid container mb={2} alignItems={'center'}>
                <Grid item xs>
                    {!shouldNotDisplayAllStaffMembers && (
                        <MultiStaffMemberSelect multiple onStaffMemberChanged={handleStaffMemberChanged} />
                    )}
                </Grid>
                <Grid item xs container alignItems={'center'} justifyContent={'flex-end'}>
                    <WeekPicker isWeek={currentView === 'resourceTimeGridWeek'} onDateChanged={handleDateChanged} />

                    <Tooltip title={t<string>('exportAppointments')}>
                        <IconButton onClick={handlePrintAppointments} className={'toolbarIconButton'} size={'large'}>
                            <PrintIcon />
                        </IconButton>
                    </Tooltip>

                    <Tooltip title={t<string>('changeView')}>
                        <IconButton onClick={handleChangeView} className={'toolbarIconButton'} size={'large'}>
                            {currentView === 'resourceTimeGridWeek' ? <ViewDayIcon /> : <ViewWeekIcon />}
                        </IconButton>
                    </Tooltip>
                    <Tooltip title={isFullscreen ? t<string>('fullScreenReturn') : t<string>('fullScreen')}>
                        <IconButton onClick={handleToggleFullscreen} className={'toolbarIconButton'} size={'large'}>
                            {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                        </IconButton>
                    </Tooltip>
                </Grid>
            </Grid>
            <StyledPaper sx={{ padding: 3 }}>
                <FullCalendar
                    ref={calendarRef}
                    loading={handleLoadingCalendarData}
                    locale={user?.language || 'en-EN'}
                    plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, resourceTimeGridPlugin]}
                    firstDay={1}
                    // headerToolbar={{ center: 'resourceTimeGridWeek,resourceTimeGridDay' }}
                    initialView="resourceTimeGridDay"
                    views={views}
                    eventTextColor={'#fff'}
                    slotDuration={'00:30:00'}
                    scrollTime={moment().subtract(1, 'h').format('HH:mm')}
                    slotLabelInterval={{ minutes: 30 }}
                    dateClick={handleDateClick}
                    events={handleEvents}
                    eventContent={(e) => <CalendarEventContent event={e.event} refetchEvents={refetchEvents} />}
                    eventTimeFormat={calendarOptions.eventTimeFormat}
                    dayHeaderFormat={calendarOptions.dayHeaderFormat}
                    dayHeaderContent={(hookProps) => <DayContent text={hookProps.text} date={hookProps.date} />}
                    titleFormat={calendarOptions.titleFormat}
                    slotLabelFormat={calendarOptions.slotLabelFormat}
                    allDaySlot={false}
                    eventDrop={handleEventEdit}
                    eventDurationEditable={false}
                    eventResourceEditable={false}
                    resourceLabelContent={({ resource }) => {
                        const user = business?.staffMembers?.find((u) => u.uid === resource.id);
                        return (
                            <Grid
                                container
                                direction={'row'}
                                alignItems={'center'}
                                justifyContent={'center'}
                                flexWrap={'wrap'}
                            >
                                <UserProfile src={user?.photoURL} alt={user?.displayName} size={24} />
                                {currentView !== 'resourceTimeGridWeek' ? (
                                    <Typography
                                        variant="caption"
                                        fontWeight={500}
                                        ml={1}
                                        maxWidth={'100%'}
                                        align={'left'}
                                        sx={{ whiteSpace: 'wrap', flexGrow: 1 }}
                                    >
                                        {user?.displayName ?? 'N/A'}
                                    </Typography>
                                ) : null}
                            </Grid>
                        );
                    }}
                    resources={(staffMembers.length > 0 ? staffMembers : business?.staffMembers)
                        ?.filter((staff) => (shouldNotDisplayAllStaffMembers ? staff.uid === user?.uid : true))
                        .map((staff) => ({
                            id: staff.uid,
                            title: staff.displayName,
                            businessHours: getWorkingHours(staff),
                        }))}
                    nowIndicator
                    droppable
                    editable
                    datesAboveResources
                />
            </StyledPaper>

            <ExportsDialog
                open={isExportsDialogOpen}
                onClose={handleCloseExportsDialog}
                documents={(business?.staffMembers ?? []).map((staff) => ({
                    path: 'exports/appointments',
                    params: {
                        uid: staff.uid,
                        date: moment().format('YYYY-MM-DD'),
                    },
                }))}
            />
        </StyledPage>
    );
}

export default AppointmentsPage;

const calendarOptions: Record<string, FormatterInput> = {
    titleFormat: {
        day: '2-digit',
        month: 'long',
    },
    slotLabelFormat: {
        hour: '2-digit',
        minute: '2-digit',
    },
    eventTimeFormat: {
        hour: 'numeric',
        minute: 'numeric',
    },
    dayHeaderFormat: { weekday: 'long', omitCommas: true },
};
