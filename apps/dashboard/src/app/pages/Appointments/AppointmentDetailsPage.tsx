import Videocam from '@mui/icons-material/Videocam';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { useBusinessClient } from '@bookr-technologies/store/hooks/useBusinessClients';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import AppointmentDetails from '../../components/AppointmentDetails/AppointmentDetails';
import ClientCard from '../../components/ClientCard/ClientCard';
import { Page } from '../../components/Page';
import { getAppointmentById } from '../../utils/appointments';
import { ClientTabs } from '../Clients/ClientTabs';

const StyledPaper = styled(Paper)({
    width: '100%',
    overflowY: 'auto',
    background: '#FFFFFF',
    boxShadow: '0px 2px 6px rgba(5, 16, 55, 0.1)',
    borderRadius: '16px',
});

const StyledJoinLink = styled('a')({ display: 'block', width: '100%', textDecoration: 'none' });

function AppointmentDetailsPage() {
    const { t } = useTranslation('clientPage');
    const { t: common } = useTranslation('common');
    const { appointmentId } = useParams();
    const [appointment, setAppointment] = useState<AppointmentModel | null>(null);

    const appId = appointmentId && Number.parseInt(appointmentId);

    const fetchAppointment = useCallback(async () => {
        if (!appId) {
            // TODO redirect me to 404
            setAppointment(null);
            return;
        }

        const data: AppointmentModel = await getAppointmentById(appId);
        if (data) {
            setAppointment(data);
        }
    }, [appId]);

    useEffect(() => {
        // noinspection JSIgnoredPromiseFromCall
        fetchAppointment();
    }, [appId, fetchAppointment]);

    const handleNotesChanges = useCallback(async () => await fetchAppointment(), [fetchAppointment]);

    const { data: clientData, isLoading, isError } = useBusinessClient(appointment?.client.uid);

    if (isLoading || isError || !clientData || !appointment) {
        return <PageLoader />;
    }

    const { client, stats } = clientData;

    return (
        <Page backToLabel={t('backToClients')} backToHref={`/clients/${client.uid}`}>
            <Grid container spacing={2} alignItems={'flex-start'} minHeight={'100%'}>
                <Grid item xs={5}>
                    <Grid>
                        <StyledPaper sx={{ padding: 3 }}>
                            <ClientCard client={client} stats={stats} />
                        </StyledPaper>

                        {appointment.onlineEvent?.joinUrl && (
                            <StyledJoinLink
                                href={appointment.onlineEvent?.joinUrl}
                                target={'_blank'}
                                rel={'noopener noreferrer'}
                            >
                                <StyledPaper
                                    sx={{
                                        marginTop: 3,
                                        padding: '24px',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                    }}
                                >
                                    <Typography variant={'body1'} fontWeight={700}>
                                        {common('joinOnlineEvent')}
                                    </Typography>

                                    <Videocam color={'accent'} />
                                </StyledPaper>
                            </StyledJoinLink>
                        )}
                    </Grid>

                    <Grid mt={2}>
                        <ClientTabs clientId={client.uid} defaultActiveTab={2} />
                    </Grid>
                </Grid>

                <Grid item xs={7} container sx={{ minHeight: '75vh' }}>
                    <StyledPaper sx={{ p: 5 }}>
                        <AppointmentDetails data={appointment} onChange={() => handleNotesChanges()} />
                    </StyledPaper>
                </Grid>
            </Grid>
        </Page>
    );
}

export default AppointmentDetailsPage;
