import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { RecaptchaVerifier as FirebaseRecaptchaVerifier } from 'firebase/auth';
import { Form, Formik, FormikProps } from 'formik';
import { useSnackbar } from 'notistack';
import { Fragment, KeyboardEvent, MouseEvent, useCallback, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate, useNavigate } from 'react-router-dom';
import { useAuthStore } from '@bookr-technologies/store';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { RecaptchaVerifier } from '@bookr-technologies/ui/RecaptchaVerifier';
import { useLocationState } from '@bookr-technologies/ui/hooks/useLocationState';
import authSideImage from '../../assets/authSideImage.png';
import { AuthLayout } from '../components/Layout/AuthLayout';
import { verifyPhoneNumber } from '../utils/auth';

const StyledForm = styled(Form)(({ theme }) => ({
    display: 'flex',
    flexDirection: 'column',
    flex: '1 1 auto',
    '.codeInput': {
        '.MuiOutlinedInput-root': { padding: '10px' },
        '.MuiOutlinedInput-input': {
            fontSize: 18,
            fontWeight: 500,
            textAlign: 'center',
        },
    },

    '.notReceivedMessage': {
        fontSize: 18,
        fontWeight: 500,
        color: '#afafaf',
        marginTop: 28,
        '.MuiLink-root': {
            color: theme.palette.accent.main,
        },
    },
}));

function SignInPage() {
    const locationState = useLocationState();
    const navigate = useNavigate();
    const { enqueueSnackbar } = useSnackbar();
    const { t } = useTranslation('authConfirmationPage');
    const { t: common } = useTranslation('common');
    const formikRef = useRef<FormikProps<typeof initialValues> | null>(null);
    const reCaptchaRef = useRef<FirebaseRecaptchaVerifier | null>(null);
    const confirmationResult = useAuthStore((state) => state.confirmationResult);
    const setConfirmationResult = useAuthStore((state) => state.setConfirmationResult);

    const initialValues = useMemo(
        () => ({
            code: ['', '', '', '', '', ''],
        }),
        [],
    );

    const handlePaste = useCallback((e: any) => {
        e.preventDefault();

        const text = e.clipboardData.getData('text/plain') ?? '';
        const code = text
            .replace(/[\s\D]/g, '')
            .split('')
            .slice(0, 6);

        if (code.length < 6) {
            for (let i = code.length; i < 6; i++) {
                code.push('');
            }
        }

        formikRef.current?.setFieldValue('code', code);
    }, []);

    const handleKeyDown = useCallback((e: KeyboardEvent<HTMLInputElement>) => {
        if (e.key === ' ' || e.key.match(/\d/)) {
            e.preventDefault();
        }
    }, []);

    const handleKeyUp = useCallback(
        (index: number) => (e: KeyboardEvent<HTMLInputElement>) => {
            e.preventDefault();
            const previous = document.querySelector<HTMLInputElement>(`input[name="code.${index - 1}"]`);
            const next = document.querySelector<HTMLInputElement>(`input[name="code.${index + 1}"]`);
            const code = formikRef.current?.values.code ?? [];
            const key = e.key.toLowerCase();

            const navigatePrevious = () => previous && previous.focus();
            const navigateNext = () => next && next.focus();

            if (key === 'backspace') {
                code[index] === '' && navigatePrevious();
                code[index] = '';
            } else if (key === 'delete') {
                navigateNext();
                code[index + 1] = '';
            } else if (key >= '0' && key <= '9') {
                navigateNext();
                code[index] = key;
            } else if (key === 'arrowleft') {
                navigatePrevious();
            } else if (key === 'arrowright') {
                navigateNext();
            }

            formikRef.current?.setFieldValue('code', code);

            if (code.filter(Boolean).length === code.length && !formikRef.current?.isSubmitting) {
                formikRef.current?.submitForm();
            }
        },
        [],
    );

    const handleSubmit = useCallback(
        async ({ code }: any) => {
            const { postSignInUrl } = locationState ?? {};
            const verificationCode = code.join('');

            try {
                await confirmationResult?.confirm(verificationCode);
                navigate(postSignInUrl ?? '/');
                setConfirmationResult(null);
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            } catch (e: any) {
                enqueueSnackbar(e.message, { variant: 'error' });
            }
        },
        [confirmationResult, enqueueSnackbar, locationState, navigate, setConfirmationResult],
    );

    const handleSendCode = useCallback(
        async (e?: MouseEvent) => {
            e?.preventDefault();
            if (reCaptchaRef.current) {
                try {
                    await verifyPhoneNumber(reCaptchaRef.current, locationState);
                    enqueueSnackbar(t('codeSent'), { variant: 'success' });
                } catch (e) {
                    enqueueSnackbar((e as Error).message, { variant: 'error' });
                }
            } else {
                enqueueSnackbar(t('verificationCodeNotSent'), { variant: 'error' });
            }
        },
        [enqueueSnackbar, locationState, t],
    );

    if (!confirmationResult) {
        return <Navigate to={'/auth/sign-in'} replace />;
    }

    return (
        <AuthLayout image={authSideImage} title={t('title')} description={t('subtitle')} backTo={'/auth/sign-in'}>
            <Formik initialValues={initialValues} onSubmit={handleSubmit} innerRef={formikRef}>
                {({ values }) => (
                    <StyledForm>
                        <RecaptchaVerifier innerRef={reCaptchaRef} />

                        <Stack direction={'row'} spacing={1} alignItems={'center'}>
                            {values.code.map((value, index) => (
                                <Fragment key={index}>
                                    {index > 0 && index % 3 === 0 && (
                                        <Grid item width={24} justifyContent={'center'}>
                                            <Typography variant={'body1'} sx={{ fontWeight: 500 }} align={'center'}>
                                                -
                                            </Typography>
                                        </Grid>
                                    )}
                                    <Grid item maxWidth={'50px !important'}>
                                        <FormikTextField
                                            name={`code.${index}`}
                                            autoFocus={index === 0}
                                            inputProps={{ maxLength: 1 }}
                                            className={'codeInput'}
                                            autoComplete={'off'}
                                            onPaste={handlePaste}
                                            onKeyDown={handleKeyDown}
                                            onKeyUp={handleKeyUp(index)}
                                            fullWidth
                                            required
                                        />
                                    </Grid>
                                </Fragment>
                            ))}
                        </Stack>

                        <Typography color={'textSecondary'} className={'notReceivedMessage'}>
                            {t('notReceivedMessage')} &nbsp;
                            <Link href={'#'} onClick={handleSendCode}>
                                {t('sendCodeAgainMessage')}
                            </Link>
                        </Typography>

                        <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                            <FormikButton size={'large'} variant={'contained'} color={'primary'} disableElevation>
                                {common('continueButton')}
                            </FormikButton>
                        </Stack>
                    </StyledForm>
                )}
            </Formik>
        </AuthLayout>
    );
}

export default SignInPage;
