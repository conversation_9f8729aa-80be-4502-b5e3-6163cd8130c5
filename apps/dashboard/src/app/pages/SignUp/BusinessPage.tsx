import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { Formik } from 'formik';
import { useSnackbar } from 'notistack';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation, useQuery } from 'react-query';
import { Navigate, useNavigate } from 'react-router-dom';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useAuthStore } from '@bookr-technologies/store';
import { CountrySelectVariant, FormikCountrySelect } from '@bookr-technologies/ui/CountrySelect';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import authSideImage from '../../../assets/authSideImage.png';
import { AuthLayout } from '../../components/Layout/AuthLayout';
import { StyledSignUpBusinessForm } from '../../components/StyledSignUpBusinessForm';
import { useSignUpBusiness } from '../../hooks/useSignUpBusiness';
import { signUpBusinessSchema } from '../../validators/signUpBusinessValidators';

function BusinessPage() {
    const { t } = useTranslation('signUpBusinessPage');
    const { t: common } = useTranslation('common');
    const { enqueueSnackbar } = useSnackbar();
    const resolveUser = useAuthStore((state) => state.resolveUser);

    const { data, isLoading } = useQuery(`business:amIAnEmployee`, () => businessEndpoint.amIAnEmployee());

    const joinBusinessMutation = useMutation(() => businessEndpoint.joinBusiness());

    const navigate = useNavigate();

    const { handleSubmit, initialState, nextPath, checking } = useSignUpBusiness({
        redirectTo: '/auth/sign-up/business/type',
    });

    useEffect(() => {
        if (joinBusinessMutation.isSuccess) {
            resolveUser().then(() => {
                navigate('/appointments');
            });
        } else if (joinBusinessMutation.isError) {
            enqueueSnackbar(getErrorMessage(joinBusinessMutation.error), { variant: 'error' });
        } else if (!isLoading && data && !joinBusinessMutation.isLoading) {
            joinBusinessMutation.mutate();
        }
    }, [isLoading, data, navigate, joinBusinessMutation, enqueueSnackbar, resolveUser]);

    if (isLoading || (!isLoading && data)) {
        return <PageLoader />;
    }

    if (nextPath) {
        return <Navigate to={nextPath} replace />;
    }

    return (
        <AuthLayout
            image={authSideImage}
            title={t('title')}
            description={t('subtitle')}
            backTo={'/auth/logout'}
            loading={checking}
        >
            <Formik
                initialValues={initialState}
                onSubmit={handleSubmit}
                validationSchema={signUpBusinessSchema.mainPage}
                validateOnMount
                validateOnChange
                validateOnBlur
            >
                {() => (
                    <StyledSignUpBusinessForm>
                        <Stack
                            spacing={2.5}
                            alignItems={'flex-start'}
                            justifyContent={'flex-start'}
                            maxWidth={'480px !important'}
                        >
                            <FormikTextField name={'businessName'} label={t('businessNameField')} fullWidth />
                            <Stack direction={'row'} spacing={2.5} width={'100%'}>
                                <Grid item xs maxWidth={'146px !important'} pl={0}>
                                    <FormikCountrySelect
                                        name={'callingCode'}
                                        variant={CountrySelectVariant.CallingCode}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid item xs>
                                    <FormikTextField name={'phoneNumber'} label={t('phoneField')} fullWidth />
                                </Grid>
                            </Stack>
                        </Stack>

                        <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                            <FormikButton size={'large'} variant={'contained'} color={'primary'} disableElevation>
                                {common('continueButton')}
                            </FormikButton>
                        </Stack>
                    </StyledSignUpBusinessForm>
                )}
            </Formik>
        </AuthLayout>
    );
}

export default BusinessPage;
