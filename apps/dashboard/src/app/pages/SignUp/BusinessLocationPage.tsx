import Stack from '@mui/material/Stack';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { FormikButton, FormikLocationMap } from '@bookr-technologies/ui/Fields';
import { FormikLocationName } from '@bookr-technologies/ui/Fields/FormikLocationName';
import authSideImage from '../../../assets/authSideImage.png';
import { AuthLayout } from '../../components/Layout/AuthLayout';
import { StyledSignUpBusinessForm } from '../../components/StyledSignUpBusinessForm';
import { useSignUpBusiness } from '../../hooks/useSignUpBusiness';
import { signUpBusinessSchema } from '../../validators/signUpBusinessValidators';

function SignUpBusinessTypePage() {
    const { t } = useTranslation('signUpBusinessLocationPage');
    const { t: common } = useTranslation('common');

    const { handleSubmit, initialState, nextPath, checking } = useSignUpBusiness({
        redirectTo: '/auth/sign-up/business/program',
    });

    if (nextPath) {
        return <Navigate to={nextPath} replace />;
    }

    return (
        <Formik
            initialValues={initialState}
            onSubmit={handleSubmit}
            validationSchema={signUpBusinessSchema.locationPage}
            validateOnMount
            validateOnChange
            validateOnBlur
        >
            {() => (
                <AuthLayout
                    image={authSideImage}
                    title={t('title')}
                    description={t('subtitle')}
                    backTo={'/auth/sign-up/business/type'}
                    rightSideElement={<FormikLocationMap name={'location'} autoDetect />}
                    sx={styles.root}
                    loading={checking}
                >
                    <StyledSignUpBusinessForm>
                        <Stack
                            spacing={2.5}
                            alignItems={'flex-start'}
                            justifyContent={'flex-start'}
                            maxWidth={'480px !important'}
                        >
                            <FormikLocationName
                                name={'locationName'}
                                label={t('addressField')}
                                locationName={'location'}
                                fullWidth
                            />
                        </Stack>
                        <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                            <FormikButton size={'large'} variant={'contained'} color={'primary'} disableElevation>
                                {common('continueButton')}
                            </FormikButton>
                        </Stack>
                    </StyledSignUpBusinessForm>
                </AuthLayout>
            )}
        </Formik>
    );
}

export default SignUpBusinessTypePage;

const styles = {
    root: {
        '.authLayout__side': {
            width: 648,
        },
    },
};
