import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import FormControl from '@mui/material/FormControl';
import FormGroup from '@mui/material/FormGroup';
import FormHelperText from '@mui/material/FormHelperText';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { Navigate } from 'react-router-dom';
import { categoriesEndpoint } from '@bookr-technologies/api/endpoints/categoriesEndpoint';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import { FormikCheckbox } from '@bookr-technologies/ui/Fields/FormikCheckbox';
import authSideImage from '../../../assets/authSideImage.png';
import { AuthLayout } from '../../components/Layout/AuthLayout';
import { StyledSignUpBusinessForm } from '../../components/StyledSignUpBusinessForm';
import { useSignUpBusiness } from '../../hooks/useSignUpBusiness';
import { signUpBusinessSchema } from '../../validators/signUpBusinessValidators';

function BusinessTypePage() {
    const { t } = useTranslation('signUpBusinessTypePage');
    const { t: common } = useTranslation('common');

    const { handleSubmit, initialState, nextPath, checking } = useSignUpBusiness({
        redirectTo: '/auth/sign-up/business/location',
    });

    const categories = useQuery('availableCategories', () => categoriesEndpoint.fetchAvailableCategories());

    if (nextPath) {
        return <Navigate to={nextPath} replace />;
    }

    return (
        <AuthLayout
            image={authSideImage}
            title={t('title')}
            description={t('subtitle')}
            backTo={'/auth/sign-up/business'}
            loading={checking}
        >
            <Formik
                initialValues={initialState}
                onSubmit={handleSubmit}
                validationSchema={signUpBusinessSchema.categoriesPage}
                validateOnMount
                validateOnChange
                validateOnBlur
            >
                {() => (
                    <StyledSignUpBusinessForm>
                        <Stack
                            spacing={2.5}
                            alignItems={'flex-start'}
                            justifyContent={'flex-start'}
                            maxWidth={'480px !important'}
                        >
                            <StyledFormControl variant={'standard'}>
                                <Typography color={'textSecondary'} variant={'subtitle1'} className={'font-medium'}>
                                    {t('chooseBusinessDomain')}
                                </Typography>
                                <FormGroup>
                                    {categories.isLoading ? (
                                        <Box p={2}>
                                            <CircularProgress />
                                        </Box>
                                    ) : null}
                                    {categories.data?.map(({ code }) => (
                                        <FormikCheckbox
                                            key={code}
                                            name={'categories'}
                                            value={code}
                                            label={common(`categories.${code.toLowerCase()}`)}
                                            color={'accent'}
                                        />
                                    ))}
                                </FormGroup>
                                <FormHelperText>{t('selectCategoryHelperText')}</FormHelperText>
                            </StyledFormControl>
                        </Stack>
                        <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                            <FormikButton size={'large'} variant={'contained'} color={'primary'} disableElevation>
                                {common('continueButton')}
                            </FormikButton>
                        </Stack>
                    </StyledSignUpBusinessForm>
                )}
            </Formik>
        </AuthLayout>
    );
}

export default BusinessTypePage;
export const StyledFormControl = styled(FormControl)({
    '.MuiFormGroup-root': {
        marginTop: 8,
        boxShadow: '0 11px 20px rgba(5, 16, 55, 0.1)',
        borderRadius: 16,
        padding: '10px 28px',
        maxWidth: 420,
        maxHeight: 320,
        flexWrap: 'nowrap',
        overflowY: 'auto',
    },
    '.MuiFormControlLabel-label, .font-medium': {
        fontWeight: 500,
    },
    '.MuiFormHelperText-root': {
        marginTop: 8,
    },
});
