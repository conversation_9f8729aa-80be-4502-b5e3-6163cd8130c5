import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { createUserWithEmailAndPassword, sendEmailVerification } from 'firebase/auth';
import { Form, Formik } from 'formik';
import { useSnackbar } from 'notistack';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate, useNavigate } from 'react-router-dom';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { DEFAULT_USER_PHOTO_URL } from '@bookr-technologies/api/constants/UserDefaults';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { DEFAULT_CALLING_CODE } from '@bookr-technologies/core/constants';
import { FirebaseService } from '@bookr-technologies/core/services';
import { AvailableLanguages } from '@bookr-technologies/i18n';
import { useAuthStore } from '@bookr-technologies/store';
import { useUser } from '@bookr-technologies/store/hooks/useUser';
import { CountrySelectVariant, FormikCountrySelect } from '@bookr-technologies/ui/CountrySelect';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { useLocationState } from '@bookr-technologies/ui/hooks/useLocationState';
import authSideImage from '../../../assets/authSideImage.png';
import { AuthLayout } from '../../components/Layout/AuthLayout';
import { signUpUserSchema } from '../../validators/signUpUserValidators';

const StyledForm = styled(Form)({
    display: 'flex',
    flexDirection: 'column',
    flex: '1 1 auto',
});

type FormValues = {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    callingCode: string;
    phoneNumber: string;
    language: string;
};

function SignUpPage() {
    const { t } = useTranslation('signUpPage');
    const { t: common } = useTranslation('common');
    const user = useUser();
    const authenticated = useAuthStore((state) => state.authenticated);
    const navigate = useNavigate();
    const { enqueueSnackbar } = useSnackbar();
    const locationState = useLocationState();
    const firebaseUser = FirebaseService.auth().currentUser;
    const initialState = useMemo<FormValues>(
        () => ({
            firstName: '',
            lastName: '',
            password: '',
            passwordConfirmation: '',
            email: locationState.email ?? firebaseUser?.email ?? '',
            language:
                !user?.language || user.language === 'invalid'
                    ? AvailableLanguages.find((a) => a.code === navigator.language)?.code ?? 'en-EN'
                    : user.language,
            callingCode: DEFAULT_CALLING_CODE,
            phoneNumber:
                !user?.phoneNumber || user.phoneNumber === 'invalid'
                    ? firebaseUser?.phoneNumber ?? ''
                    : user.phoneNumber,
        }),
        [user, locationState.email, firebaseUser],
    );

    const handleSubmit = useCallback(
        async (values: FormValues) => {
            try {
                const user = await createUserWithEmailAndPassword(
                    FirebaseService.auth(),
                    values.email,
                    values.password,
                );
                await usersEndpoint.create({
                    uid: user.user?.uid,
                    displayName: `${values.firstName ?? ''} ${values.lastName ?? ''}`.trim(),
                    email: values.email,
                    language: values.language,
                    phoneNumber: `${values.callingCode}${values.phoneNumber}`,
                    photoURL: DEFAULT_USER_PHOTO_URL,
                    accountType: AccountType.BusinessOwner,
                });

                // TODO: add in app verification
                await sendEmailVerification(user.user);

                navigate('/auth/sign-up/verification', {
                    state: {
                        postSignInUrl: locationState?.postSignInUrl,
                    },
                });
            } catch (e) {
                const message = getErrorMessage(e, 'errorCreatingAccount');
                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [enqueueSnackbar, locationState?.postSignInUrl, navigate, t],
    );

    if (authenticated === null && !user) {
        return null;
    }

    if (authenticated && user && user.accountType !== AccountType.Invalid) {
        return <Navigate to={locationState?.postSignInUrl ?? '/'} replace />;
    }

    return (
        <AuthLayout image={authSideImage} title={t('title')} description={t('subtitle')} backTo={'/auth/logout'}>
            <Formik
                initialValues={initialState}
                onSubmit={handleSubmit}
                validationSchema={signUpUserSchema}
                enableReinitialize
                validateOnMount
                validateOnChange
                validateOnBlur
            >
                {() => (
                    <StyledForm>
                        <Stack
                            spacing={2.5}
                            alignItems={'flex-start'}
                            justifyContent={'flex-start'}
                            maxWidth={'480px !important'}
                        >
                            <FormikTextField name={'email'} label={t('emailField')} fullWidth />
                            <FormikTextField name={'firstName'} label={t('firstNameField')} fullWidth />
                            <FormikTextField name={'lastName'} label={t('lastNameField')} fullWidth />
                            <Grid container>
                                <Grid item xs maxWidth={'140px !important'}>
                                    <FormikCountrySelect
                                        name={'callingCode'}
                                        label={common('callingCodeField')}
                                        variant={CountrySelectVariant.CallingCode}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid item xs flexGrow={1} pl={2.5}>
                                    <FormikTextField
                                        name={'phoneNumber'}
                                        label={common('phoneNumberField')}
                                        fullWidth
                                    />
                                </Grid>
                            </Grid>

                            <FormikTextField type={'password'} name={'password'} label={t('passwordField')} fullWidth />

                            <FormikTextField
                                type={'password'}
                                name={'passwordConfirmation'}
                                label={t('passwordConfirmationField')}
                                fullWidth
                            />

                            <Box width={260}>
                                <FormikCountrySelect
                                    name={'language'}
                                    variant={CountrySelectVariant.Language}
                                    fullWidth
                                />
                            </Box>
                        </Stack>
                        <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                            <FormikButton size={'large'} variant={'contained'} color={'primary'} disableElevation>
                                {common('continueButton')}
                            </FormikButton>
                        </Stack>
                    </StyledForm>
                )}
            </Formik>
        </AuthLayout>
    );
}

export default SignUpPage;
