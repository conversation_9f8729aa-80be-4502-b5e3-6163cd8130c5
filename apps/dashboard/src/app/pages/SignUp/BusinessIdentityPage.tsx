/* eslint-disable @typescript-eslint/no-non-null-assertion */
import LoadingButton from '@mui/lab/LoadingButton';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { AxiosError } from 'axios';
import { useSnackbar } from 'notistack';
import { ChangeEvent, useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate, useNavigate } from 'react-router-dom';
import { useAuthStore } from '@bookr-technologies/store';
import { useBusinessStore } from '@bookr-technologies/store/businessStore';
import { useLocationState } from '@bookr-technologies/ui/hooks/useLocationState';
import authSideImage from '../../../assets/authSideImage.png';
import { AuthLayout } from '../../components/Layout/AuthLayout';
import { StyledSignUpBusinessForm } from '../../components/StyledSignUpBusinessForm';
import { useSignUpBusiness } from '../../hooks/useSignUpBusiness';

const StyledForm = styled(StyledSignUpBusinessForm)({
    '.avatar': {
        width: 134,
        height: 134,
        marginBottom: 18,
    },
    '.upload-button': {
        borderRadius: 10,
    },
    '.skip-button': {
        marginLeft: 34,
        opacity: 0.7,
        textTransform: 'unset',
        paddingTop: 18,
        paddingBottom: 18,
        fontSize: 18,
        fontWeight: 500,
        borderRadius: 20,
        minWidth: 200,
    },
    '.upload-field': {
        display: 'none',
    },
});

function SignUpBusinessProgramPage() {
    const { t } = useTranslation('signUpBusinessIdentityPage');
    const locationState = useLocationState();
    const navigate = useNavigate();
    const { nextPath, checking } = useSignUpBusiness();

    const resolveUser = useAuthStore((state) => state.resolveUser);
    const createBusiness = useBusinessStore((state) => state.createBusiness);
    const attachProfilePicture = useBusinessStore((state) => state.attachProfilePicture);
    const { enqueueSnackbar } = useSnackbar();
    const [file, setFile] = useState<File | null>(null);
    const [imagePreview, setImagePreview] = useState<string>();
    const [loading, setLoading] = useState<boolean>(false);
    const fieldRef = useRef<HTMLInputElement>(null);

    const handleChangeFile = useCallback(async (event: ChangeEvent<HTMLInputElement>) => {
        const selectedFile = event.target.files?.[0] ?? null;
        if (selectedFile) {
            setImagePreview(URL.createObjectURL(selectedFile));
            setFile(selectedFile);
            event.target.value = '';
        }
    }, []);

    const handleClickUpload = useCallback(async () => {
        fieldRef.current?.click();
    }, []);

    const handleCreateBusiness = useCallback(async () => {
        setLoading(true);
        try {
            await createBusiness();
            await resolveUser();
            enqueueSnackbar(t('businessCreated'), { variant: 'success' });
            setLoading(false);
        } catch (e) {
            const { response, message } = e as AxiosError<any>;
            enqueueSnackbar(response?.data?.message ?? message, { variant: 'error' });
            setLoading(false);
            return false;
        }

        return true;
    }, [createBusiness, enqueueSnackbar, resolveUser, t]);

    const handleSkip = useCallback(async () => {
        const canContinue = await handleCreateBusiness();
        if (canContinue) {
            const nextPath = locationState?.postSignInUrl ?? '/';
            navigate(nextPath);
        }
    }, [handleCreateBusiness, locationState?.postSignInUrl, navigate]);

    const handleSubmit = useCallback(async () => {
        const canContinue = await handleCreateBusiness();
        if (!canContinue) {
            return;
        }

        try {
            if (file) {
                await attachProfilePicture(file);
            }
        } catch (e) {
            const { response, message } = e as AxiosError<any>;
            enqueueSnackbar(response?.data?.message ?? message, { variant: 'error' });
        }

        const nextPath = locationState?.postSignInUrl ?? '/';
        navigate(nextPath);
    }, [attachProfilePicture, enqueueSnackbar, file, handleCreateBusiness, locationState?.postSignInUrl, navigate]);

    if (nextPath) {
        return <Navigate to={nextPath} replace />;
    }

    return (
        <AuthLayout
            image={authSideImage}
            title={t('title')}
            description={t('subtitle')}
            backTo={'/auth/sign-up/business/program'}
            loading={checking}
        >
            <StyledForm as={'div'}>
                <Stack spacing={2.5} alignItems={'flex-start'} justifyContent={'flex-start'}>
                    <Grid item container alignItems={'center'} direction={'column'} maxWidth={'184px !important'}>
                        <Avatar src={imagePreview} className={'avatar'} />
                        <input type="file" onChange={handleChangeFile} className={'upload-field'} ref={fieldRef} />
                        <Button
                            variant={'contained'}
                            color={'primary'}
                            size={'small'}
                            fullWidth
                            disableElevation
                            className={'upload-button'}
                            onClick={handleClickUpload}
                        >
                            {t('uploadPhoto')}
                        </Button>
                    </Grid>
                </Stack>
                <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                    <Grid alignItems={'center'}>
                        <LoadingButton
                            disableElevation
                            size={'large'}
                            variant={'contained'}
                            color={'primary'}
                            onClick={handleSubmit}
                            disabled={!file || loading}
                            loading={loading}
                        >
                            {t('finish')}
                        </LoadingButton>
                        {!loading ? (
                            <LoadingButton
                                onClick={handleSkip}
                                size={'large'}
                                variant={'text'}
                                color={'inherit'}
                                className={'skip-button'}
                                disabled={loading}
                                loading={loading}
                            >
                                {t('skip')}
                            </LoadingButton>
                        ) : null}
                    </Grid>
                </Stack>
            </StyledForm>
        </AuthLayout>
    );
}

export default SignUpBusinessProgramPage;
