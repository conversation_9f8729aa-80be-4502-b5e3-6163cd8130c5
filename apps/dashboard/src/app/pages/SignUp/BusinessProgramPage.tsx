import Stack from '@mui/material/Stack';
import { Formik } from 'formik';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Navigate } from 'react-router-dom';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import { WorkingProgram } from '@bookr-technologies/ui/WorkingProgram';
import { workingProgramValidation } from '@bookr-technologies/ui/WorkingProgram/workingProgramValidation';
import authSideImage from '../../../assets/authSideImage.png';
import { AuthLayout } from '../../components/Layout/AuthLayout';
import { StyledSignUpBusinessForm } from '../../components/StyledSignUpBusinessForm';
import { useSignUpBusiness } from '../../hooks/useSignUpBusiness';

function BusinessProgramPage() {
    const { t } = useTranslation('signUpBusinessIdentityPage');
    const { t: common } = useTranslation('common');

    const { handleSubmit, initialState, nextPath, checking } = useSignUpBusiness({
        redirectTo: '/auth/sign-up/business/identity',
    });

    const validationSchema = useMemo(() => workingProgramValidation('workingHours'), []);

    if (nextPath) {
        return <Navigate to={nextPath} replace />;
    }

    return (
        <AuthLayout
            image={authSideImage}
            title={t('title')}
            description={t('subtitle')}
            backTo={'/auth/sign-up/business/location'}
            sx={styles.root}
            loading={checking}
        >
            <Formik
                initialValues={initialState}
                onSubmit={handleSubmit}
                validationSchema={validationSchema}
                validateOnMount
                validateOnChange
            >
                {() => (
                    <StyledSignUpBusinessForm sx={styles.form}>
                        <Stack
                            spacing={2.5}
                            maxWidth={700}
                            marginBottom={2.5}
                            alignItems={'flex-start'}
                            justifyContent={'flex-start'}
                            overflow={'auto'}
                        >
                            <WorkingProgram name={'workingProgram'} />
                        </Stack>
                        <Stack direction={'row'} flexGrow={1} alignItems={'flex-end'} mt={3}>
                            <FormikButton size={'large'} variant={'contained'} color={'primary'} disableElevation>
                                {common('continueButton')}
                            </FormikButton>
                        </Stack>
                    </StyledSignUpBusinessForm>
                )}
            </Formik>
        </AuthLayout>
    );
}

export default BusinessProgramPage;

const styles = {
    root: {
        maxHeight: '100vh',
    },
    form: {
        maxHeight: 'calc(100% - 198px)',
    },
};
