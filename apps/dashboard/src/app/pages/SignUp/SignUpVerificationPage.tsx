import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import MuiLink from '@mui/material/Link';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { sendEmailVerification } from 'firebase/auth';
import { useSnackbar } from 'notistack';
import { useCallback, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { Link, Navigate, useNavigate } from 'react-router-dom';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useAuthStore } from '@bookr-technologies/store';
import { useCurrentUser, useFirebaseUser } from '@bookr-technologies/store/hooks/useUser';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';
import { useLocationState } from '@bookr-technologies/ui/hooks/useLocationState';
import verificationBanner from '../../../assets/verificationBanner.jpg';
import { AuthLayout } from '../../components/Layout/AuthLayout';

const Root = styled(Stack)`
    .SignUpPage-verification {
        padding: 24px;
        min-width: 476px;
        background-color: #f6f6f6;
        border-radius: 14px;
        line-height: 30px;
        margin-bottom: 76px;

        b {
            display: block;
            font-weight: 600;
            color: #111;
            margin: 4px 0;
        }
    }

    .resendLinkButton {
        background-color: #eee;
        color: #222;

        &:hover {
            background-color: #ddd;
        }
    }
`;

function SignUpVerificationPage() {
    const [loading, setLoading] = useState(false);
    const { t } = useTranslation('signUpVerificationPage');
    const setMetadata = useAuthStore((state) => state.setMetadata);
    const logout = useAuthStore((state) => state.logout);
    const { t: common } = useTranslation('common');
    const user = useCurrentUser();
    const firebaseUser = useFirebaseUser();
    const locationState = useLocationState();
    const { enqueueSnackbar } = useSnackbar();
    const navigate = useNavigate();
    const confirm = useConfirmation();
    const metadata = useAuthStore((state) => state.metadata);
    const email = user?.email || firebaseUser?.email || locationState?.email;

    const handleResend = useCallback(async () => {
        setLoading(true);
        try {
            if (firebaseUser) {
                await sendEmailVerification(firebaseUser);
            }
        } catch (error) {
            const message = getErrorMessage(error, 'errorSendingVerification');
            enqueueSnackbar(t(message), { variant: 'error' });
        } finally {
            setLoading(false);
        }
    }, [enqueueSnackbar, firebaseUser, t]);

    const handleConfirmed = useCallback(async () => {
        setLoading(true);
        try {
            await firebaseUser?.reload();
            if (firebaseUser?.email) {
                const meta = await usersEndpoint.getMetadata(firebaseUser?.email);
                setMetadata(meta);
                if (!meta.verified) {
                    // noinspection ES6MissingAwait
                    confirm({
                        title: t('emailNotConfirmed'),
                        message: t('emailNotConfirmedMessage'),
                        cancelHide: true,
                        submitText: common('ok'),
                    });
                } else {
                    navigate('/');
                }
            } else {
                enqueueSnackbar(t('errorVerifyingEmail'), { variant: 'error' });
            }
        } catch (error) {
            const message = getErrorMessage(error, 'errorCheckingConfirmedEmail');
            enqueueSnackbar(t(message), { variant: 'error' });
        } finally {
            setLoading(false);
        }
    }, [common, confirm, enqueueSnackbar, firebaseUser, navigate, setMetadata, t]);

    const handleCreateNewAccount = useCallback(async () => {
        await firebaseUser?.delete();
        await logout();
    }, [logout, firebaseUser]);

    if (!user?.email) {
        return <Navigate to={'/auth/sign-in'} replace />;
    }

    if (user && metadata?.verified) {
        return <Navigate to={'/'} replace />;
    }

    return (
        <AuthLayout image={verificationBanner} title={t('title')} description={t('subtitle')}>
            <Root alignItems={'flex-start'} justifyContent={'flex-start'} maxWidth={'480px !important'}>
                <Paper className={'SignUpPage-verification'} elevation={0}>
                    <Typography variant={'subtitle1'} fontWeight={500} color={'textSecondary'}>
                        <Trans t={t} i18nKey={'verificationMessage'} components={{ b: <b /> }} values={{ email }} />
                    </Typography>
                </Paper>

                <Grid container spacing={3}>
                    <Grid item xs>
                        <LoadingButton
                            loading={loading}
                            variant={'contained'}
                            color={'primary'}
                            size={'large'}
                            onClick={handleConfirmed}
                            disableElevation
                            fullWidth
                        >
                            {t('confirmed')}
                        </LoadingButton>
                    </Grid>
                    <Grid item xs>
                        {!loading ? (
                            <LoadingButton
                                loading={loading}
                                variant={'contained'}
                                size={'large'}
                                className={'resendLinkButton'}
                                onClick={handleResend}
                                disableElevation
                                fullWidth
                            >
                                {t('resendEmail')}
                            </LoadingButton>
                        ) : null}
                    </Grid>
                </Grid>

                <Typography variant={'subtitle1'} fontWeight={500} mt={3}>
                    {t('wrongEmail')}
                </Typography>
                <Typography variant={'subtitle1'} fontWeight={500} mt={0.5} color={'textSecondary'}>
                    <Trans
                        t={t}
                        i18nKey={'enterNewEmail'}
                        components={{
                            cta: (
                                <MuiLink
                                    onClick={handleCreateNewAccount}
                                    component={Link}
                                    to={'/auth/sign-up'}
                                    underline={'hover'}
                                    color={'accent.main'}
                                />
                            ),
                        }}
                        values={{ email: user?.email }}
                    />
                </Typography>
            </Root>
        </AuthLayout>
    );
}

export default SignUpVerificationPage;
