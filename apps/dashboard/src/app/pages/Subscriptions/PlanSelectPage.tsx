import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { PeriodType } from '@bookr-technologies/api/constants/PeriodType';
import { BackButton } from '@bookr-technologies/ui/Buttons/BackButton';
import { PeriodSwitch } from '@bookr-technologies/ui/PeriodSwitch';
import { PlanSelect } from '@bookr-technologies/ui/PlanSelect';
import { useLocationState } from '@bookr-technologies/ui/hooks/useLocationState';
import { Page } from '../../components/Page';

const Root = styled(Page)(({ theme }) => ({
    paddingTop: theme.spacing(6),
}));

export function PlanSelectPage() {
    const { t } = useTranslation('planSelectPage');
    const [period, setPeriod] = useState(PeriodType.Yearly);
    const state = useLocationState();

    return (
        <Root backgroundColor={'#f6f6f6'}>
            {state.from ? (
                <Grid container mb={3}>
                    <BackButton to={state.from} replace />
                </Grid>
            ) : null}
            <Grid container alignItems={'center'} justifyContent={'center'} direction={'column'} mb={7}>
                <Typography variant={'h3'} component={'h1'} fontWeight={700} mb={4} align={'center'} maxWidth={524}>
                    {t('title')}
                </Typography>
                <PeriodSwitch value={period} onChange={setPeriod} />
            </Grid>
            <Container maxWidth={'lg'}>
                <PlanSelect period={period} />
            </Container>
        </Root>
    );
}

export default PlanSelectPage;
