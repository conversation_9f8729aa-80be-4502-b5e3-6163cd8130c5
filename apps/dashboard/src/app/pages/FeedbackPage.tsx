import BugReportIcon from '@mui/icons-material/BugReport';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CommentIcon from '@mui/icons-material/Comment';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import SegmentIcon from '@mui/icons-material/Segment';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import Chip from '@mui/material/Chip';
import Fade from '@mui/material/Fade';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled, SxProps } from '@mui/material/styles';
import classNames from 'classnames';
import { Form, Formik, FormikProps } from 'formik';
import { t } from 'i18next';
import { useSnackbar } from 'notistack';
import { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { object, string } from 'yup';
import { feedbackEndpoint } from '@bookr-technologies/api/endpoints/feedbackEndpoint';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { Page } from '../components/Page';

const CATEGORIES = [
    { value: 'Bug Report', code: 'bugReport', icon: <BugReportIcon fontSize={'small'} /> },
    { value: 'Suggestions', code: 'suggestions', icon: <CommentIcon fontSize={'small'} /> },
    { value: 'Content', code: 'content', icon: <SegmentIcon fontSize={'small'} /> },
    { value: 'Compliment', code: 'compliment', icon: <ThumbUpIcon fontSize={'small'} /> },
    { value: 'Other', code: 'other', icon: <MoreHorizIcon fontSize={'small'} /> },
];

const initialValues = {
    thoughts: '',
    message: '',
    category: CATEGORIES[0].value,
};

const validationSchema = object().shape({
    thoughts: string().required(t('errors.requiredField', { ns: 'common' })),
    message: string().required(t('errors.requiredField', { ns: 'common' })),
});

const StyledChip = styled(Chip)(({ theme }) => ({
    margin: theme.spacing(0, 2, 2, 0),
    padding: theme.spacing(0, 1.5),
    height: 36,
    '.MuiChip-icon': {
        margin: 0,
        fontSize: 16,
        width: 16,
        height: 16,
    },
    '&.active': {
        backgroundColor: '#333',
        color: '#fff',
        '.MuiChip-icon': {
            color: theme.palette.accent.main,
        },
    },
}));

function FeedbackPage() {
    const formikRef = useRef<FormikProps<typeof initialValues>>(null);
    const [status, setStatus] = useState<string | null>(null);
    const { t } = useTranslation('feedbackPage');
    const { enqueueSnackbar } = useSnackbar();

    const handleSubmit = useCallback(
        async (values: any) => {
            setStatus(null);
            try {
                await feedbackEndpoint.create(values);
                setStatus('success');
                formikRef.current?.resetForm();
            } catch (e) {
                const message = getErrorMessage(e, 'errorSendingFeedback');
                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [enqueueSnackbar, t],
    );

    const handleCategory = useCallback(
        (value: string) => () => {
            formikRef.current?.setFieldValue('category', value);
        },
        [],
    );

    return (
        <Page title={t('title')}>
            <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                innerRef={formikRef}
                validationSchema={validationSchema}
                validateOnMount
                validateOnChange
            >
                {({ values }) => (
                    <Grid container alignItems={'flex-start'}>
                        <Paper component={Form} sx={styles.paper}>
                            <Grid container>
                                <Typography variant={'body1'} color={'textSecondary'} mb={2}>
                                    {t('whatDoYouThinkAboutUs')}
                                </Typography>
                                <FormikTextField
                                    placeholder={t('whatDoYouThinkAboutUs')}
                                    name={'thoughts'}
                                    InputProps={{ minRows: 3 }}
                                    multiline
                                    fullWidth
                                />
                            </Grid>
                            <Grid container mb={2} mt={3}>
                                <Typography variant={'body1'} color={'textSecondary'} mb={2}>
                                    {t('chooseFeedbackCategory')}
                                </Typography>
                                <Grid container>
                                    {CATEGORIES.map(({ code, value, icon }) => (
                                        <StyledChip
                                            key={code}
                                            label={t(`categories.${code}`)}
                                            icon={icon}
                                            onClick={handleCategory(value)}
                                            className={classNames({ active: value === values.category })}
                                        />
                                    ))}
                                </Grid>
                            </Grid>
                            <Grid container>
                                <Typography variant={'body1'} color={'textSecondary'} mb={2}>
                                    {t('tellUsWhatWeCanImprove')}
                                </Typography>
                                <FormikTextField
                                    placeholder={t('tellUsWhatWeCanImprove')}
                                    name={'message'}
                                    InputProps={{ minRows: 3 }}
                                    multiline
                                    fullWidth
                                />
                            </Grid>
                            <Grid container justifyContent={'flex-end'} mt={5}>
                                <FormikButton variant={'contained'} disableElevation>
                                    {t('send')}
                                </FormikButton>
                            </Grid>
                        </Paper>

                        <Fade in={status === 'success'} unmountOnExit>
                            <Paper sx={styles.successPaper}>
                                <CheckCircleIcon color={'success'} />
                                <div className={'content'}>
                                    <Typography variant={'body2'} fontWeight={700} mb={0.5}>
                                        {t('feedbackSentSuccessfully')}
                                    </Typography>
                                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                        {t('feedbackSentSuccessfullyDescription')}
                                    </Typography>
                                </div>
                            </Paper>
                        </Fade>
                    </Grid>
                )}
            </Formik>
        </Page>
    );
}

const styles: Record<string, SxProps> = {
    paper: {
        maxWidth: 634,
        boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
        borderRadius: '16px',
        padding: '26px 36px',
    },

    successPaper: {
        width: 'calc(100% - 658px)',
        boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
        borderRadius: '16px',
        padding: '24px',
        marginLeft: '20px',
        display: 'flex',
        alignItems: 'flex-start',
        '.content': {
            marginLeft: '20px',
        },
    },
};

export default FeedbackPage;
