import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '@bookr-technologies/store';

export function LogoutPage() {
    const logout = useAuthStore((store) => store.logout);
    const authenticated = useAuthStore((store) => store.authenticated);
    const navigate = useNavigate();

    useEffect((): void => {
        // noinspection JSIgnoredPromiseFromCall
        logout();
    }, [logout]);

    useEffect(() => {
        if (authenticated === false) {
            navigate('/');
        }
    }, [authenticated, navigate]);
    return null;
}
