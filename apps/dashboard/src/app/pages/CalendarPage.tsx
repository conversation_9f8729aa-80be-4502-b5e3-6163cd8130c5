import { styled } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { CalendarEvent, CalendarView } from '@bookr-technologies/calendar-view';
import { useBusiness } from '@bookr-technologies/store/hooks/useBusiness';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import { Page } from '../components/Page';

function CalendarPage() {
    const { t } = useTranslation('calendarPage');
    const business = useBusiness();

    const resources = business.data?.staffMembers?.map((staffMember) => ({
        id: staffMember.uid,
        displayName: staffMember.displayName,
        photoURL: staffMember.photoURL,
    }));

    const events: CalendarEvent[] = [
        {
            summary: 'Consultație Oftalmologie',
            title: '<PERSON>',
            start: '2022-10-15T10:00:00.000Z',
            end: '2022-10-15T10:10:00.000Z',
            color: '#000',
            data: {},
            resourceId: resources?.[0]?.id || '1',
        },
        {
            summary: 'Consultație Oftalmologie',
            title: '<PERSON>',
            start: '2022-10-15T10:00:00.000Z',
            end: '2022-10-16T10:15:00.000Z',
            color: '#f00',
            data: {},
            resourceId: resources?.[0]?.id || '1',
        },
        {
            summary: 'Consultație Oftalmologie',
            title: 'Martin Stoleru',
            start: '2022-10-15T10:00:00.000Z',
            end: '2022-10-15T10:20:00.000Z',
            color: '#000',
            data: {},
            resourceId: resources?.[1]?.id || '1',
        },
        {
            summary: 'Consultație Oftalmologie',
            title: 'Martin Stoleru',
            start: '2022-10-15T10:00:00.000Z',
            end: '2022-10-15T10:30:00.000Z',
            color: '#000',
            data: {},
            resourceId: resources?.[2]?.id || '1',
        },
        {
            summary: 'Consultație Oftalmologie',
            title: 'Martin Stoleru',
            start: '2022-10-15T10:00:00.000Z',
            end: '2022-10-15T10:40:00.000Z',
            color: '#000',
            data: {},
            resourceId: resources?.[3]?.id || '1',
        },
    ];

    if (business.isFetching || !business.data) {
        return <PageLoader />;
    }

    return (
        <Root title={t('title')} fluid disableGutters backgroundColor="#fff">
            <CalendarView resources={resources} events={events} />
        </Root>
    );
}

const Root = styled(Page)({
    padding: 0,
    '.Page-container': {
        display: 'flex',
        maxHeight: '100vh',
    },
});

export default CalendarPage;
