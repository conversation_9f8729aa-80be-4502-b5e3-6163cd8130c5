import Box from '@mui/material/Box';
import Paper from '@mui/material/Paper';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useBusinessClient } from '@bookr-technologies/store/hooks/useBusinessClients';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import { TabPanel } from '@bookr-technologies/ui/TabPanel';
import AppointmentsList from '../../components/AppointmentsList/AppointmentsList';
import { ClientNotesList } from '../../components/ClientNotesList/ClientNotesList';
import { DocumentsList } from '../../components/DocumentsList/DocumentsList';

const StyledPaperTabs = styled(Paper)`
    height: 700px;
    max-height: 700px;
    overflow-y: auto;
    background: #fff;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
    border-radius: 16px;
    padding: 40px !important;

    .MuiTabs-root {
        padding-bottom: 20px;
        margin-top: -20px;
    }

    .MuiTabs-indicator {
        display: none;
    }

    .MuiTabs-flexContainer {
        justify-content: center;
        border-radius: 16px;
        background-color: #eee;
    }

    .Mui-selected {
        margin-top: 6px;
        margin-bottom: 6px;
        border-radius: 11px;
        background-color: ${({ theme }) => theme.palette.common.white};
        box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
    }
`;

export const StyledTabs = styled(Tabs)`
    .MuiButtonBase-root {
        text-transform: none;
        width: 32%;
    }
`;
export function ClientTabs({ clientId, defaultActiveTab = 0 }: { clientId: string; defaultActiveTab?: number }) {
    const { t } = useTranslation('clientPage');
    const { data, isLoading, isError } = useBusinessClient(clientId);
    const [tabIndex, setTabIndex] = useState(defaultActiveTab);

    const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
        setTabIndex(newValue);
    };

    if (isLoading || isError || !data) {
        return <PageLoader />;
    }

    const { client, appointments } = data;

    return (
        <StyledPaperTabs>
            <Box>
                <StyledTabs value={tabIndex} onChange={handleTabChange}>
                    <Tab disableRipple label={t('appointments')} />
                    <Tab disableRipple label={t('documents')} />
                    <Tab disableRipple label={t('notes')} />
                </StyledTabs>
            </Box>
            <TabPanel value={tabIndex} index={0}>
                <AppointmentsList appointments={appointments} />
            </TabPanel>
            <TabPanel value={tabIndex} index={1}>
                <DocumentsList clientId={client.uid} />
            </TabPanel>
            <TabPanel value={tabIndex} index={2}>
                <ClientNotesList clientId={client.uid} />
            </TabPanel>
        </StyledPaperTabs>
    );
}
