import SearchIcon from '@mui/icons-material/Search';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import InputBase from '@mui/material/InputBase';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import { AxiosError } from 'axios';
import debounce from 'lodash/debounce';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useAuthStore } from '@bookr-technologies/store';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import { PrivilegeBasedAccess } from '@bookr-technologies/ui/PrivilegeBasedAccess';
import { UsersProfilesList } from '@bookr-technologies/ui/UsersProfilesList/UsersProfilesList';
import { InviteClients } from '../../components/InviteClients/InviteClients';
import { Page } from '../../components/Page';

const Search = styled('div')({
    position: 'relative',
    borderRadius: '8px',
    border: '1px solid #757575',
    backgroundColor: '#F6F6F6',
    '&:hover': {
        backgroundColor: '#F6F6F6',
    },
    marginLeft: 0,
    width: '100%',
});

const SearchIconWrapper = styled('div')({
    padding: ' 8px 12px',
    height: '100%',
    position: 'absolute',
    pointerEvents: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
});

const StyledInputBase = styled(InputBase)(({ theme }) => ({
    color: '#545454',
    '& .MuiInputBase-input': {
        padding: theme.spacing(1, 1, 1, 0),
        // vertical padding + font size from searchIcon
        paddingLeft: `calc(1em + ${theme.spacing(4)})`,
        transition: theme.transitions.create('width'),
        width: '100%',
        [theme.breakpoints.up('sm')]: {
            width: '20ch',
            '&:focus': {
                width: '20ch',
            },
        },
    },
    '&::placeholder': {
        color: '#545454',
    },
}));

const StyledPaper = styled(Paper)({
    height: '80vh',
    overflowY: 'auto',
    background: '#FFFFFF',
    boxShadow: '0px 2px 6px rgba(5, 16, 55, 0.1)',
    borderRadius: '16px',
});

const StyledGrid = styled(Grid)({
    minHeight: '90vh',
});

const LoadMoreButton = styled(Button)({
    backgroundColor: '#eeeeee',
    fontWeight: 500,
    color: '#111111',
    borderRadius: '10px',
    fontSize: '14px',
    lineHeight: '21px',
    alignSelf: 'center',
    textTransform: 'none',
    padding: '10px 20px',
});

const CLIENTS_PER_PAGE = 20;
const debouncedFunc = debounce((func) => func(), 500);

const UserProfilesList = function ClientsPage() {
    const { t } = useTranslation('clientsPage');
    const user = useAuthStore((state) => state.user);
    const businessId = user?.business.id;

    const [page, setPage] = useState(0);
    const [searchValue, setSearchValue] = useState('');
    const [clients, setClients] = useState<UserModel[]>([]);

    const { data, isFetching, isError, refetch } = useQuery(
        `business:clients`,
        () =>
            businessClientsEndpoint.getClients({
                businessId,
                textSearch: searchValue,
                page,
                size: CLIENTS_PER_PAGE,
            }),
        {
            retry: (failureCount, error: any) => {
                const { response } = error as AxiosError;
                return ![401, 403].includes(response?.status || 0) && failureCount <= 3;
            },
            cacheTime: 0,
            onSuccess: (data) => {
                if (page > 0) {
                    setClients((prevState) => [...prevState, ...(data?.content ?? [])]);
                } else {
                    setClients(data?.content ?? []);
                }
                setPage((prev) => prev + 1);
            },
        },
    );

    if (isError) {
        return <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]} />;
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const findUser = (e: any) => {
        if (searchValue === '' || e.target.value === '' || e.target.value !== searchValue) {
            setPage(0);
        }
        setSearchValue(e.target.value);
        debouncedFunc(() => refetch());
    };

    const updatePage = () => {
        refetch();
    };

    return (
        <Page title={t('title')}>
            <Grid container spacing={2}>
                <StyledGrid item xs={6}>
                    <StyledPaper sx={{ padding: 3 }}>
                        <Search>
                            <SearchIconWrapper>
                                <SearchIcon />
                            </SearchIconWrapper>
                            <StyledInputBase
                                placeholder={t('searchPlaceholder')}
                                inputProps={{ 'aria-label': 'search' }}
                                onChange={findUser}
                            />
                        </Search>
                        {isFetching && <PageLoader padding={2} />}
                        <UsersProfilesList clients={clients} />
                        {!data?.last && (
                            <Grid container spacing={0} direction="column" alignItems="center" justifyContent="center">
                                <LoadMoreButton onClick={updatePage}>{t('displayMore')}</LoadMoreButton>
                            </Grid>
                        )}
                    </StyledPaper>
                </StyledGrid>
                <StyledGrid item xs={6}>
                    <InviteClients />
                </StyledGrid>
            </Grid>
        </Page>
    );
};

export default UserProfilesList;
