import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router';
import { useBusinessClient } from '@bookr-technologies/store/hooks/useBusinessClients';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import ClientCard from '../../components/ClientCard/ClientCard';
import { Page } from '../../components/Page';
import { ClientTabs } from './ClientTabs';

const StyledPaperProfile = styled(Paper)`
    min-height: 20vh;
    overflow-y: auto;
    background: #fff;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
    border-radius: 16px;
    padding: 40px !important;

    .MuiTabs-root {
        padding-bottom: 20px;
        margin-top: -20px;
    }

    .MuiTabs-indicator {
        display: none;
    }

    .MuiTabs-flexContainer {
        justify-content: center;
        border-radius: 16px;
        background-color: #eee;
    }

    .MuiButtonBase-root {
        text-transform: none;
        width: 45%;
    }

    .Mui-selected {
        margin-top: 6px;
        margin-bottom: 6px;
        border-radius: 11px;
        background-color: ${({ theme }) => theme.palette.common.white};
    }
`;

function ClientPage() {
    const { t } = useTranslation('clientPage');
    const { clientId } = useParams();

    const { data, isLoading, isError } = useBusinessClient(clientId);

    if (isLoading || isError || !data) {
        return <PageLoader />;
    }
    const { client, stats } = data;

    return (
        <Page backToLabel={t('backToClients')} backToHref={'/clients'}>
            <Grid container spacing={2}>
                <Grid item xs={5}>
                    <StyledPaperProfile sx={{ padding: 3 }}>
                        <ClientCard client={client} stats={stats} />
                    </StyledPaperProfile>
                </Grid>
                <Grid item xs={7}>
                    <ClientTabs clientId={client.uid} />
                </Grid>
            </Grid>
        </Page>
    );
}

export default ClientPage;
