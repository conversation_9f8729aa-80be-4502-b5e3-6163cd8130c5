import { useMutation } from 'react-query';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { businessBlockEndpoint } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { queryClient } from '@bookr-technologies/store/queryClient';

export function useBlockClientMutation() {
    return useMutation(({ uid, value }: { uid: string; value: boolean }) =>
        businessBlockEndpoint.blockClient(uid, value),
    );
}

export function useDeleteClientMutation() {
    return useMutation((clientId: string) => businessClientsEndpoint.destroy(clientId), {
        onSuccess: async () => {
            await queryClient.invalidateQueries(`business:clients`);
        },
    });
}
