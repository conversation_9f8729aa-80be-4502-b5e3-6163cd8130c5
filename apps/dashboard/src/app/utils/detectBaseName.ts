export function detectDashboardBaseName(url?: string | URL) {
    const url$ = typeof url === 'string' ? new URL(url, window.location.toString()) : url;
    const isDashboardHost = /^(staging\.)?dashboard(.*)$/i.test(url$?.host || window.location.host);
    const isLocalHost = /^localhost(.*)$/i.test(url$?.host || window.location.host);

    return isDashboardHost || isLocalHost ? '/' : '/dashboard';
}
