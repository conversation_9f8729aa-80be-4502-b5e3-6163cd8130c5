import { RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { FirebaseService } from '@bookr-technologies/core/services';
import { useAuthStore } from '@bookr-technologies/store';

export async function verifyPhoneNumber(
    verifier: RecaptchaVerifier,
    values: { callingCode: string; phoneNumber: string },
) {
    const confirmationResult = await signInWithPhoneNumber(
        FirebaseService.auth(),
        values.callingCode + values.phoneNumber,
        verifier,
    );

    useAuthStore.getState().setConfirmationResult(confirmationResult);
    return confirmationResult;
}
