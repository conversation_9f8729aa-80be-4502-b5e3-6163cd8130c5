import { detectDashboardBaseName } from './detectBaseName';

describe('#detectBaseName', () => {
    it('should match the right basename', () => {
        expect(detectDashboardBaseName()).toEqual('/');
        expect(detectDashboardBaseName('http://localhost:4200')).toEqual('/');
        expect(detectDashboardBaseName('http://localhost:4200/dashboard/foo')).toEqual('/');
        expect(detectDashboardBaseName('/dashboard/foo')).toEqual('/');

        expect(detectDashboardBaseName('http://bookr.ro')).toEqual('/dashboard');
        expect(detectDashboardBaseName('http://bookr.ro/dashboard')).toEqual('/dashboard');
        expect(detectDashboardBaseName('http://dashboard.bookr.ro')).toEqual('/');
        expect(detectDashboardBaseName('http://dashboard.bookr.ro/dashboard')).toEqual('/');
        expect(detectDashboardBaseName('http://dashboard.bookr.ro/dashboard/foo')).toEqual('/');

        expect(detectDashboardBaseName('http://staging.bookr.ro')).toEqual('/dashboard');
        expect(detectDashboardBaseName('http://staging.bookr.ro/dashboard')).toEqual('/dashboard');
        expect(detectDashboardBaseName('http://staging.dashboard.bookr.ro')).toEqual('/');
        expect(detectDashboardBaseName('http://staging.dashboard.bookr.ro/dashboard')).toEqual('/');
        expect(detectDashboardBaseName('http://staging.dashboard.bookr.ro/dashboard/foo')).toEqual('/');
    });
});
