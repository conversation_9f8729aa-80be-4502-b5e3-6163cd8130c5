import invert from 'invert-color';
import moment from 'moment';
import { useMutation } from 'react-query';
import { EventInput } from '@fullcalendar/common';
import { BookNowRequest } from '@bookr-technologies/api/dto/BookNowDTO';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { rgba2hex } from '@bookr-technologies/ui/styles/rgba2hex';

export const getBusinessWithAppointments = async (businessId: string, timestampFrom = 1, timestampTo = 1) => {
    return await businessEndpoint.show(businessId, {
        params: {
            includeAppointments: true,
            timestampFrom,
            timestampTo,
        },
    });
};

export const getAppointmentById = async (appointmentId: number): Promise<AppointmentModel> => {
    return await appointmentsEndpoint.show(appointmentId);
};

export const cancelAppointment = async (appointmentId: number) => {
    await appointmentsEndpoint.cancelAppointment(appointmentId);
};

export function useBookNowMutation() {
    return useMutation((data: BookNowRequest) => appointmentsEndpoint.create(data));
}

const BW_PALETTE = { black: '#111111', white: '#ffffff' };
const DEFAULT_BREAK_BACKGROUND_COLOR = '#2F80FB';
const DEFAULT_BREAK_TEXT_COLOR = invert(DEFAULT_BREAK_BACKGROUND_COLOR, BW_PALETTE);

export const mapStaffAppointmentsToCalendarEvents = (staffAppointments: AppointmentModel[]) =>
    staffAppointments.map<EventInput>((appointment) => {
        appointment.service.color = getColor(appointment.service.color) || '#2F80FB';
        return {
            id: `${appointment.id}`,
            start: moment(appointment.dateTime).toDate(),
            end: moment(appointment.dateTime).add(appointment.service.duration, 'minutes').toDate(),
            title: `${appointment.client.displayName}\n${appointment.service.name}`,
            backgroundColor: appointment.service.color,
            textColor: invert(appointment.service.color, BW_PALETTE),
            borderColor: 'transparent',
            resourceId: appointment.staff.uid,
            extendedProps: { appointment },
        };
    });

export const mapBreakAppointmentsToCalendarEvents = (breaks: BreakModel[], staffId: string) =>
    breaks.map<EventInput>((br) => ({
        id: `${br.id}`,
        start: moment(br.fromDateTime).toDate(),
        end: moment(br.toDateTime).toDate(),
        title: br.title || 'Break',
        backgroundColor: DEFAULT_BREAK_BACKGROUND_COLOR,
        textColor: DEFAULT_BREAK_TEXT_COLOR,
        borderColor: `transparent`,
        resourceId: staffId,
        extendedProps: {
            isBreak: true,
            break: br,
        },
    }));

const getColor = (color: string) => (color.startsWith('rgba') ? rgba2hex(color) : color);
