import { useMutation } from 'react-query';
import { breaksEndpoint } from '@bookr-technologies/api/endpoints/breaksEndpoint';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';

export const deleteBreak = async (breakId: number) => {
    await breaksEndpoint.destroy(breakId);
};

export function useCreateBreakMutation() {
    return useMutation((data: Partial<BreakModel>) => breaksEndpoint.create(data));
}
