import { t } from 'i18next';
import * as Yup from 'yup';

export const signUpUserSchema = Yup.object().shape({
    firstName: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    lastName: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    email: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    language: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    password: Yup.string()
        .required(t('errors.requiredField', { ns: 'common' }))
        .min(8, t('errors.minLength', { ns: 'common', minLength: 8 })),
    passwordConfirmation: Yup.string()
        .required(t('errors.requiredField', { ns: 'common' }))
        .oneOf([Yup.ref('password'), null], t('errors.passwordNotMatch', { ns: 'common' })),
});
