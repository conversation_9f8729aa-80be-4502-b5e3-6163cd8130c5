import { t } from 'i18next';
import * as Yup from 'yup';

export const signUpBusinessSchema = {
    mainPage: Yup.object().shape({
        businessName: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
        callingCode: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
        phoneNumber: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    }),
    categoriesPage: Yup.object().shape({
        categories: Yup.array()
            .required(t('errors.requiredField', { ns: 'common' }))
            .min(1, t('errors.requiredField', { ns: 'common' })),
    }),
    locationPage: Yup.object().shape({
        locationName: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
        location: Yup.object().shape({
            lat: Yup.number().required(t('errors.requiredField', { ns: 'common' })),
            lng: Yup.number().required(t('errors.requiredField', { ns: 'common' })),
        }),
    }),
};
