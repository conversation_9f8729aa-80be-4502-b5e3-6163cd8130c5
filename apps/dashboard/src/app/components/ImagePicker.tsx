import LoadingButton from '@mui/lab/LoadingButton';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import { useFormikContext } from 'formik';
import React, { useCallback, useState } from 'react';
import { useFilePicker } from '@bookr-technologies/ui/hooks/useFilePicker';

interface Props {
    name: string;
    onDelete(image: string): void;
}

const StyledAvatar = styled(Avatar)(({ theme }) => ({
    width: 94,
    height: 94,
    boxShadow: theme.shadows[2],
}));

export interface ImagePickerRecord {
    image: string | null;
    file: File | null;
}

export function ImagePicker({ name, onDelete }: Props) {
    const formik = useFormikContext();
    const profileImage = formik.getFieldMeta<ImagePickerRecord['image']>(`${name}.image`);
    const profileFile = formik.getFieldMeta<ImagePickerRecord['file']>(`${name}.file`);
    const [loading, setLoading] = useState(false);

    const filePicker = useFilePicker({
        accept: 'image/*',
        onChange([file]: File[]) {
            console.log(file);
            formik.setFieldValue(`${name}.image`, filePicker.generatePreview(file));
            formik.setFieldValue(`${name}.file`, file);
        },
    });

    const handleDelete = useCallback(async () => {
        setLoading(true);
        try {
            if (!profileFile.value && profileImage.value) {
                await onDelete(profileImage.value);
            }

            filePicker.invalidatePreview(profileFile.value);

            formik.setFieldValue(`${name}.image`, null);
            formik.setFieldValue(`${name}.file`, null);
        } finally {
            setLoading(false);
        }
    }, [filePicker, formik, name, onDelete, profileFile.value, profileImage.value]);

    return (
        <>
            {profileImage.value ? <StyledAvatar src={profileImage.value} /> : <StyledAvatar />}
            <Stack width={100} marginLeft={2}>
                <Button color={'accent'} fullWidth onClick={filePicker.handleClick}>
                    Upload
                </Button>
                <LoadingButton color={'error'} fullWidth onClick={handleDelete} disabled={loading} loading={loading}>
                    Delete
                </LoadingButton>
            </Stack>
        </>
    );
}
