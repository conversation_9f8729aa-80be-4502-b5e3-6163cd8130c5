import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { NoteModel } from '@bookr-technologies/api/models/NoteModel';
import PageLoader from '@bookr-technologies/ui/PageLoader/PageLoader';
import { NoteCard } from '../NoteCard/NoteCard';
import { NoteDialog } from '../NoteDialog/NoteDialog';

interface ClientNoteListProps {
    clientId: string;
}

const StyledStack = styled(Stack)`
    height: 500px;
    overflow-y: auto;
    border-radius: 16px;
    padding: ${({ theme }) => theme.spacing(1.5, 0, 1.5, 0)};
    margin-bottom: ${({ theme }) => theme.spacing(3)};
    position: relative;
`;

const StyledHeaderContainer = styled('div')`
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: space-between;

    .addNote {
        font-size: 14px;
        font-weight: 600;
        color: ${({ theme }) => theme.palette.accent.main};
    }

    .addNote:hover {
        cursor: pointer;
    }
`;

const StyledNoNotesContainer = styled('div')`
    height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .headerText {
        height: 60px;
        width: 400px;
        font-size: 24px;
        font-weight: bold;
        font-family: Plus Jakarta Display, sans-serif;
        color: #1f1f1f;
        text-align: center;
    }

    .addNoteButton {
        width: auto !important;
        height: 40px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 500;
        color: ${({ theme }) => theme.palette.common.white};
        background: #111111;
    }
`;

export function ClientNotesList({ clientId }: ClientNoteListProps) {
    const { enqueueSnackbar } = useSnackbar();
    const [noteDialogOpen, setNoteDialogOpen] = useState(false);
    const [noteData, setNoteData] = useState(new NoteModel());
    const { t } = useTranslation('appointmentDetailsPage');
    const { t: commonT } = useTranslation('common');

    const { data, isLoading, isError, refetch } = useQuery(
        ['notes', clientId],
        () => businessClientsEndpoint.getClientNotes(clientId),
        {
            retry: 1,
            onError: () => enqueueSnackbar(t('errorGettingNotes'), { variant: 'error' }),
        },
    );

    if (isLoading) {
        return <PageLoader />;
    }

    const onSubmit = () => {
        refetch();
        setNoteData(new NoteModel());
    };

    const onDelete = () => {
        refetch();
        setNoteData(new NoteModel());
    };

    if (isError || !data || data?.length === 0) {
        return (
            <>
                <NoteDialog
                    note={noteData}
                    clientId={clientId}
                    onSubmit={onSubmit}
                    onDelete={onDelete}
                    onClose={() => setNoteDialogOpen(false)}
                    open={noteDialogOpen}
                />
                <StyledNoNotesContainer>
                    <Typography className={'headerText'}>{t('noNotes')}</Typography>
                    <Button className={'addNoteButton'} onClick={() => setNoteDialogOpen(true)}>
                        {t('addNote')}
                    </Button>
                </StyledNoNotesContainer>
            </>
        );
    }

    const NoteList = (data || []).map((note: NoteModel) => (
        <NoteCard note={note} setNoteData={setNoteData} setNoteDialogOpen={setNoteDialogOpen} />
    ));

    return (
        <>
            <StyledHeaderContainer>
                <Typography
                    variant={'h5'}
                    component={'h2'}
                    lineHeight={'32px'}
                    marginBottom={'24px'}
                    fontWeight={'bold'}
                >
                    {commonT('generalNotes')}
                </Typography>
                {data.length > 0 && (
                    <div className={'addNote'} onClick={() => setNoteDialogOpen(true)}>
                        {t('addNote')}
                    </div>
                )}
            </StyledHeaderContainer>
            {data.length > 0 && (
                <StyledStack display={'flex'} flexDirection={'column'} spacing={2}>
                    {NoteList}
                </StyledStack>
            )}
            <NoteDialog
                note={noteData}
                clientId={clientId}
                onSubmit={onSubmit}
                onDelete={onDelete}
                onClose={() => setNoteDialogOpen(false)}
                open={noteDialogOpen}
            />
        </>
    );
}
