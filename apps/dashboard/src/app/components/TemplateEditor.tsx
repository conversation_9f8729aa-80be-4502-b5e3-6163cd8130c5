import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import { alpha, styled } from '@mui/material/styles';
import { useFormikContext } from 'formik';
import random from 'lodash/random';
import { ClipboardEvent, KeyboardEvent, MouseEvent, useCallback, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { notificationsEndpoint } from '@bookr-technologies/api/endpoints/notificationEndpoint';

const closeIcon =
    '<span class="closeIcon"><svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 0 24 24" width="20px"><path d="M0 0h24v24H0V0z" fill="none"/><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/></svg></span>';

const Root = styled('section')(({ theme }) => ({
    '.NotificationPage-templateVariableBlock': {
        display: 'inline-block',
    },
    '.TemplateEditor-content': {
        minHeight: 140,
        outline: 'none',
        fontFamily: 'Poppins',
        fontWeight: 500,
        fontSize: 14,
        lineHeight: '21px',
        color: '#545454',
        maxHeight: 300,
        overflow: 'auto',
    },
    '.MuiChip-root': {
        marginRight: theme.spacing(1.25),
        marginBottom: theme.spacing(1.25),
    },
    '.MuiChip-label': {
        padding: 0,
    },
    '.TemplateEditor-variableChip': {
        margin: '0 0 12px',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden',
        transition: theme.transitions.create('padding'),

        '&:first-child': {
            marginLeft: 0,
        },

        '& + &': {
            marginLeft: 0,
        },

        '.closeIcon': {
            position: 'absolute',
            top: 0,
            left: 0,
            opacity: 0,
            height: 32,
            width: '100%',
            pointerEvents: 'none',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            transform: 'translate(-4%, 0)',
            transition: theme.transitions.create(['transform', 'opacity']),
            '& svg': {
                fill: theme.palette.accent.main,
            },
        },

        '&:hover': {
            color: 'transparent',
            '.closeIcon': {
                opacity: 1,
                transform: 'translate(0, 0)',
            },
        },
    },
    '.TemplateEditor-variableChip, .MuiChip-root': {
        display: 'inline-flex',
        height: 32,
        lineHeight: '32px',
        padding: '0 14px',
        backgroundColor: alpha(theme.palette.accent.main, 0.1),
        color: theme.palette.accent.main,
        borderRadius: 8,
        fontSize: 12,
        fontWeight: 500,
        transition: theme.transitions.create(['background-color', 'color']),
        '&:hover': {
            backgroundColor: alpha(theme.palette.accent.main, 0.2),
        },
        '&:active': {
            backgroundColor: alpha(theme.palette.accent.main, 0.3),
        },
    },
}));

interface Props {
    defaultValue: string;
    name: string;
}

// TODO: Refactor this mess
export function TemplateEditor({ name, defaultValue }: Props) {
    const { t } = useTranslation('businessSettingsPage');
    const editorRef = useRef<HTMLDivElement | null>(null);
    const formikContext = useFormikContext();

    const templateVariables = useQuery('notifications/templates/variables', () =>
        notificationsEndpoint.getTemplatesVariables(),
    );

    const createChip = useCallback(
        (varName: string) => {
            if (!varName && !templateVariables.data?.includes(varName)) {
                return null;
            }

            const el = document.createElement('figure');
            el.id = `${varName}-${random(1000, 9999)}`;
            el.contentEditable = 'false';
            el.spellcheck = false;
            el.innerText = t(`variables.${varName}`);
            el.innerHTML += closeIcon;
            el.classList.add('TemplateEditor-variableChip', `TemplateEditor-${varName}`);
            el.setAttribute('data-varName', varName);

            if (varName === 'newLine') {
                const fragment = document.createDocumentFragment();
                fragment.appendChild(el);
                fragment.appendChild(document.createElement('br'));
                return fragment;
            }

            return el;
        },
        [t, templateVariables.data],
    );

    const getRange = useCallback(() => {
        const element = editorRef.current;

        if (element && window.getSelection) {
            const selection = window.getSelection();
            if (selection?.rangeCount) {
                const range = selection?.getRangeAt(0);
                if (range.commonAncestorContainer.parentNode === element) {
                    return range;
                }
            }
        }

        return null;
    }, []);

    const goToEnd = useCallback((node?: Node | null) => {
        const el = editorRef.current;
        const range = document.createRange();
        const selection = window.getSelection();

        if (el && selection) {
            range.setEndAfter(node || el.childNodes[el.childNodes.length - 1]);
            range.collapse();

            selection.removeAllRanges();
            selection.addRange(range);
        }
    }, []);

    const normalize = useCallback(() => {
        const element = editorRef.current;
        if (element) {
            element.innerHTML = element.innerHTML.replace(/<figure/g, '&nbsp;<figure');
            element.innerHTML = element.innerHTML.replace(/<\/figure>/g, '</figure>&nbsp;');
            element.innerHTML = element.innerHTML.replace(/&nbsp;\s&nbsp;/g, '&nbsp;');
            element.innerHTML = element.innerHTML.replace(/\s&nbsp;/g, '&nbsp;');
            element.innerHTML = element.innerHTML.replace(/&nbsp;\s/g, '&nbsp;');
            element.innerHTML = element.innerHTML.split('&nbsp;').filter(Boolean).join('&nbsp;');
            element.innerHTML += '&nbsp;';
        }
    }, []);

    const toText = useCallback(() => {
        const editor = editorRef.current;
        if (!editor) {
            return '';
        }

        let text = '';

        function addText(node: HTMLElement) {
            if (node.nodeType === Node.TEXT_NODE) {
                text += node.textContent;
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                if (node?.classList?.contains('TemplateEditor-variableChip')) {
                    text += ` {{${node.getAttribute('data-varName')}}} `;
                } else if (node.hasChildNodes()) {
                    node.childNodes.forEach((child) => addText(child as HTMLElement));
                }
            }
        }

        editor.childNodes.forEach((node) => addText(node as HTMLElement));

        return text.replace(/\s\s+/g, ' ').trim();
    }, []);

    const parseText = useCallback(
        (text: string) =>
            text
                .replace(/{{\s/g, '{{')
                .replace(/\s}}/g, '}}')
                .replace(/{{/g, ' {{')
                .replace(/}}/g, '}} ')
                .split(' ')
                .map((word) => word.replace(/\r/g, '').replace(/\n/g, ' {{newLine}} ').trim().split(' '))
                .flat()
                .map((word) => {
                    if (!word) {
                        return null;
                    }

                    if (/{{(.*)}}/.test(word)) {
                        return createChip(word.replace(/{{(.*)}}/, '$1').trim());
                    }

                    return document.createTextNode(' ' + word + ' ');
                })
                .filter(Boolean),
        [createChip],
    );

    const hydrate = useCallback(() => {
        editorRef.current?.childNodes?.forEach((child) => {
            if (child.nodeType === Node.TEXT_NODE) {
                const text = child.textContent || '';
                const nodes = parseText(text) as Node[];

                if (nodes.find((node) => node.nodeType !== Node.TEXT_NODE)) {
                    child.replaceWith(...nodes);
                    goToEnd(nodes[nodes.length - 1]);
                }
            }
        });
    }, [goToEnd, parseText]);

    const handleInput = useCallback(
        () => {
            formikContext.setFieldValue(name, toText());
            hydrate();
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [formikContext, name, toText],
    );

    const insertText = useCallback(
        (text: string, skipUpdate = false) => {
            const element = editorRef.current;
            const range = getRange();

            if (!element) {
                return;
            }

            if (range && range.startOffset !== range.endOffset) {
                range.deleteContents();
            }

            parseText(text).forEach((word) => {
                if (word) {
                    let added;
                    if (range) {
                        range.insertNode(word);
                        added = true;
                    }

                    if (!added) {
                        element.appendChild(word);
                    }
                }
            });

            normalize();
            if (!skipUpdate) {
                handleInput();
            }
        },
        [getRange, handleInput, normalize, parseText],
    );

    const handleAddVariable = useCallback(
        (varName: any) => (e: MouseEvent<HTMLElement>) => {
            e.preventDefault();
            insertText(`{{${varName}}}`);
            goToEnd();
        },
        [insertText, goToEnd],
    );

    const handleBlur = useCallback(() => {
        handleInput();
    }, [handleInput]);

    const handleClick = useCallback(
        (e: MouseEvent<HTMLElement>) => {
            const el = e.target as HTMLElement;
            if (el.getAttribute('data-varName')) {
                el.remove();
            }
            handleInput();
        },
        [handleInput],
    );

    const handleKeyDown = useCallback(
        (e: KeyboardEvent<HTMLElement>) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                insertText('{{newLine}}');
                goToEnd();
            }
        },
        [goToEnd, insertText],
    );

    const handlePaste = useCallback(
        (e: ClipboardEvent<HTMLDivElement>) => {
            e.preventDefault();
            const text = e.clipboardData.getData('text/plain');
            insertText(text);
        },
        [insertText],
    );

    useEffect(
        () => {
            if (editorRef.current) {
                editorRef.current.innerHTML = '';
            }

            if (defaultValue) {
                insertText(defaultValue);
            }
        },
        // eslint-disable-next-line
        [defaultValue],
    );

    return (
        <Root>
            <Grid container alignItems={'center'} justifyContent={'flex-start'}>
                {templateVariables.isLoading ? (
                    <CircularProgress />
                ) : (
                    templateVariables.data?.map((name, index) => (
                        <Chip key={index} label={t('variables.' + name)} onClick={handleAddVariable(name)} />
                    ))
                )}
            </Grid>
            <Paper variant={'outlined'}>
                <div
                    contentEditable
                    className={'TemplateEditor-content'}
                    ref={editorRef}
                    onPaste={handlePaste}
                    onKeyDown={handleKeyDown}
                    onBlur={handleBlur}
                    onInput={handleInput}
                    onClick={handleClick}
                />
            </Paper>
        </Root>
    );
}
