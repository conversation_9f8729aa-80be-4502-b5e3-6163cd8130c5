import { useFormikContext } from 'formik';
import React, { useCallback } from 'react';
import TextFieldClientSearchSuggestionInput, { UserModelType } from './TextFieldClientSearchSuggestionInput';

interface FormikClientAutocompleteProps {
    name: string;
    initialValue?: UserModelType;
}

export function FormikClientAutocomplete({ initialValue, name, ...rest }: FormikClientAutocompleteProps) {
    const formik = useFormikContext();

    const handleChange = useCallback((value: UserModelType) => formik.setFieldValue(name, value), [formik, name]);

    return <TextFieldClientSearchSuggestionInput initialValue={initialValue} onChanged={handleChange} {...rest} />;
}
