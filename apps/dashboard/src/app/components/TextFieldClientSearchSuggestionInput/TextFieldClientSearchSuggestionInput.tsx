import Autocomplete, { createFilterOptions } from '@mui/material/Autocomplete';
import Avatar from '@mui/material/Avatar';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListSubheader from '@mui/material/ListSubheader';
import TextField from '@mui/material/TextField';
import { styled } from '@mui/material/styles';
import debounce from 'lodash/debounce';
import * as React from 'react';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ListChildComponentProps, VariableSizeList } from 'react-window';
import { DEFAULT_USER_PHOTO_URL } from '@bookr-technologies/api/constants/UserDefaults';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useBusinessClients } from '@bookr-technologies/store/hooks/useBusinessClients';
import PageLoader from '@bookr-technologies/ui/PageLoader/PageLoader';

export interface UserModelType extends Partial<UserModel> {
    inputValue?: string;
}

interface Props {
    onChanged: (user: UserModelType) => void;
    initialValue?: UserModelType;
}

const LIST_BOX_PADDING = 8; // px

const filter = createFilterOptions<UserModelType>();

const StyledAvatar = styled(Avatar)`
    margin-right: 14px;

    &.add {
        background: ${({ theme }) => theme.palette.accent.main};
    }
`;

const StyledTextField = styled(TextField)`
    .MuiOutlinedInput-root {
        padding-top: 12px;
        padding-bottom: 0;
        font-size: 14px;
    }
`;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function useResetCache(data: any) {
    const ref = React.useRef<VariableSizeList>(null);
    React.useEffect(() => {
        if (ref.current != null) {
            ref.current.resetAfterIndex(0, true);
        }
    }, [data]);
    return ref;
}

function renderRow(props: ListChildComponentProps) {
    const { data, index, style } = props;
    const dataSet = data[index];
    const inlineStyle = {
        ...style,
        top: (style.top as number) + LIST_BOX_PADDING,
        height: 'auto',
    };

    const listProps = dataSet[0];
    const option = dataSet[1];

    if (Object.prototype.hasOwnProperty.call(dataSet, 'group')) {
        return (
            <ListSubheader key={dataSet.key} component="div" style={inlineStyle}>
                {dataSet.group}
            </ListSubheader>
        );
    }

    return (
        <ListItem {...listProps} key={option.uid} style={inlineStyle}>
            {option.inputValue ? <StyledAvatar className={'add'} /> : <StyledAvatar src={option.photoURL} />}

            <ListItemText primary={option.displayName} primaryTypographyProps={styles.primaryTypographyProps} />
        </ListItem>
    );
}

const OuterElementContext = React.createContext({});

const OuterElementType = React.forwardRef<HTMLDivElement>((props, ref) => {
    const outerProps = React.useContext(OuterElementContext);
    return <div ref={ref} {...props} {...outerProps} />;
});

const ListBoxComponent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLElement>>((props, ref) => {
    const { children, ...other } = props;
    const itemData: React.ReactChild[] = [];
    (children as React.ReactChild[]).forEach((item: React.ReactChild & { children?: React.ReactChild[] }) => {
        itemData.push(item);
        itemData.push(...(item.children || []));
    });

    const itemCount = itemData.length;
    const itemSize = 48;

    const getChildSize = (child: React.ReactChild) => {
        // if (Object.prototype.hasOwnProperty.call(child, 'group')) {
        //     return 48;
        // }
        //
        // return itemSize;
        return 48;
    };

    const getHeight = () => {
        if (itemCount > 8) {
            return 8 * itemSize;
        }
        return itemData.map(getChildSize).reduce((a, b) => a + b, 0);
    };

    const gridRef = useResetCache(itemCount);

    return (
        <div ref={ref}>
            <OuterElementContext.Provider value={other}>
                <VariableSizeList
                    itemData={itemData}
                    height={getHeight() + 2 * LIST_BOX_PADDING}
                    width="100%"
                    ref={gridRef}
                    outerElementType={OuterElementType}
                    innerElementType="ul"
                    itemSize={(index) => getChildSize(itemData[index])}
                    overscanCount={5}
                    itemCount={itemCount}
                >
                    {renderRow}
                </VariableSizeList>
            </OuterElementContext.Provider>
        </div>
    );
});

const debouncedFunc = debounce((func) => func(), 500);

export default function TextFieldClientSearchSuggestionInput({ initialValue, onChanged }: Props) {
    const { t } = useTranslation('newAppointmentDialog');
    const { t: commonT } = useTranslation('common');
    const [value, setValue] = React.useState<UserModelType | null>(initialValue || null);
    const [searchValue, setSearchValue] = useState('');
    const [clients, setClients] = useState<UserModel[]>([]);
    const { isFetching } =
        useBusinessClients(searchValue, 0, 50, {
            onSuccess: (data) => {
                setClients(data?.content);
            },
        }) || [];

    const handleFilter = useCallback(
        (options: any, params: any) => {
            const filtered = filter(options, params);
            const { inputValue } = params;
            // Suggest the creation of a new value
            const isExisting = options.some((option: UserModelType) => inputValue === option.displayName);
            if (inputValue !== '' && !isExisting && !isFetching) {
                filtered.push({
                    inputValue,
                    displayName: `${commonT('Add')} "${inputValue}"`,
                });
            }

            return filtered;
        },
        [commonT, isFetching],
    );

    const handleOnChange = useCallback(
        (event: any, newValue: any) => {
            let result;
            if (typeof newValue === 'string') {
                result = {
                    displayName: newValue,
                    uid: newValue,
                    photoURL: DEFAULT_USER_PHOTO_URL,
                };
            } else if (newValue && newValue.inputValue) {
                // Create a new value from the user input
                result = { displayName: newValue.inputValue };
            } else {
                result = newValue;
            }
            setValue(result);
            onChanged && onChanged(result);
        },
        [onChanged],
    );

    const getOptionLabel = (option: UserModelType | string) => {
        // Value selected with enter, right from the input
        if (typeof option === 'string') {
            return option;
        }
        // Add "xxx" option created dynamically
        if (option.inputValue) {
            return option.inputValue;
        }
        // Regular option
        return option.displayName || '';
    };

    return (
        <Autocomplete
            id="textfield-client-search-suggestion-input"
            freeSolo
            loading={isFetching}
            loadingText={<PageLoader />}
            value={value}
            onChange={handleOnChange}
            filterOptions={handleFilter}
            selectOnFocus
            ListboxComponent={ListBoxComponent}
            clearOnBlur
            handleHomeEndKeys
            groupBy={(option) => option.displayName?.charAt(0).toUpperCase() || '-'}
            renderGroup={(params: any) => params}
            onInputChange={(event, newInputValue) => debouncedFunc(() => setSearchValue(newInputValue))}
            options={
                isFetching
                    ? []
                    : clients.sort((a: UserModelType, b: UserModelType) =>
                          (a.displayName ?? '')
                              .trim()
                              .toUpperCase()
                              .localeCompare((b.displayName ?? '').trim().toUpperCase()),
                      )
            }
            getOptionLabel={getOptionLabel}
            renderOption={(props: any, option: any) => [props, option]}
            sx={styles.autocomplete}
            renderInput={(params) => <StyledTextField {...params} label={t('addClientName')} color={'secondary'} />}
        />
    );
}

const styles = {
    primaryTypographyProps: {
        fontWeight: 600,
    },
    autocomplete: {
        width: 445,
    },
};
