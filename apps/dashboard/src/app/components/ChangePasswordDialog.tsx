import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { signInWithEmailAndPassword, updatePassword } from 'firebase/auth';
import { Form, Formik } from 'formik';
import { t } from 'i18next';
import { useSnackbar } from 'notistack';
import React, { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import * as Yup from 'yup';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { FirebaseService } from '@bookr-technologies/core/services';
import { useFirebaseUser } from '@bookr-technologies/store/hooks/useUser';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '.MuiDialog-paper': {
        maxWidth: 920,
        width: '100%',
        borderRadius: 16,
        padding: theme.spacing(5, 2, 2),
    },
}));

const buttonStyle = {
    height: 56,
    lineHeight: '56px',
    borderRadius: 20,
    paddingLeft: 22,
    paddingRight: 22,
};

const SaveButton = styled(FormikButton)({ ...buttonStyle, minWidth: 240 });
const CancelButton = styled(Button)({
    ...buttonStyle,
    marginRight: 14,
});

const initialValues = {
    oldPassword: '',
    newPassword: '',
    passwordConfirmation: '',
};

const validationSchema = Yup.object().shape({
    oldPassword: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    newPassword: Yup.string()
        .required(t('errors.requiredField', { ns: 'common' }))
        .min(8, t('errors.minLength', { ns: 'common', minLength: 8 })),
    passwordConfirmation: Yup.string()
        .required(t('errors.requiredField', { ns: 'common' }))
        .oneOf([Yup.ref('newPassword'), null], t('errors.passwordNotMatch', { ns: 'common' })),
});

export function ChangePasswordDialog({ onClose, ...rest }: DialogProps) {
    const { t } = useTranslation('changePasswordDialog');
    const firebaseUser = useFirebaseUser();
    const { enqueueSnackbar } = useSnackbar();

    const handleClose = useCallback(() => {
        if (onClose) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (onClose as any)();
        }
    }, [onClose]);

    const handleSubmit = useCallback(
        async (values: any) => {
            if (!firebaseUser?.email) {
                enqueueSnackbar(t('notLoggedIn'), { variant: 'error' });
                return;
            }

            try {
                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
                await signInWithEmailAndPassword(FirebaseService.auth(), firebaseUser.email, values.oldPassword);
                await updatePassword(firebaseUser, values.newPassword);
                enqueueSnackbar(t('passwordChanged'), { variant: 'success' });
                handleClose();
            } catch (e) {
                const message = getErrorMessage(e, 'errorChangingPassword');
                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [enqueueSnackbar, firebaseUser, handleClose, t],
    );

    return (
        <StyledDialog onClose={handleClose} {...rest}>
            <Formik
                initialValues={initialValues}
                onSubmit={handleSubmit}
                validationSchema={validationSchema}
                validateOnMount
                validateOnChange
            >
                <Form>
                    <DialogContent>
                        <Grid container>
                            <Grid item xs>
                                <Typography variant={'h4'} fontWeight={700} mb={2}>
                                    {t('title')}
                                </Typography>
                                <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                    <Trans t={t} i18nKey={'description'} components={{ br: <br /> }} />
                                </Typography>
                            </Grid>
                            <Grid item xs container direction={'column'} maxWidth={'380px !important'}>
                                <Grid container mb={3}>
                                    <FormikTextField
                                        type={'password'}
                                        name={'oldPassword'}
                                        label={t('oldPassword')}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid container mb={3}>
                                    <FormikTextField
                                        type={'password'}
                                        name={'newPassword'}
                                        label={t('newPassword')}
                                        fullWidth
                                    />
                                </Grid>
                                <Grid container>
                                    <FormikTextField
                                        type={'password'}
                                        name={'passwordConfirmation'}
                                        label={t('passwordConfirmation')}
                                        fullWidth
                                    />
                                </Grid>
                            </Grid>
                        </Grid>
                    </DialogContent>
                    <DialogActions>
                        <Grid container alignItems={'center'} justifyContent={'flex-end'} px={3} py={2}>
                            <CancelButton onClick={handleClose}>{t('cancel')}</CancelButton>
                            <SaveButton variant={'contained'} disableElevation>
                                {t('submit')}
                            </SaveButton>
                        </Grid>
                    </DialogActions>
                </Form>
            </Formik>
        </StyledDialog>
    );
}
