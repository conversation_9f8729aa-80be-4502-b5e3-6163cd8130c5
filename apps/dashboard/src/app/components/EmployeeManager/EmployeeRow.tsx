import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import ListItem from '@mui/material/ListItem';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { alpha, styled } from '@mui/material/styles';
import classNames from 'classnames';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { UserProfile } from '@bookr-technologies/ui/UserProfile';
import { useDragElement } from '../../hooks/useDragElement';

interface EmployeeRowProps {
    image: string;
    name: string;
    displayName: string;
    isOwner: boolean;
    index: number;
    onRemove(name: string): void | Promise<void>;
    onMove(dragIndex: number, hoverIndex: number): void;
}

const Root = styled(ListItem)(({ theme }) => ({
    padding: theme.spacing(1.25, 0),
    '&.isDragging': {
        '&, & + .MuiListItemSecondaryAction-root': {
            opacity: 0,
        },
    },
    '.MuiListItemIcon-root': {
        minWidth: 40,
    },
}));

const StyledButton = styled(LoadingButton)(({ theme }) => ({
    borderRadius: 10,
    backgroundColor: alpha(theme.palette.error.main, 0.1),
    color: theme.palette.error.main,
    '&:hover': {
        backgroundColor: alpha(theme.palette.error.main, 0.2),
    },
}));

export function EmployeeRow({ image, index, displayName, name, isOwner, onRemove, onMove }: EmployeeRowProps) {
    const { t } = useTranslation('employeeManager');
    const [loading, setLoading] = useState(false);

    const { handlerId, elRef, isDragging } = useDragElement({ index, onMove });

    const handleRemove = useCallback(async () => {
        setLoading(true);
        await onRemove(name);
        setLoading(false);
    }, [name, onRemove]);

    return (
        <Root ref={elRef} className={classNames({ isDragging })} data-handler-id={handlerId}>
            <ListItemIcon>
                <DragIndicatorIcon />
            </ListItemIcon>
            <ListItemAvatar>
                <UserProfile src={image} alt={displayName} disableElevation />
            </ListItemAvatar>
            <ListItemText primary={displayName} primaryTypographyProps={{ fontWeight: 500, variant: 'subtitle1' }} />
            <ListItemSecondaryAction>
                <Grid container alignItems={'center'} justifyContent={'center'} width={120}>
                    {isOwner ? (
                        <Typography variant={'subtitle1'} fontWeight={500}>
                            {t('owner')}
                        </Typography>
                    ) : (
                        <StyledButton
                            size={'small'}
                            variant={'contained'}
                            loading={loading}
                            onClick={handleRemove}
                            fullWidth
                            disableElevation
                        >
                            {t('remove')}
                        </StyledButton>
                    )}
                </Grid>
            </ListItemSecondaryAction>
        </Root>
    );
}
