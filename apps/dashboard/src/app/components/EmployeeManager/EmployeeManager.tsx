import List from '@mui/material/List';
import { styled } from '@mui/material/styles';
import { useFormikContext } from 'formik';
import React, { useCallback, useMemo } from 'react';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { EmployeeRow } from './EmployeeRow';

interface EmployeeManagerProps {
    name: string;
    onDelete(uid: string): void | Promise<void>;
}

const Root = styled(List)({
    width: '100%',
});

export function EmployeeManager({ name, onDelete }: EmployeeManagerProps) {
    const formik = useFormikContext();
    const records = formik.getFieldMeta<UserModel[]>(name);
    const users = useMemo(() => {
        const list = records.value || [];
        return list.filter(Boolean).sort(UserModel.sort);
    }, [records.value]);

    const handleRemove = useCallback(
        async (fieldPath: string) => {
            const member = formik.getFieldMeta<UserModel>(fieldPath).value?.uid;
            if (member && onDelete) {
                await onDelete(member);
            }
            formik.setFieldValue(fieldPath, null);
            const newRecords = formik.getFieldMeta<UserModel[]>(name).value.filter(Boolean);
            formik.setFieldValue(name, newRecords);
        },
        [formik, name, onDelete],
    );

    const handleMoveCard = useCallback(
        (dragIndex: any, hoverIndex: any) => {
            const newRecords = formik.getFieldMeta<UserModel[]>(name).value.filter(Boolean);
            const dragRecord = newRecords[dragIndex];
            newRecords[dragIndex] = newRecords[hoverIndex];
            newRecords[hoverIndex] = dragRecord;

            formik.setFieldValue(
                name,
                newRecords.map((record, index) => ({ ...record, staffRank: index })),
            );
        },
        [formik, name],
    );

    return (
        <Root>
            {users.map((user, index) => (
                <EmployeeRow
                    key={user.uid}
                    image={user.photoURL}
                    displayName={user.displayName}
                    name={`${name}.${index}`}
                    isOwner={user.accountType === AccountType.BusinessOwner}
                    index={index}
                    onRemove={handleRemove}
                    onMove={handleMoveCard}
                />
            ))}
        </Root>
    );
}
