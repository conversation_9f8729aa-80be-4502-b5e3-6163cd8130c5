import CircularProgress from '@mui/material/CircularProgress';
import Grid, { GridProps } from '@mui/material/Grid';
import { ReactNode } from 'react';

interface LazyContentProps extends Omit<GridProps, 'children'> {
    loading: boolean;
    children: ReactNode | (() => ReactNode);
}

export function LazyContent({ loading, children, ...rest }: LazyContentProps) {
    if (loading) {
        return (
            <Grid container justifyContent={'center'} p={4} {...rest}>
                <CircularProgress />
            </Grid>
        );
    }

    return <>{typeof children === 'function' ? children() : children}</>;
}
