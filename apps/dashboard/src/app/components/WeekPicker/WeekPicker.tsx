import ButtonBase from '@mui/material/ButtonBase';
import TextField from '@mui/material/TextField';
import { styled } from '@mui/material/styles';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { PickersDay, PickersDayProps } from '@mui/x-date-pickers/PickersDay';
import moment, { Moment } from 'moment';
import { useCallback, useState } from 'react';
import { formatDate } from '@bookr-technologies/core/datetime';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';

type CustomPickerDayProps = PickersDayProps<Date> & {
    dayIsBetween: boolean;
    isFirstDay: boolean;
    isLastDay: boolean;
};

const CustomPickersDay = styled(PickersDay, {
    shouldForwardProp: (prop) => !['dayIsBetween', 'isFirstDay', 'isLastDay'].includes(String(prop)),
})<CustomPickerDayProps>(({ theme, dayIsBetween, isFirstDay, isLastDay }) => ({
    '&:hover': {
        backgroundColor: '#E2E2E2',
        borderRadius: 10,
    },
    '&.MuiPickersDay-today': {
        borderRadius: 10,
    },
    ...(dayIsBetween && {
        borderRadius: 0,
        backgroundColor: '#EEEEEE',
        color: theme.palette.common.black,
        '&:hover': {
            backgroundColor: '#E2E2E2',
        },
        '&:focus': {
            backgroundColor: theme.palette.secondary.main,
            color: theme.palette.common.white,
        },
        '&.Mui-selected': {
            backgroundColor: theme.palette.primary.main,
        },
    }),
    ...(isFirstDay && {
        borderTopLeftRadius: 12,
        borderBottomLeftRadius: 12,
    }),
    ...(isLastDay && {
        borderTopRightRadius: 12,
        borderBottomRightRadius: 12,
    }),
})) as React.ComponentType<CustomPickerDayProps>;

const StyledTextField = styled(TextField)({
    '.MuiOutlinedInput-root': {
        paddingTop: '8px',
        paddingBottom: '8px',
    },
    '.MuiInputBase-input': {
        fontSize: 12,
    },
    '.MuiFormLabel-root': {
        fontSize: 14,
        textTransform: 'capitalize',
    },
});

interface WeekPickerProps {
    isWeek?: boolean;
    onDateChanged: (date: Moment) => void;
}

export default function WeekPicker({ isWeek, onDateChanged }: WeekPickerProps) {
    const [value, setValue] = useState<Date | null>(new Date());
    const [open, setOpen] = useState<boolean>(false);
    const user = useCurrentUser();

    const startOfWeek = moment(value).startOf('week');
    const endOfWeek = moment(value).endOf('week');

    const renderWeekPickerDay = (
        date: Date,
        selectedDates: Array<Date | null>,
        pickersDayProps: PickersDayProps<Date>,
    ) => {
        if (!value) {
            return <PickersDay {...pickersDayProps} />;
        }

        const momentDate = moment(date);

        const dayIsBetween = !!(isWeek && momentDate.isBetween(startOfWeek, endOfWeek, 'days', '[]'));
        const isFirstDay = !!(isWeek && momentDate.isSame(startOfWeek, 'day'));
        const isLastDay = !!(isWeek && momentDate.isSame(endOfWeek, 'day'));

        return (
            <CustomPickersDay
                {...pickersDayProps}
                disableMargin
                dayIsBetween={dayIsBetween}
                isFirstDay={isFirstDay}
                isLastDay={isLastDay}
            />
        );
    };

    const handleDateChange = useCallback(
        (newDate: Date | null) => {
            setValue(newDate);
            onDateChanged && onDateChanged(moment(newDate));
        },
        [onDateChanged],
    );

    const handleOpen = useCallback((e: any) => {
        e.preventDefault();
        setOpen(true);
    }, []);
    const handleClose = useCallback(() => setOpen(false), []);

    return (
        <LocalizationProvider dateAdapter={AdapterMoment} adapterLocale={user?.language || 'en-EN'}>
            <DatePicker
                label={
                    isWeek
                        ? `${startOfWeek.format('MMM DD')} - ${endOfWeek.format('MMM DD YYYY')}`.replace(/\./g, '')
                        : formatDate(value || new Date(), 'll')
                }
                value={value}
                open={open}
                onClose={handleClose}
                onChange={handleDateChange}
                renderDay={renderWeekPickerDay}
                renderInput={(params: any) => (
                    <ButtonBase
                        component={'a'}
                        href={'#'}
                        onClick={handleOpen}
                        sx={{ borderRadius: 1.5, '&, *': { cursor: 'pointer !important' } }}
                    >
                        <StyledTextField
                            {...params}
                            disabled
                            inputProps={{ 'aria-readonly': true }}
                            sx={styles.field}
                        />
                    </ButtonBase>
                )}
            />
        </LocalizationProvider>
    );
}

const styles = {
    field: {
        '.MuiOutlinedInput-root': {
            width: 264,
            backgroundColor: '#fff',
            borderRadius: 2,
            boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
            padding: '10px 24px',
        },
        '.MuiOutlinedInput-notchedOutline': {
            border: 'none',
        },
        '.MuiFormLabel-root': {
            fontWeight: 500,
            fontSize: 16,
            color: '#545454',
        },
    },
};
