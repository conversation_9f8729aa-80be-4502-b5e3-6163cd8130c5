import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import MenuItem from '@mui/material/MenuItem';
import VStack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { AxiosError } from 'axios';
import { Formik } from 'formik';
import { useSnackbar } from 'notistack';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useAuthStore } from '@bookr-technologies/store';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import FormikSelect from '@bookr-technologies/ui/Fields/FormikSelect';

interface Props {
    open: boolean;
    onClose: () => void;
    onSave: () => void;
    user: UserModel | null;
}

export function VatInformationDialog({ user, onSave, open, onClose }: Props) {
    const { t } = useTranslation('common');
    const { enqueueSnackbar } = useSnackbar();
    const saveVatInformationMutation = useMutation(
        'saveVatInformation',
        async (vatInformation: { vatCode: string; isVatRegistered: string }) => {
            console.log('vatInformation', vatInformation);
            await usersEndpoint.updateSettings(user?.uid || '', {
                ...user?.userSettings,
                vatCode: vatInformation.vatCode,
                isVatRegistered: vatInformation.isVatRegistered === 'true',
            });
        },
        {
            onError(e) {
                enqueueSnackbar((e as AxiosError<any>).response?.data.message, {
                    variant: 'error',
                });
            },
            onSuccess() {
                useAuthStore.getState().resolveUser();
                onSave();
                onClose();
            },
        },
    );

    const handleSubmit = useCallback(
        async (values: { vatCode: string; isVatRegistered: string }) => {
            saveVatInformationMutation.mutate(values);
        },
        [saveVatInformationMutation],
    );

    if (!user) {
        return null;
    }

    return (
        <Dialog open={open} onClose={onClose} maxWidth={'md'}>
            <Formik
                initialValues={{
                    vatCode: user.userSettings.vatCode || '',
                    isVatRegistered: String(user.userSettings.isVatRegistered || 'false'),
                }}
                onSubmit={handleSubmit}
            >
                {({ values }) => (
                    <VStack>
                        <DialogTitle>{t('updateYourVatSettings')}</DialogTitle>
                        <DialogContent>
                            <FormikTextField
                                name={'vatCode'}
                                label={t('vatCode')}
                                fullWidth
                                sx={{
                                    marginBottom: 2,
                                }}
                            />
                            <Typography
                                variant={'subtitle2'}
                                color={'textSecondary'}
                                style={{ width: '100%' }}
                                pb={0.5}
                            >
                                {t('isTvaRegistered')}
                            </Typography>
                            <FormikSelect
                                label={t('isTvaRegistered')}
                                name={'isVatRegistered'}
                                variant={'filled'}
                                fullWidth
                            >
                                <MenuItem value={'true'}>{t('yes')}</MenuItem>
                                <MenuItem value={'false'}>{t('no')}</MenuItem>
                            </FormikSelect>
                        </DialogContent>
                        <DialogActions>
                            <FormikButton
                                onClick={() => handleSubmit(values)}
                                disabled={!values.vatCode}
                                size={'small'}
                                variant={'contained'}
                            >
                                {t('save')}
                            </FormikButton>
                        </DialogActions>
                    </VStack>
                )}
            </Formik>
        </Dialog>
    );
}
