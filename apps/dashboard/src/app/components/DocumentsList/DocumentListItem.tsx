import DeleteIcon from '@mui/icons-material/Delete';
import DescriptionIcon from '@mui/icons-material/Description';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';

const StyledGrid = styled(Grid)`
    .fileDetailsContainer:hover {
        cursor: pointer;
        text-decoration: underline;
    }

    .fileName {
        color: ${({ theme }) => theme.palette.grey[500]};
    }

    .deleteIcon {
        color: ${({ theme }) => theme.palette.grey['400']};
    }

    .deleteIcon:hover {
        width: 100%;
    }
`;

interface DocumentListItemProps {
    fileName: string;
    handleDownload: (fileName: string) => void;
    handleRemove: (fileName: string) => void;
}

export function DocumentListItem({ fileName, handleDownload, handleRemove }: DocumentListItemProps) {
    return (
        <StyledGrid container alignItems={'center'}>
            <Grid
                item
                xs={11}
                container
                alignItems={'center'}
                className={'fileDetailsContainer'}
                onClick={() => handleDownload(fileName)}
            >
                <DescriptionIcon />
                <Typography className={'fileName'} variant={'body1'} fontWeight={500} ml={1}>
                    {fileName}
                </Typography>
            </Grid>
            <Grid item xs={1} container alignItems={'center'} justifyContent={'center'}>
                <IconButton className={'deleteIcon'} onClick={() => handleRemove(fileName)}>
                    <DeleteIcon />
                </IconButton>
            </Grid>
        </StyledGrid>
    );
}
