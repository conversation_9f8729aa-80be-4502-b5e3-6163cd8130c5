import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { downloadDocument } from '@bookr-technologies/api/utils/downloadDocument';
import { PageLoader } from '@bookr-technologies/ui/PageLoader/PageLoader';
import { useFilePicker } from '@bookr-technologies/ui/hooks/useFilePicker';
import { DocumentListItem } from './DocumentListItem';

const StyledStack = styled(Stack)`
    height: 500px;
    overflow-y: auto;
    border-radius: 16px;
    padding: ${({ theme }) => theme.spacing(1.5, 2, 1.5, 2.75)};
    margin-bottom: ${({ theme }) => theme.spacing(3)};
    position: relative;
    background: #eee;
`;

const StyledNoDocumentsContainer = styled('div')`
    height: 500px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 20px;

    .headerText {
        height: 60px;
        width: 400px;
        font-size: 24px;
        font-weight: bold;
        font-family: Plus Jakarta Display, sans-serif;
        color: #1f1f1f;
        text-align: center;
    }

    .uploadButton {
        width: 200px;
        height: 40px;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 500;
        color: ${({ theme }) => theme.palette.common.white};
        background: #111111;
    }
`;

const StyledHeaderContainer = styled('div')`
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: space-between;

    .addDocument {
        font-size: 14px;
        font-weight: 600;
        color: ${({ theme }) => theme.palette.accent.main};
    }

    .addDocument:hover {
        cursor: pointer;
    }
`;

interface DocumentsListProps {
    clientId: string;
}

export function DocumentsList({ clientId }: DocumentsListProps) {
    const { t } = useTranslation('clientPage');
    const { enqueueSnackbar } = useSnackbar();
    const {
        data: documents,
        isLoading,
        isError,
        refetch,
    } = useQuery(`/${clientId}/documents`, () => businessClientsEndpoint.getDocuments(clientId), {
        retry: 1,
        onError: () => enqueueSnackbar(t('documentsListError'), { variant: 'error' }),
    });

    const filePicker = useFilePicker({
        accept: 'image/*, .pdf, .txt, .doc, .docx, .xml, application/msword, application/vnd. openxmlformats-officedocument. wordprocessingml.document',
        async onChange([file]: File[]) {
            await handleUploadDocument(file);
        },
    });

    const handleUploadDocument = async (file: File) => {
        await businessClientsEndpoint.uploadDocument(clientId, file);
        await refetch();
    };

    const handleDownloadDocument = async (fileName: string) => {
        const data = await businessClientsEndpoint.downloadDocument(clientId, fileName);
        downloadDocument(data, fileName);
    };

    const handleRemoveDocument = async (fileName: string) => {
        await businessClientsEndpoint.deleteDocument(clientId, fileName);
        await refetch();
    };

    if (isLoading) {
        return <PageLoader />;
    }

    if (isError || !documents) {
        return null;
    }

    if (documents && documents.length === 0) {
        return (
            <StyledNoDocumentsContainer>
                <Typography className={'headerText'}>{t('noDocuments')}</Typography>
                <Button className={'uploadButton'} onClick={filePicker.handleClick}>
                    {t('uploadDocuments')}
                </Button>
            </StyledNoDocumentsContainer>
        );
    }

    const DocumentItems = documents.map((document) => (
        <DocumentListItem
            key={document}
            fileName={document.substring(document.lastIndexOf('/') + 1)}
            handleDownload={handleDownloadDocument}
            handleRemove={handleRemoveDocument}
        />
    ));

    return (
        <>
            <StyledHeaderContainer>
                <Typography
                    variant={'h5'}
                    component={'h2'}
                    lineHeight={'32px'}
                    marginBottom={'24px'}
                    fontWeight={'bold'}
                >
                    {t('clientsDocuments')}
                </Typography>
                {DocumentItems.length > 0 && (
                    <div className={'addDocument'} onClick={filePicker.handleClick}>
                        {t('addDocument')}
                    </div>
                )}
            </StyledHeaderContainer>
            {DocumentItems.length > 0 && (
                <StyledStack display={'flex'} flexDirection={'column'} spacing={2}>
                    {DocumentItems}
                </StyledStack>
            )}
        </>
    );
}
