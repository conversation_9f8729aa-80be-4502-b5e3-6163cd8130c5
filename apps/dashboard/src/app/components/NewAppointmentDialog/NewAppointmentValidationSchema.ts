import { t } from 'i18next';
import moment from 'moment';
import * as Yup from 'yup';

export const newAppointmentValidationSchema = Yup.object().shape({
    client: Yup.object()
        .shape({
            displayName: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
        })
        .required(t('errors.requiredField', { ns: 'common' })),
    dateTime: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    service: Yup.number().required(t('errors.requiredField', { ns: 'common' })),
    staffId: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
});

export const initialValues = {
    client: { email: '', displayName: '', callingCode: '+40', phoneNumber: '', uid: '' },
    dateTime: moment().valueOf(),
};
