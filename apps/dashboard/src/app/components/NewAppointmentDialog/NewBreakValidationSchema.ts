import { t } from 'i18next';
import moment from 'moment';
import * as Yup from 'yup';

export const newBreakValidationSchema = Yup.object().shape({
    title: Yup.string().nullable(),
    fromDateTime: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    toDateTime: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
});

export const initialValues = {
    title: '',
    fromDateTime: moment().valueOf(),
    toDateTime: moment().valueOf(),
};
