/* eslint-disable @typescript-eslint/no-non-null-assertion */
import Box from '@mui/material/Box';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import FormControl from '@mui/material/FormControl';
import Grid from '@mui/material/Grid';
import InputLabel from '@mui/material/InputLabel';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AxiosError } from 'axios';
import { Form, Formik } from 'formik';
import moment from 'moment';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { emitCustomEvent } from 'react-custom-events';
import { useTranslation } from 'react-i18next';
import { AccountType } from '@bookr-technologies/api/constants/AccountType';
import { DEFAULT_USER_PHOTO_URL } from '@bookr-technologies/api/constants/UserDefaults';
import { BookNowRequest, BookNowResponse } from '@bookr-technologies/api/dto/BookNowDTO';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { PaymentMethod } from '@bookr-technologies/api/models/PaymentMethod';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useBusinessStore } from '@bookr-technologies/store/businessStore';
import { useUser } from '@bookr-technologies/store/hooks/useUser';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';
import { CountrySelectVariant, FormikCountrySelect } from '@bookr-technologies/ui/CountrySelect';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { FormikCheckbox } from '@bookr-technologies/ui/Fields/FormikCheckbox';
import { FormikDateTimePicker } from '@bookr-technologies/ui/Fields/FormikDateTimePicker';
import { CustomEvent } from '../../enums/CustomEvent';
import { useBookNowMutation } from '../../utils/appointments';
import { NewAppointmentSuccess } from '../NewAppointmentSuccess/NewAppointmentSuccess';
import { FormikClientAutocomplete } from '../TextFieldClientSearchSuggestionInput/FormikClientAutocomplete';
import { initialValues, newAppointmentValidationSchema } from './NewAppointmentValidationSchema';
import { SelectField } from './SelectField';

interface Props {
    client?: UserModel;
    timestamp?: number;
    staffId?: string;
}

export function NewAppointmentTab({ client, timestamp, staffId }: Props) {
    const { t } = useTranslation('common');
    const { t: profileSettingsT } = useTranslation('profileSettingsPage');
    const user = useUser();
    const { enqueueSnackbar } = useSnackbar();
    const confirm = useConfirmation();
    const bookNowMutation = useBookNowMutation();
    const [appointment, setAppointment] = useState<AppointmentModel>();
    const business = useBusinessStore((state) => state.business);
    const fetchCurrentBusiness = useBusinessStore((state) => state.fetchCurrentUserBusiness);

    const getServicesForUser = (userModel: UserModel) => userModel.services.sort(ServiceModel.sort);

    const services = getServicesForUser(
        staffId ? business?.staffMembers?.find((staff) => staff.uid === staffId) || user : user,
    );

    const handleOnSubmit = useCallback(
        async (values: any) => {
            const request: BookNowRequest = {
                client: {
                    uid: values.client.uid || null,
                    email: values.client.email || '',
                    accountType: AccountType.Client,
                    photoURL: DEFAULT_USER_PHOTO_URL,
                    ...values.client,
                    phoneNumber: values.client.phoneNumber
                        ? (values.client.callingCode || '+40') + values.client.phoneNumber
                        : '',
                },
                inviteClient: false,
                staffId: values.staffId,
                serviceId: values.service,
                timestamp: Math.floor(moment(values.dateTime).valueOf() / 1000),
                paymentMethod: values.paymentOnline ? PaymentMethod.CARD : PaymentMethod.CASH,
                recurrenceStrategy: '',
                timestampEnd: 0,
            };

            const data = await appointmentsEndpoint.isTimeslotFree(
                request.staffId,
                request.timestamp,
                request.serviceId,
            );

            if (data.free) {
                bookNowMutation.mutate(request);
            } else {
                const { appointment } = data;
                const result = await confirm({
                    title: t('slotExpired'),
                    message: t('slotExpiredMessage', {
                        start: moment(appointment.dateTime).format('HH:mm'),
                        end: moment(appointment.dateTime).add(appointment.service.duration, 'minutes').format('HH:mm'),
                        displayName: appointment.client.displayName,
                    }),
                });

                if (result) {
                    bookNowMutation.mutate(request);
                }
            }
        },
        [bookNowMutation, confirm, t],
    );

    useEffect(() => {
        if (bookNowMutation.isSuccess) {
            const { appointment, error, success } = bookNowMutation.data as BookNowResponse;
            if (success) {
                setAppointment(appointment);
                emitCustomEvent(CustomEvent.REFETCH_CALENDAR_EVENTS);
            } else if (error) {
                enqueueSnackbar(error, { variant: 'error' });
            }
        } else if (bookNowMutation.isError) {
            enqueueSnackbar((bookNowMutation.error as AxiosError<any>).response?.data.message, { variant: 'error' });
        }
    }, [bookNowMutation, enqueueSnackbar]);

    useEffect(() => {
        if (!business) {
            fetchCurrentBusiness();
        }
    }, [business, fetchCurrentBusiness]);

    if (!business) {
        return null;
    }

    if (appointment) {
        return <NewAppointmentSuccess appointment={appointment} />;
    }

    return (
        <Formik
            initialValues={{
                ...initialValues,
                dateTime: timestamp ? timestamp : initialValues.dateTime,
                client: client || initialValues.client,
                staffId: staffId || user.uid,
                service: services && services.length > 0 ? services[0].id : null,
            }}
            onSubmit={handleOnSubmit}
            validationSchema={newAppointmentValidationSchema}
            validateOnMount
        >
            {({ values, setFieldValue }) => (
                <Form className={'form'}>
                    <DialogContent className={'content'}>
                        <Typography variant={'h6'} component={'div'} className={'section-title'}>
                            {t('client')}
                        </Typography>

                        <Stack spacing={3} alignItems={'flex-start'} width={445}>
                            <FormikClientAutocomplete initialValue={client || initialValues.client} name={'client'} />
                            {!values.client?.uid && (
                                <>
                                    <FormikTextField
                                        name="client.email"
                                        id="name"
                                        margin="dense"
                                        label={t('clientEmail')}
                                        type="email"
                                        fullWidth
                                        variant="outlined"
                                        color="secondary"
                                    />
                                    <Stack direction={'row'} spacing={3} width={'100%'}>
                                        <Grid item xs maxWidth={'140px !important'}>
                                            <FormikCountrySelect
                                                name={'client.callingCode'}
                                                label={t('callingCodeField')}
                                                variant={CountrySelectVariant.CallingCode}
                                                color={'secondary'}
                                            />
                                        </Grid>
                                        <Grid item xs>
                                            <FormikTextField
                                                name={'client.phoneNumber'}
                                                label={t('phoneNumberField')}
                                                fullWidth
                                                color={'secondary'}
                                            />
                                        </Grid>
                                    </Stack>
                                </>
                            )}
                        </Stack>
                        <Box sx={{ pt: 4 }} />
                        <Typography variant={'h6'} component={'div'} className={'section-title'}>
                            {t('appointment')}
                        </Typography>
                        <Stack spacing={3} alignItems={'flex-start'} width={445}>
                            <SelectField
                                name={'staffId'}
                                label={t('selectStaffMember')}
                                options={business.staffMembers.map(({ uid, displayName, photoURL }) => ({
                                    id: uid,
                                    name: displayName,
                                    photoURL,
                                }))}
                                color={'secondary'}
                                onChange={(staffId) => {
                                    const services = getServicesForUser(
                                        business.staffMembers.find((s) => s.uid === staffId)!,
                                    );
                                    if (services && services.length > 0) {
                                        setFieldValue('service', services[0].id);
                                    }
                                }}
                            />
                            <LocalizationProvider dateAdapter={AdapterMoment} locale={user?.language || 'en-EN'}>
                                <FormikDateTimePicker name={'dateTime'} />
                            </LocalizationProvider>
                            {services?.length > 0 ? (
                                <FormControl fullWidth>
                                    <InputLabel id="service-select-label">{t('selectService')}</InputLabel>
                                    <SelectField
                                        name={'service'}
                                        label={t('selectService')}
                                        labelId={'services-select-label'}
                                        style={{ paddingTop: 16, paddingBottom: 4 }}
                                        color={'secondary'}
                                        options={(user.uid === values.staffId
                                            ? user
                                            : business.staffMembers.find((s) => s.uid === values.staffId)!
                                        ).services.map(({ id, name, duration, color }) => ({
                                            id,
                                            name: `${name} - ${duration} mins`,
                                            color,
                                        }))}
                                    />
                                </FormControl>
                            ) : (
                                <Link href={'/settings/profile/services'} underline={'hover'} color={'accent.main'}>
                                    {profileSettingsT('newService')}
                                </Link>
                            )}
                            {services?.find((s) => s.id === values.service)?.acceptsOnlinePayments ? (
                                <FormikCheckbox name={'paymentOnline'} label={t('paymentOnline')} color={'accent'} />
                            ) : null}
                        </Stack>
                    </DialogContent>
                    <DialogActions className={'footer'}>
                        <FormikButton loading={bookNowMutation.isLoading} variant={'contained'}>
                            {t('addNewAppointment')}
                        </FormikButton>
                    </DialogActions>
                </Form>
            )}
        </Formik>
    );
}
