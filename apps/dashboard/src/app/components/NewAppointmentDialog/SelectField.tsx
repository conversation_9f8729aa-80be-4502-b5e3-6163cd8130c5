import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectProps } from '@mui/material/Select';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useFormikContext } from 'formik';
import { useCallback } from 'react';

export interface SelectFieldProps extends Omit<SelectProps, 'name' | 'onChange' | 'value'> {
    name: string;
    options: { id: number | string; name: string; color?: string; photoURL?: string }[];
    onChange?: (id: string | number) => void;
}

const StyledSelect = styled(Select)({
    borderRadius: 16,
});

export function SelectField({ options, onChange, onBlur, ...rest }: SelectFieldProps) {
    const formik = useFormikContext();
    const meta = formik.getFieldMeta(rest.name);

    const handleChange = useCallback(
        (event: any) => {
            formik.handleChange(event);
            onChange && onChange(event.target.value);
        },
        [onChange, formik],
    );

    const handleBlur = useCallback(
        (event: any) => {
            formik.handleBlur(event);
            if (onBlur) {
                onBlur(event);
            }
        },
        [formik, onBlur],
    );

    return (
        <StyledSelect
            onChange={handleChange}
            onBlur={handleBlur}
            value={meta.value}
            error={meta.touched && !!meta.error}
            size={'small'}
            variant={'outlined'}
            fullWidth
            {...rest}
        >
            {options.map((option) => (
                <MenuItem key={option.id} value={option.id}>
                    <Stack
                        direction={'row'}
                        spacing={2}
                        minHeight={46}
                        alignItems={'center'}
                        justifyContent={'flex-start'}
                        py={option.photoURL ? 1 : 0}
                    >
                        {option.photoURL && <Avatar src={option.photoURL} />}
                        {option.color && <Box width={12} height={12} borderRadius={'6px'} bgcolor={option.color} />}
                        <Typography variant={'body1'} fontWeight={500}>
                            {option.name}
                        </Typography>
                    </Stack>
                </MenuItem>
            ))}
        </StyledSelect>
    );
}
