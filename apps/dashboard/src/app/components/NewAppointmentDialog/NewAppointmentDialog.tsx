import CloseIcon from '@mui/icons-material/Close';
import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Dialog from '@mui/material/Dialog';
import IconButton from '@mui/material/IconButton';
import Slide from '@mui/material/Slide';
import Tab from '@mui/material/Tab';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { TransitionProps } from '@mui/material/transitions';
import * as React from 'react';
import { PropsWithChildren } from 'react';
import { useTranslation } from 'react-i18next';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { TabPanel } from '@bookr-technologies/ui/TabPanel';
import { StyledTabs } from '../Layout/SettingsLayout';
import { NewAppointmentTab } from './NewAppointmentTab';
import { NewBreakTab } from './NewBreakTab';

interface NewAppointmentDialogProps {
    handleCloseDialog: () => void;
    isOpen: boolean;
    client?: UserModel;
    timestamp?: number;
    staffId?: string;
}

const Transition = React.forwardRef(({ children, ...rest }: PropsWithChildren<TransitionProps>, ref) => (
    <Slide direction="left" ref={ref} {...rest}>
        {/* eslint-disable-next-line */}
        {children as any}
    </Slide>
));

const StyledDialog = styled(Dialog)`
    .MuiDialog-container {
        justify-content: flex-end;
        align-items: stretch;
        height: 100vh;
    }

    .MuiDialog-paper {
        margin: 0;
        min-width: 545px;
        max-height: 100%;
    }

    .appbar {
        background: #eee;
        position: relative;
        box-shadow: none;
        color: #000;

        .MuiToolbar-regular {
            padding-left: 50px;
        }

        .MuiTypography-h6 {
            margin: 0;
            font-weight: 600;
            font-size: 18px;
            line-height: 30px;
        }
    }

    .content {
        align-items: flex-start;
        background: #fff;
        border-radius: 16px;
        padding-right: 25px;
        padding-left: 45px;

        .section-title {
            font-weight: 600;
            font-size: 16px;
            line-height: 24px;
            letter-spacing: 0.005em;
            margin-bottom: 18px;
        }
    }

    .footer {
        justify-content: flex-start;
        padding-left: 50px;
        padding-right: 50px;
        padding-bottom: 67px;
    }

    .form {
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        height: 100%;
    }

    .tab {
        background: #eee;
        display: flex;
        flex-direction: column;
        flex: 1 1 auto;
        padding: 0;

        .box {
            background: #fff;
            border-radius: 16px;
            height: 100%;
        }
    }
`;

const StyledTabWrapper = styled(Box)`
    padding-left: 24px;
    background: #eeeeee;
    div:first-child {
        border: 0;
    }
`;

export default ({ isOpen, handleCloseDialog, client, timestamp, staffId, ...rest }: NewAppointmentDialogProps) => {
    const { t } = useTranslation('common');
    const [value, setValue] = React.useState(0);

    const handleChange = (event: React.SyntheticEvent, newValue: number) => {
        setValue(newValue);
    };

    return (
        <StyledDialog open={isOpen} onClose={handleCloseDialog} TransitionComponent={Transition} {...rest}>
            <AppBar className={'appbar'}>
                <Toolbar>
                    <Typography sx={{ ml: 2, flex: 1 }} variant="h6" component="div">
                        {t('create')}
                    </Typography>
                    <IconButton edge="start" color="inherit" onClick={handleCloseDialog} aria-label="close">
                        <CloseIcon />
                    </IconButton>
                </Toolbar>
            </AppBar>
            <StyledTabWrapper>
                <StyledTabs value={value} onChange={handleChange} color={'secondary'}>
                    <Tab disableRipple label={t('appointment')} color={'secondary'} />
                    <Tab disableRipple label={t('break')} />
                </StyledTabs>
            </StyledTabWrapper>
            <TabPanel value={value} index={0}>
                <NewAppointmentTab client={client} timestamp={timestamp} staffId={staffId} />
            </TabPanel>
            <TabPanel value={value} index={1}>
                <NewBreakTab timestamp={timestamp} staffId={staffId} />
            </TabPanel>
        </StyledDialog>
    );
};
