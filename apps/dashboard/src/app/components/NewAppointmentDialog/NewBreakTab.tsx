import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import DialogContent from '@mui/material/DialogContent';
import List from '@mui/material/List/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AxiosError } from 'axios';
import { Form, Formik } from 'formik';
import moment from 'moment';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useCallback, useEffect } from 'react';
import { emitCustomEvent } from 'react-custom-events';
import { useTranslation } from 'react-i18next';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { useAuthStore } from '@bookr-technologies/store';
import { useUser } from '@bookr-technologies/store/hooks/useUser';
import { FormikButton } from '@bookr-technologies/ui/Fields';
import { FormikDateTimePicker } from '@bookr-technologies/ui/Fields/FormikDateTimePicker';
import { FormikTextField } from '@bookr-technologies/ui/Fields/FormikTextField';
import { CustomEvent } from '../../enums/CustomEvent';
import { deleteBreak, useCreateBreakMutation } from '../../utils/breaks';
import { getTimezoneOffsetForDate } from '../../utils/dateUtils';
import { initialValues, newBreakValidationSchema } from './NewBreakValidationSchema';

interface NewBreakTabProps {
    staffId?: string;
    timestamp?: number;
}

export function NewBreakTab({ staffId, timestamp }: NewBreakTabProps) {
    const { t } = useTranslation('common');
    const user = useUser();
    const { enqueueSnackbar } = useSnackbar();
    const createBreakMutation = useCreateBreakMutation();

    const handleOnSubmit = useCallback(
        async (values: any) => {
            const m1 = getTimezoneOffsetForDate(moment(values.fromDateTime));
            const m2 = getTimezoneOffsetForDate(moment(values.toDateTime));

            const request: Partial<BreakModel> = {
                title: values.title,
                fromDateTime: moment(values.fromDateTime).add(m1, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                toDateTime: moment(values.toDateTime).add(m2, 'minutes').format('YYYY-MM-DDTHH:mm:ss.SSS[Z]'),
                staffUid: staffId || user?.uid,
            };

            createBreakMutation.mutate(request);
        },
        [createBreakMutation, staffId, user?.uid],
    );

    useEffect(() => {
        if (createBreakMutation.isSuccess) {
            useAuthStore.getState().resolveUser();
            emitCustomEvent(CustomEvent.REFETCH_CALENDAR_EVENTS);
            enqueueSnackbar(t('breakCreatedSuccessfully'), { variant: 'success' });
            createBreakMutation.reset();
        } else if (createBreakMutation.isError) {
            enqueueSnackbar((createBreakMutation.error as AxiosError<any>).response?.data.message, {
                variant: 'error',
            });
        }
    }, [createBreakMutation, enqueueSnackbar, t, user]);

    const handleDeleteBreak = async (breakId: number) => {
        try {
            await deleteBreak(breakId);
            useAuthStore.getState().resolveUser();
            emitCustomEvent(CustomEvent.REFETCH_CALENDAR_EVENTS);
        } catch (err) {
            const { response } = err as AxiosError<any>;
            enqueueSnackbar(response?.data.message, { variant: 'error' });
        }
    };

    return (
        <Formik
            initialValues={{
                title: initialValues.title,
                fromDateTime: timestamp ? moment(timestamp).valueOf() : initialValues.fromDateTime,
                toDateTime: timestamp ? moment(timestamp).valueOf() : initialValues.toDateTime,
            }}
            onSubmit={handleOnSubmit}
            validationSchema={newBreakValidationSchema}
            validateOnMount
        >
            {() => (
                <DialogContent className={'content'}>
                    <Form>
                        <Typography variant={'h6'} component={'div'} className={'section-title'}>
                            {t('breakTitle')}:
                        </Typography>

                        <FormikTextField name={'title'} label={''} fullWidth size={'small'} />

                        <Box pt={3} />

                        <Typography variant={'h6'} component={'div'} className={'section-title'}>
                            {t('from')}:
                        </Typography>

                        <LocalizationProvider dateAdapter={AdapterMoment} locale={user?.language || 'en-EN'}>
                            <FormikDateTimePicker name={'fromDateTime'} label={''} />
                        </LocalizationProvider>

                        <Box pt={3} />

                        <Typography variant={'h6'} component={'div'} className={'section-title'}>
                            {t('to')}:
                        </Typography>

                        <LocalizationProvider dateAdapter={AdapterMoment} locale={user?.language || 'en-EN'}>
                            <FormikDateTimePicker name={'toDateTime'} label={''} />
                        </LocalizationProvider>

                        <FormikButton variant={'contained'} sx={{ mt: 3 }}>
                            {t('addNewBreak')}
                        </FormikButton>
                    </Form>

                    <Box pt={6} />

                    <Typography variant={'h6'} component={'div'} className={'section-title'}>
                        {t('yourBreaks')}
                    </Typography>

                    <List sx={{ pt: 0 }}>
                        {user.breaks.map((b) => (
                            <ListItem
                                key={b.id}
                                disableGutters
                                secondaryAction={
                                    <Button
                                        onClick={() => handleDeleteBreak(b.id)}
                                        sx={{ textTransform: 'none', color: '#F05253' }}
                                    >
                                        {t('delete')}
                                    </Button>
                                }
                            >
                                <ListItemText
                                    primary={b.title || 'Break'}
                                    secondary={`${moment(b.fromDateTime).format('DD MMM YYYY, HH:mm')} - ${moment(
                                        b.toDateTime,
                                    ).format('DD MMM YYYY, HH:mm')}`}
                                    primaryTypographyProps={{ fontSize: '16px', fontWeight: 500 }}
                                    secondaryTypographyProps={{ color: '#AFAFAF', fontSize: '14px' }}
                                />
                            </ListItem>
                        ))}
                    </List>
                </DialogContent>
            )}
        </Formik>
    );
}
