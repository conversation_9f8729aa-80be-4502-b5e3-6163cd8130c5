import Avatar from '@mui/material/Avatar';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectProps } from '@mui/material/Select';
import { styled } from '@mui/material/styles';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useBusinessStore } from '@bookr-technologies/store/businessStore';
import { AllMembers } from './AllMembers';

export const StyledStaffMember = styled('div')`
    display: flex;
    justify-content: center;
    align-items: center;

    .photoURL {
        width: 30px;
        height: 30px;
        margin-right: 14px;
    }

    .all-staff-members-icon {
        color: ${({ theme }) => theme.palette.accent.main};
    }
`;

export interface StaffMemberSelectProps extends SelectProps {
    onStaffMemberChanged: (staff: UserModel | null) => void;
    staffMember?: UserModel | null;
}

export function StaffMemberSelect({ onStaffMemberChanged, staffMember, ...rest }: StaffMemberSelectProps) {
    const { t } = useTranslation('common');
    const business = useBusinessStore((state) => state.business);
    const [member, setMember] = useState<UserModel | null>(staffMember ?? null);

    useEffect(() => {
        setMember(staffMember ?? null);
    }, [staffMember]);

    if (!business) {
        return null;
    }

    const options = business.staffMembers;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const handleChange = (event: any) => {
        const staff = options.find((staff) => staff.uid === event.target.value) || null;
        setMember(staff);
        onStaffMemberChanged && onStaffMemberChanged(staff);
    };

    return (
        <Select
            labelId={'staff-select'}
            id={'staff-select'}
            variant={'filled'}
            value={member?.uid || 'all'}
            label={t('allStaffMembers')}
            placeholder={t('allStaffMembers')}
            onChange={handleChange}
            {...rest}
        >
            <MenuItem value={'all'}>
                <AllMembers />
            </MenuItem>
            {business.staffMembers.map((staff) => (
                <MenuItem key={staff.uid} value={staff.uid}>
                    <StyledStaffMember>
                        <Avatar src={staff.photoURL} className={'photoURL'} />
                        {staff.displayName}
                    </StyledStaffMember>
                </MenuItem>
            ))}
        </Select>
    );
}
