import GroupsIcon from '@mui/icons-material/Groups';
import { useTranslation } from 'react-i18next';
import { StyledStaffMember } from './StaffMemberSelect';

export function AllMembers() {
    const { t } = useTranslation('common');

    return (
        <StyledStaffMember>
            <GroupsIcon className={'photoURL all-staff-members-icon'} />
            {t('allStaffMembers')}
        </StyledStaffMember>
    );
}
