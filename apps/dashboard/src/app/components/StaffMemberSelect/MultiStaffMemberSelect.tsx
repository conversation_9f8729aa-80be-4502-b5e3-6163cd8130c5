/* eslint-disable @typescript-eslint/no-explicit-any */
import Avatar from '@mui/material/Avatar';
import AvatarGroup from '@mui/material/AvatarGroup';
import Grid from '@mui/material/Grid';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import { ReactElement, ReactNode, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { Nullable, Optional } from '@bookr-technologies/core/types';
import { useBusinessStore } from '@bookr-technologies/store/businessStore';
import { UserProfile } from '@bookr-technologies/ui/UserProfile';
import { AllMembers } from './AllMembers';
import { StaffMemberSelectProps, StyledStaffMember } from './StaffMemberSelect';

interface MultiStaffMemberSelectProps extends Omit<StaffMemberSelectProps, 'onStaffMemberChanged' | 'staffMember'> {
    onStaffMemberChanged: (staff: Nullable<UserModel[]>) => void;
    staffMember?: Nullable<string | UserModel[]>;
}

function normalizeStaffMember(staffMember: Optional<UserModel | UserModel[] | string | string[]>): string[] {
    if (staffMember) {
        const items = Array.isArray(staffMember) ? staffMember : [staffMember];

        return items.map((item) => (typeof item === 'string' ? item : item.uid));
    }

    return [];
}

export function MultiStaffMemberSelect({ onStaffMemberChanged, staffMember, ...rest }: MultiStaffMemberSelectProps) {
    const { t } = useTranslation('common');
    const business = useBusinessStore((state) => state.business);
    const [member, setMember] = useState<string[]>(() => normalizeStaffMember(staffMember));

    useEffect(() => {
        setMember(normalizeStaffMember(staffMember));
    }, [staffMember]);

    if (!business) {
        return null;
    }

    const options = business.staffMembers;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const handleChange = (event: any, item: ReactNode) => {
        let staffMembers: UserModel[] = [];

        if ((item as any)?.props?.value !== 'all') {
            staffMembers = event.target.value
                .map((value: string) => options.find((staff) => staff.uid === value))
                .filter(Boolean);
        }

        setMember(staffMembers.map((staff) => staff.uid));
        onStaffMemberChanged && onStaffMemberChanged(staffMembers);
    };

    return (
        <Select
            multiple
            labelId={'staff-select'}
            id={'staff-select'}
            variant={'filled'}
            value={member && member.length > 0 ? member : ['all']}
            label={t('allStaffMembers')}
            placeholder={t('allStaffMembers')}
            onChange={handleChange}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            renderValue={(values: any) => {
                if (values.length === 0 || (values.includes('all') && values.length === 1)) {
                    return <AllMembers />;
                }

                const staffMembers = (values as string[]).map((value: string) =>
                    options.find((staff) => staff.uid === value),
                );

                if (staffMembers.length === 1) {
                    return (
                        <StyledStaffMember>
                            <Avatar src={staffMembers[0]?.photoURL} className={'photoURL'} />
                            {staffMembers[0]?.displayName}
                        </StyledStaffMember>
                    );
                }

                return (
                    <Grid container alignItems={'center'}>
                        <AvatarGroup
                            total={staffMembers.length}
                            sx={{ height: 44, alignItems: 'center' }}
                            max={3}
                            componentsProps={{
                                additionalAvatar: {
                                    sx: {
                                        width: 30,
                                        height: 30,
                                        fontSize: 14,
                                    },
                                },
                            }}
                        >
                            {staffMembers
                                .map(
                                    (value) =>
                                        value && (
                                            <UserProfile
                                                key={value.uid}
                                                size={30}
                                                src={value.photoURL}
                                                alt={value.displayName}
                                            />
                                        ),
                                )
                                .filter(Boolean)}
                        </AvatarGroup>

                        {staffMembers
                            .map((value) => value && value.displayName.split(' ')[0])
                            .slice(0, 3)
                            .filter(Boolean)
                            .join(', ')}

                        {staffMembers.length > 3 && ', ...'}
                    </Grid>
                );
            }}
            {...rest}
        >
            <MenuItem value={'all'}>
                <AllMembers />
            </MenuItem>
            {business.staffMembers.map((staff) => (
                <MenuItem key={staff.uid} value={staff.uid}>
                    <StyledStaffMember>
                        <Avatar src={staff.photoURL} className={'photoURL'} />
                        {staff.displayName}
                    </StyledStaffMember>
                </MenuItem>
            ))}
        </Select>
    );
}
