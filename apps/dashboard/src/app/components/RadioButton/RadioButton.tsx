import Radio, { RadioProps } from '@mui/material/Radio';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { useCallback } from 'react';

interface RadioButtonProps extends RadioProps {
    label: string;
    text?: string;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    onChange: (value: any) => void;
}

const StyledWrapper = styled(Stack)`
    border: 1px solid #afafaf;
    border-radius: 12px;
    transition: border 500ms ease-out;

    &:hover {
        border: 1px solid #000;
    }

    &.selected {
        border: 1px solid #2f80fb;
    }
`;

const StyledLabel = styled('span')`
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0.005em;
    color: #000000;
    margin-top: 0 !important;
    margin-left: 30px !important;
`;

const StyledLabelWithText = styled('span')`
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0.005em;
    color: #000000;
    margin-top: 0 !important;
`;

function RadioButton({ label, text, value, checked, onChange, ...rest }: RadioButtonProps) {
    const handleChange = useCallback(() => onChange && onChange(value), [onChange, value]);
    return (
        <StyledWrapper
            onClick={handleChange}
            display={'flex'}
            alignItems={'center'}
            spacing={10}
            flexDirection={'row'}
            p={1}
            my={1}
            width={'50%'}
            className={checked ? 'selected' : ''}
        >
            {text ? (
                <div style={{ display: 'flex', alignItems: 'row', alignSelf: 'flex-start' }}>
                    <Radio
                        checked={checked}
                        value={value}
                        color={checked ? 'secondary' : 'default'}
                        {...rest}
                        style={{ alignSelf: 'baseline' }}
                    />
                    <div>
                        <StyledLabelWithText>{label}</StyledLabelWithText>
                        <Typography variant={'body1'} fontWeight={500} color={'textSecondary'}>
                            {text}
                        </Typography>
                    </div>
                </div>
            ) : (
                <React.Fragment>
                    <Radio checked={checked} value={value} color={checked ? 'secondary' : 'default'} {...rest} />
                    <StyledLabel>{label}</StyledLabel>
                </React.Fragment>
            )}
        </StyledWrapper>
    );
}

export default RadioButton;
