import LoadingButton from '@mui/lab/LoadingButton';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { alpha, styled } from '@mui/material/styles';
import { useCallback } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { logger } from 'react-query/types/react/logger';
import { useNavigate } from 'react-router-dom';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { SubscriptionModel } from '@bookr-technologies/api/models/SubscriptionModel';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { formatDate } from '@bookr-technologies/core/datetime';
import { useAuthStore } from '@bookr-technologies/store';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';
import { LoadingPlaceholder } from '@bookr-technologies/ui/LoadingPlaceholder';
import atmBanner from '@bookr-technologies/ui/assets/banners/atmBanner.jpg';

const Root = styled(Grid)(({ theme }) => ({
    backgroundColor: '#111',
    '.MuiTypography-root': {
        color: '#fff',
        '&.CurrentPlan-textSecondary': {
            color: '#afafaf',
        },
    },
    '.CurrentPlan-cta': {
        backgroundColor: '#fff',
        color: '#111',
        minWidth: 164,
        padding: theme.spacing(1, 3),
        marginRight: theme.spacing(2),
        '&:last-child': {
            marginRight: 0,
        },
        '&.CurrentPlan-ctaCancel': {
            backgroundColor: '#545454',
            color: '#fff',
        },

        '&.MuiLoadingButton-loading': {
            backgroundColor: '#ccc',
            color: 'transparent',
        },
    },
    '.CurrentPlan-trialBadge': {
        backgroundColor: alpha(theme.palette.accent.main, 0.2),
        color: theme.palette.accent.main,
        padding: theme.spacing(0.25, 1.25),
        borderRadius: 6,
        marginLeft: theme.spacing(1.25),
    },
    '.CurrentPlan-banner, .CurrentPlan-bannerImage': {
        userSelect: 'none',
    },
    '.CurrentPlan-bannerImage': {
        width: '100%',
        height: 'auto',
    },
}));

export function CurrentPlan() {
    const { t } = useTranslation('subscriptionCard');
    const subscription = useAuthStore((state) => state.subscription);
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const cancelSubscription = useMutation('business/subscription/cancel', (subscriptionId: string) =>
        businessEndpoint.cancelSubscription(subscriptionId),
    );

    const isTrial = SubscriptionModel.isTrial(subscription);
    const nextPayment = subscription?.nextPaymentDate;
    const canUpgrade = true;
    const canCancel = true;
    const loading = false;

    const confirm = useConfirmation();
    const navigate = useNavigate();

    const handleCancel = useCallback(async () => {
        if (!subscription?.stripeSubscriptionId) {
            navigate('/subscription/plans/select', { replace: true });
            return;
        }

        const result = await confirm({
            title: t('cancelSubscriptionTitle'),
            message: t('cancelSubscriptionMessage'),
            isDestructiveAction: true,
        });

        if (result) {
            await cancelSubscription.mutate(subscription?.stripeSubscriptionId);
            await resolveUser();
            navigate('/subscription/plans/select', { replace: true });
        }
    }, [cancelSubscription, confirm, navigate, resolveUser, subscription?.stripeSubscriptionId, t]);

    const handleUpgrade = useCallback(async () => {
        if (!subscription?.stripeSubscriptionId) {
            navigate('/subscription/plans/select', { replace: true });
            return;
        }

        const result = await confirm({
            title: <Trans i18nKey={'upgradeSubscriptionTitle'} t={t} components={{ b: <b /> }} />,
            message: <Trans i18nKey={'upgradeSubscriptionMessage'} t={t} components={{ b: <b /> }} />,
            submitText: t('upgradePlan'),
        });

        if (result) {
            await handleCancel();
        }
    }, [confirm, handleCancel, navigate, subscription?.stripeSubscriptionId, t]);

    return (
        <Root className={'CurrentPlan-header'} container direction={'row'} px={8}>
            <Grid item xs container flexGrow={1} className={'CurrentPlan-planDetails'} direction={'column'} py={6}>
                <Grid container direction={'column'} flexGrow={1}>
                    <Typography variant={'caption'} className={'CurrentPlan-textSecondary'} fontWeight={500}>
                        {t('currentPlan')}
                    </Typography>
                    <Grid container alignItems={'center'} justifyContent={'flex-start'} mb={1}>
                        <LoadingPlaceholder loading={loading} width={[100, 140]} height={42} light>
                            <Typography variant={'h4'} fontWeight={700}>
                                {SubscriptionPlanModel.getPlanName(subscription?.subscriptionPlan)}
                            </Typography>
                            {isTrial ? (
                                <Typography variant={'caption'} className={'CurrentPlan-trialBadge'} fontWeight={500}>
                                    {t('trial')}
                                </Typography>
                            ) : null}
                        </LoadingPlaceholder>
                    </Grid>
                    <LoadingPlaceholder loading={loading} width={[180, 260]} height={24} light>
                        <Typography variant={'body2'} fontWeight={500} maxWidth={264}>
                            {SubscriptionPlanModel.getPlanDescription(subscription?.subscriptionPlan)}
                        </Typography>
                    </LoadingPlaceholder>
                    <LoadingPlaceholder loading={loading} width={[100, 180]} height={24} light />
                </Grid>

                <Grid container direction={'column'} alignItems={'flex-start'} justifyContent={'flex-start'}>
                    <LoadingPlaceholder variant={'text'} loading={loading} width={[180, 260]} light sx={{ mb: 2.5 }}>
                        {nextPayment ? (
                            <Typography
                                variant={'caption'}
                                className={'CurrentPlan-textSecondary'}
                                fontWeight={500}
                                mb={2.5}
                            >
                                {t('nextPayment', { date: formatDate(nextPayment, 'll') })}
                            </Typography>
                        ) : null}
                    </LoadingPlaceholder>

                    <Grid container alignItems={'center'} justifyContent={'flex-start'} flexWrap={'nowrap'}>
                        <LoadingPlaceholder
                            variant={'rectangular'}
                            loading={loading}
                            light
                            width={[164, 220]}
                            height={40}
                            sx={{ marginRight: 2, borderRadius: 2.5 }}
                        >
                            {canUpgrade ? (
                                <LoadingButton
                                    className={'CurrentPlan-cta'}
                                    variant={'contained'}
                                    size={'small'}
                                    onClick={handleUpgrade}
                                    loading={cancelSubscription.isLoading}
                                    disabled={cancelSubscription.isLoading}
                                >
                                    {t('upgradePlan')}
                                </LoadingButton>
                            ) : null}
                        </LoadingPlaceholder>

                        {!cancelSubscription.isLoading ? (
                            <LoadingPlaceholder
                                variant={'rectangular'}
                                loading={loading}
                                light
                                width={[164, 220]}
                                height={40}
                                sx={{ marginRight: 2, borderRadius: 2.5 }}
                            >
                                {canCancel ? (
                                    <Button
                                        className={'CurrentPlan-cta CurrentPlan-ctaCancel'}
                                        variant={'contained'}
                                        size={'small'}
                                        onClick={handleCancel}
                                    >
                                        {t('cancelSubscription')}
                                    </Button>
                                ) : null}
                            </LoadingPlaceholder>
                        ) : null}
                    </Grid>
                </Grid>
            </Grid>

            <Box
                className={'CurrentPlan-banner'}
                pt={6}
                justifyContent={'flex-end'}
                alignItems={'flex-end'}
                display={'inline-flex'}
            >
                <img src={atmBanner} alt="ATM Banner" className={'CurrentPlan-bannerImage'} />
            </Box>
        </Root>
    );
}
