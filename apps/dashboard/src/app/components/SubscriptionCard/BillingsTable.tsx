import Link from '@mui/material/Link';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { SubscriptionBillingModel } from '@bookr-technologies/api/models/SubscriptionBillingModel';
import { SubscriptionPlanModel } from '@bookr-technologies/api/models/SubscriptionPlanModel';
import { formatDate } from '@bookr-technologies/core/datetime';
import { LoadingPlaceholder } from '@bookr-technologies/ui/LoadingPlaceholder';

interface Props {
    items: SubscriptionBillingModel[];
    loading?: boolean;
}

const Root = styled(TableContainer)(({ theme }) => ({
    '.MuiTableCell-root': {
        border: 'none',
        fontWeight: 500,
        fontSize: 14,
        lineHeight: '24px',
        color: '#111',
        padding: theme.spacing(1, 0, 2),

        '&.MuiTableCell-head': {
            fontSize: 12,
            lineHeight: '20px',
            color: '#AFAFAF',
            paddingTop: 0,
            paddingBottom: theme.spacing(1),
            textTransform: 'uppercase',
        },
    },
}));

export function BillingsTable({ items, loading }: Props) {
    const { t } = useTranslation('subscriptionCard');

    const list = loading ? new Array(3).fill('').map((_, id) => new SubscriptionBillingModel(String(id))) : items;

    return (
        <Root>
            <Table>
                <TableHead>
                    <TableRow>
                        <TableCell variant={'head'}>{t('date')}</TableCell>
                        <TableCell variant={'head'}>{t('planType')}</TableCell>
                        <TableCell variant={'head'}>{t('planPrice')}</TableCell>
                        <TableCell variant={'head'}>{t('invoiceLink')}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {list.map((item) => (
                        <TableRow key={item.id}>
                            <TableCell variant={'body'}>
                                <LoadingPlaceholder loading={!!loading} dark width={152}>
                                    {formatDate(item.startsAt, 'LLL')}
                                </LoadingPlaceholder>
                            </TableCell>
                            <TableCell variant={'body'}>
                                <LoadingPlaceholder loading={!!loading} dark width={[30, 90]}>
                                    {SubscriptionPlanModel.getPlanName(item.subscriptionPlan)}
                                </LoadingPlaceholder>
                            </TableCell>
                            <TableCell variant={'body'}>
                                <LoadingPlaceholder loading={!!loading} dark width={[30, 90]}>
                                    {SubscriptionBillingModel.getPrice(item)}
                                </LoadingPlaceholder>
                            </TableCell>
                            <TableCell variant={'body'}>
                                <LoadingPlaceholder loading={!!loading} dark width={[30, 90]}>
                                    {item.hostedInvoiceUrl ? (
                                        <Link
                                            href={item.hostedInvoiceUrl}
                                            color={'accent.main'}
                                            target={'_blank'}
                                            rel={'noreferrer,noopener'}
                                        >
                                            {t('viewInvoice')}
                                        </Link>
                                    ) : null}
                                </LoadingPlaceholder>
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Root>
    );
}
