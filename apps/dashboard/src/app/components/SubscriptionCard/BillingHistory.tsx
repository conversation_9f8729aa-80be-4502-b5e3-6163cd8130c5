import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { BillingsTable } from './BillingsTable';

const Root = styled(Grid)(({ theme }) => ({
    '.BillingHistory-noDataBox': {
        padding: theme.spacing(4),
    },
}));

export function BillingHistory() {
    const { t } = useTranslation('subscriptionCard');

    const billings = useQuery('business/subscription/billings', () => businessEndpoint.getSubscriptionBillings());
    const items = billings.data?.items || [];
    const loading = billings.isLoading;

    return (
        <Root className={'BillingHistory-root'} container direction={'column'} px={8} pt={4} pb={6}>
            <Typography variant={'h5'} mb={3} fontWeight={'700'}>
                {t('billingHistory')}
            </Typography>
            <BillingsTable loading={loading} items={items} />
            {!loading && items.length === 0 ? <NoDataState /> : null}
        </Root>
    );
}

function NoDataState() {
    const { t } = useTranslation('subscriptionCard');

    return (
        <Grid
            container
            className={'BillingHistory-noDataBox'}
            direction={'column'}
            alignItems={'center'}
            justifyContent={'center'}
        >
            <Typography variant={'subtitle1'} color={'textSecondary'} fontWeight={600}>
                {t('noBillings')}
            </Typography>
            <Typography variant={'body2'} color={'textSecondary'} fontWeight={500}>
                {t('noBillingsDescription')}
            </Typography>
        </Grid>
    );
}
