import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import moment from 'moment';
import * as React from 'react';
import { emitCustomEvent } from 'react-custom-events';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import blueCheckmark from '@bookr-technologies/ui/assets/blueCheckmark.png';
import { CustomEvent } from '../../enums/CustomEvent';

interface Props {
    appointment: AppointmentModel;
}

const StyledRowDetails = styled(Typography)`
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.005em;
    color: #757575;
`;

const StyledTitle = styled(Typography)`
    width: 60%;
    font-family: 'Plus Jakarta Display', Poppins, serif;
    font-weight: bold;
    font-size: 24px;
    line-height: 31px;
    text-align: center;
    letter-spacing: 0.0025em;
    color: #1f1f1f;
`;

const StyledImage = styled('img')`
    width: 70px;
    height: 70px;
`;

const Row = ({ title = '', value = '' }) => (
    <Stack alignItems={'center'} justifyContent={'space-between'} direction={'row'} width={'100%'}>
        <StyledRowDetails>{title}:</StyledRowDetails>
        <StyledRowDetails>{value}</StyledRowDetails>
    </Stack>
);

export function NewAppointmentSuccess({ appointment }: Props) {
    const { t } = useTranslation('common');
    const navigate = useNavigate();

    const handleOpenAppointment = () => {
        emitCustomEvent(CustomEvent.HIDE_SHOW_NEW_APPOINTMENT_DIALOG);
        navigate(`/appointments/${appointment.id}`);
    };

    return (
        <>
            <DialogContent className={'content'}>
                <Stack spacing={2} alignItems={'center'} justifyContent={'center'} mb={'80px'} mt={'130px'}>
                    <StyledImage src={blueCheckmark} alt={'Confirmation'} />
                    <StyledTitle>{t('bookingMadeSuccessfully')}</StyledTitle>
                </Stack>
                <Typography variant={'h6'} component={'div'} className={'section-title'}>
                    {t('appointmentDetails')}
                </Typography>
                <Stack spacing={1} alignItems={'flex-start'} width={445} pt={'12px'}>
                    <Row title={t('clientName')} value={appointment.client.displayName} />
                    <Row title={t('serviceName')} value={appointment.service.name} />
                    <Row title={t('dateAndTime')} value={moment(appointment.dateTime).format('DD MMM, HH:mm')} />
                </Stack>
            </DialogContent>
            <DialogActions className={'footer'}>
                <Box mt={8} width={'100%'}>
                    <Button variant={'contained'} fullWidth onClick={handleOpenAppointment}>
                        {t('openAppointment')}
                    </Button>
                </Box>
            </DialogActions>
        </>
    );
}
