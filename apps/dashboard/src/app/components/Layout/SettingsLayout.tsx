import Paper from '@mui/material/Paper';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import { styled } from '@mui/material/styles';
import React, { PropsWithChildren, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import { PageProps, Page } from '../Page';

export interface SettingsLayoutProps extends PageProps {
    noPaper?: boolean;
}

const TabsMap: Record<string, { code: string; path: string }[]> = {
    business: [
        { code: 'general', path: '/settings/business/general' },
        { code: 'categories', path: '/settings/business/categories' },
        { code: 'gallery', path: '/settings/business/gallery' },
        { code: 'workingHours', path: '/settings/business/working-hours' },
        { code: 'staff', path: '/settings/business/staff' },
        { code: 'virtualTour', path: '/settings/business/virtual-tour' },
        { code: 'notifications', path: '/settings/business/notifications' },
        { code: 'integrations', path: '/settings/business/integrations' },
    ],
    profile: [
        { code: 'general', path: '/settings/profile/general' },
        { code: 'services', path: '/settings/profile/services' },
        { code: 'payments', path: '/settings/profile/payments' },
        { code: 'workingHours', path: '/settings/profile/working-hours' },
    ],
    application: [
        { code: 'general', path: '/settings/application/general' },
        { code: 'subscription', path: '/settings/application/subscription' },
        { code: 'messages', path: '/settings/application/messages' },
        { code: 'others', path: '/settings/application/others' },
    ],
};

export const StyledTabs = styled(Tabs)(({ theme }) => ({
    borderBottom: '1px solid #cbcbcb',

    '.MuiTab-root': {
        fontWeight: 600,
        fontSize: 16,
        lineHeight: '24px',
        textTransform: 'none',
        padding: theme.spacing(2, 3),
        color: '#afafaf',
        '&.Mui-selected': {
            color: theme.palette.accent.main,
        },
    },

    '.MuiTabs-indicator': {
        backgroundColor: theme.palette.accent.main,
        height: 5,
    },
}));

export const StyledPaper = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3, 4),
    marginTop: theme.spacing(2),
    backgroundColor: '#fff',
    boxShadow: '0 2px 6px rgba(5, 16, 55, 0.1)',
    borderRadius: 16,
}));

export function SettingsLayout({ children, noPaper, ...rest }: PropsWithChildren<SettingsLayoutProps>) {
    const { t } = useTranslation('settingsLayout');
    const location = useLocation();
    const tabs = useMemo(() => {
        const category = location.pathname.split('/')[2] ?? 'business';
        return TabsMap[category] ?? [];
    }, [location.pathname]);

    const content = tabs.map(({ code, path }) => (
        <div key={path} role={'tabpanel'} id={`tabpanel-${code}`} aria-labelledby={`tab-${code}`}>
            {path === location.pathname && children}
        </div>
    ));

    return (
        <Page {...rest}>
            <StyledTabs value={location.pathname} color={'accent'} variant={'scrollable'}>
                {tabs.map(({ code, path }) => (
                    <Tab
                        key={path}
                        component={Link}
                        to={path}
                        id={`tab-${code}`}
                        aria-controls={`tabpanel-${code}`}
                        label={t(`tabs.${code}`)}
                        value={path}
                    />
                ))}
            </StyledTabs>
            {noPaper ? content : <StyledPaper>{content}</StyledPaper>}
        </Page>
    );
}
