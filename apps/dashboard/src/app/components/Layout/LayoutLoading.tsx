import Box from '@mui/material/Box';
import LinearProgress from '@mui/material/LinearProgress';
import { useState } from 'react';
import { useCustomEventListener } from 'react-custom-events';
import { CustomEvent } from '../../enums/CustomEvent';

export function LayoutLoading() {
    const [isLoaderVisible, setLoaderVisible] = useState(false);

    useCustomEventListener(CustomEvent.SHOW_HIDE_PAGE_LOADER, (visible: boolean) => setLoaderVisible(visible));

    return <Box className={'pageLoader'}>{isLoaderVisible && <LinearProgress color={'secondary'} />}</Box>;
}
