import Box, { BoxProps } from '@mui/material/Box';
import Typography, { TypographyProps } from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React, { PropsWithChildren } from 'react';

const Root = styled(Box)({
    '.MuiFormLabel-root': {
        fontWeight: 600,
        fontSize: 14,
        lineHeight: '24px',
        color: '#757575',
        marginBottom: 4,
        display: 'block',
    },
});

interface SettingsLayoutSectionProps extends BoxProps {
    title: string;
    titleProps?: TypographyProps;
}

export function SettingsLayoutSection({
    children,
    title,
    titleProps = {},
    ...rest
}: PropsWithChildren<SettingsLayoutSectionProps>) {
    return (
        <Root {...rest}>
            <Typography
                variant={'h5'}
                fontWeight={600}
                fontSize={24}
                lineHeight={'32px'}
                color={'#333'}
                {...titleProps}
            >
                {title}
            </Typography>
            {children}
        </Root>
    );
}
