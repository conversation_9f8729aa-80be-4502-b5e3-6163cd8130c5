import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import React, { ElementType, isValidElement, Suspense } from 'react';
import { Outlet } from 'react-router-dom';
import { useAuthStore, usePageStore } from '@bookr-technologies/store';
import { CookieBanner } from '@bookr-technologies/ui/CookieBanner/CookieBanner';
import PageLoader from '@bookr-technologies/ui/PageLoader/PageLoader';
import { LayoutHeader } from './LayoutHeader';
import { LayoutLoading } from './LayoutLoading';
import { LayoutSidebar, SIDEBAR_WIDTH } from './LayoutSidebar';

interface LayoutProps {
    LoadingPageComponent?: ElementType;
    ContentComponent?: ElementType;
    noSidebar?: boolean;
    noHeader?: boolean;
}

const StyledRoot = styled('div', { name: 'StyledRoot' })({
    display: 'flex',
    minHeight: '100vh',
    width: '100%',
    position: 'relative',

    '.Layout-content': {
        display: 'flex',
        flex: '1 1 auto',
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'flex-start',
        maxWidth: `calc(100% - ${SIDEBAR_WIDTH}px)`,

        '&.noSidebar': {
            maxWidth: '100%',
        },
    },

    '.pageLoader': {
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
    },
});

export function Layout({ noHeader, noSidebar, ContentComponent, LoadingPageComponent = PageLoader }: LayoutProps) {
    const fullscreen = usePageStore((state) => state.fullscreen);
    const authenticated = useAuthStore((state) => state.authenticated);
    const disableSidebar = authenticated === null || fullscreen || noSidebar;
    const disableHeader = authenticated === null || fullscreen || noHeader;

    return (
        <StyledRoot>
            <LayoutLoading />
            <LayoutSidebar hide={disableSidebar} />
            <main className={classNames('Layout-content', { noSidebar: disableSidebar })}>
                <LayoutHeader hide={disableHeader} />
                <Suspense fallback={<LoadingPageComponent />}>
                    {ContentComponent ? <ContentComponent /> : <Outlet />}
                </Suspense>
            </main>
            <CookieBanner />
        </StyledRoot>
    );
}
