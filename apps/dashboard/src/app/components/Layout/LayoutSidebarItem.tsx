import ArrowDropDownRoundedIcon from '@mui/icons-material/ArrowDropDownRounded';
import ArrowDropUpRoundedIcon from '@mui/icons-material/ArrowDropUpRounded';
import Collapse from '@mui/material/Collapse';
import List from '@mui/material/List';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import classNames from 'classnames';
import React, { Children, ElementType, PropsWithChildren, ReactNode, useCallback, useEffect, useMemo } from 'react';
import {
    LinkProps,
    matchPath,
    resolvePath,
    useLocation,
    useMatch,
    useNavigate,
    useResolvedPath,
} from 'react-router-dom';

interface Props {
    to: LinkProps['to'];
    label: ReactNode;
    icon: ElementType;
}

export function LayoutSidebarItem({ to, label, children, icon: Icon }: PropsWithChildren<Props>) {
    const resolved = useResolvedPath(to);
    const match = useMatch({ path: resolved.pathname + '/*' });
    const location = useLocation();
    const navigate = useNavigate();
    const [active, setActive] = React.useState(false);

    const hasActiveChildren = useMemo(() => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const filtered = Children.map(children, (child: any) => {
            if (child && child.props.to) {
                const resolvedPath = resolvePath(child.props.to);
                if (!matchPath({ path: resolvedPath.pathname + '/*' }, location.pathname)) {
                    return null;
                }

                return child;
            }
        })?.filter(Boolean);

        return filtered && filtered.length > 0;
    }, [children, location.pathname]);

    useEffect(() => {
        if (hasActiveChildren) {
            setActive(true);
        }
    }, [match, hasActiveChildren]);

    const handleClick = useCallback(() => {
        if (children) {
            setActive((value) => !value);
            return;
        }
        navigate(to);
    }, [navigate, to, children]);

    return (
        <>
            <ListItemButton onClick={handleClick} className={classNames({ active: active || match })}>
                <ListItemIcon>
                    <Icon className={'sidebarListIcon'} />
                </ListItemIcon>

                <ListItemText primary={label} />
                {children ? active ? <ArrowDropUpRoundedIcon /> : <ArrowDropDownRoundedIcon /> : null}
            </ListItemButton>
            {children ? (
                <Collapse in={active} timeout="auto" unmountOnExit>
                    <List component={'div'} disablePadding className={'sidebarList'}>
                        {children}
                    </List>
                </Collapse>
            ) : null}
        </>
    );
}
