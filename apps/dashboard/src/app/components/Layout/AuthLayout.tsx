import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { styled, Theme } from '@mui/material/styles';
import { SxProps } from '@mui/system/styleFunctionSx';
import classNames from 'classnames';
import React, { PropsWithChildren, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import lightBookrLogo from '@bookr-technologies/ui/assets/lightBookrLogo.svg';

interface Props {
    image?: string;
    backTo?: string;
    title?: string;
    description?: string;
    loading?: boolean;
    rightSideElement?: React.ReactNode;
    sx?: SxProps<Theme>;
}

const Root = styled('div')({
    display: 'flex',
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    '.authLayout__content': {
        flex: '1 1 auto',
        padding: '48px',
        display: 'flex',
        flexDirection: 'column',
    },
    '.authLayout__side': {
        backgroundColor: '#000',
        display: 'flex',
        alignItems: 'flex-end',
        justifyContent: 'center',
        width: '520px',
        position: 'relative',
        overflow: 'hidden',
        '&.white': {
            backgroundColor: '#fff',
        },
    },
    '.authLayout__side__image': {
        backgroundColor: '#000',
        marginBottom: -2,
        width: '100%',
        height: '100%',
        position: 'absolute',
        objectFit: 'cover',
        zIndex: 1,
    },
    '.authLayout__brand': {
        display: 'block',
        position: 'absolute',
        zIndex: 3,
        top: 128,
        left: 44,
    },
    '.authLayout__header': {
        marginBottom: 24,
    },
    '.authLayout__backTo': {
        width: 50,
        height: 50,
        border: '1px solid #afafaf',
        '&, *': {
            borderRadius: 16,
        },
    },
    '.authLayout__details': {
        marginBottom: 50,
        maxWidth: '560px',
    },
    '.authLayout__title': {
        fontWeight: '700',
        lineHeight: '44px',
        marginBottom: '16px',
    },
    '.authLayout__description': {
        fontSize: '18px',
        fontWeight: '500',
        lineHeight: '30px',
        color: '#afafaf',
    },
});

export function AuthLayout({
    children,
    image,
    title,
    description,
    backTo,
    rightSideElement,
    sx,
    loading,
}: PropsWithChildren<Props>) {
    const navigate = useNavigate();
    const handleClick = useCallback(() => {
        if (backTo) {
            navigate(backTo);
        }
    }, [backTo, navigate]);

    if (loading) {
        return null;
    }

    return (
        <Root sx={sx}>
            <main className={'authLayout__content'}>
                {backTo ? (
                    <header className={'authLayout__header'}>
                        <IconButton className={'authLayout__backTo'} onClick={handleClick}>
                            <ArrowBackIcon />
                        </IconButton>
                    </header>
                ) : null}
                <div className={'authLayout__details'}>
                    {title && (
                        <Typography component={'h1'} variant={'h4'} className={'authLayout__title'}>
                            {title}
                        </Typography>
                    )}
                    {description && (
                        <Typography
                            component={'h2'}
                            variant={'subtitle1'}
                            color={'textSecondary'}
                            className={'authLayout__description'}
                        >
                            {description}
                        </Typography>
                    )}
                </div>
                {children}
            </main>
            <aside className={classNames('authLayout__side', { white: !!rightSideElement })}>
                {rightSideElement ? (
                    rightSideElement
                ) : (
                    <>
                        <a href={'/'} className={'authLayout__brand'}>
                            <img
                                src={lightBookrLogo}
                                height={40}
                                alt={'Bookr'}
                                className={'authLayout__brand__image'}
                            />
                        </a>
                        {image ? <img src={image} alt={'Bookr'} className={'authLayout__side__image'} /> : null}
                    </>
                )}
            </aside>
        </Root>
    );
}
