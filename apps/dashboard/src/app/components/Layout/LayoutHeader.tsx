import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Slide from '@mui/material/Slide';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore, usePageStore } from '@bookr-technologies/store';
import { UserProfileDropdown } from '@bookr-technologies/ui/UserProfileDropdown';
import { NewAppointmentButton } from '../NewAppointmentButton';

interface Props {
    hide?: boolean;
}

const StyledRoot = styled('header')(({ theme }) => ({
    width: '100%',
    padding: theme.spacing(3, 0, 4),
    '.headerHeadline': {
        color: '#333',
        fontWeight: 700,
    },
    '.headerSubHeadline': {
        fontWeight: 700,
        color: '#afafaf',
        marginTop: theme.spacing(0.5),
    },
    '.headerCaption': {
        fontWeight: 500,
        color: '#757575',
    },
    '.rightContent': {
        minHeight: theme.spacing(6.25),
    },
}));

const StyledBackButton = styled(IconButton)(({ theme }) => ({
    width: theme.spacing(6.25),
    marginRight: theme.spacing(1.75),
    border: '1px solid #afafaf',
    '&, & .MuiTouchRipple-root *': {
        borderRadius: '16px !important',
    },
}));

export function LayoutHeader({ hide = false }: Props) {
    const navigate = useNavigate();
    const user = useAuthStore((state) => state.user);
    const title = usePageStore((state) => state.title);
    const subtitle = usePageStore((state) => state.subtitle);
    const backToHref = usePageStore((state) => state.backToHref);
    const backToLabel = usePageStore((state) => state.backToLabel);

    const hasBackAction = Boolean(backToHref || backToLabel);
    const handleBack = useCallback(() => {
        if (backToHref) {
            return navigate(backToHref);
        }
    }, [navigate, backToHref]);

    return (
        <Slide in={!hide} unmountOnExit exit={false}>
            <StyledRoot>
                <Container>
                    <Grid container alignItems={'flex-start'}>
                        <Grid item xs container alignItems={'center'}>
                            {hasBackAction && (
                                <StyledBackButton onClick={handleBack} size={'large'}>
                                    <ArrowBackIcon />
                                </StyledBackButton>
                            )}
                            <Box flexGrow={1}>
                                {title && (
                                    <Typography component={'h1'} variant={'h4'} className={'headerHeadline'}>
                                        {title}
                                    </Typography>
                                )}
                                {subtitle && (
                                    <Typography component={'h2'} variant={'body1'} className={'headerSubHeadline'}>
                                        {subtitle}
                                    </Typography>
                                )}
                                {backToLabel && (
                                    <Typography component={'p'} variant={'caption'} className={'headerCaption'}>
                                        {backToLabel}
                                    </Typography>
                                )}
                            </Box>
                        </Grid>
                        <Grid
                            item
                            xs
                            container
                            alignItems={'center'}
                            justifyContent={'flex-end'}
                            className={'rightContent'}
                        >
                            <Box marginRight={4}>
                                <NewAppointmentButton />
                            </Box>
                            <UserProfileDropdown
                                size={50}
                                image={user?.photoURL}
                                fullName={user?.displayName ?? ''}
                                phone={user?.phoneNumber ?? ''}
                            />
                        </Grid>
                    </Grid>
                </Container>
            </StyledRoot>
        </Slide>
    );
}
