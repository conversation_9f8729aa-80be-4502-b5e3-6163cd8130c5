import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import EventNoteIcon from '@mui/icons-material/EventNote';
import FavoriteIcon from '@mui/icons-material/Favorite';
import HistoryIcon from '@mui/icons-material/History';
import PersonIcon from '@mui/icons-material/Person';
import SettingsIcon from '@mui/icons-material/Settings';
import StoreIcon from '@mui/icons-material/Store';
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircle';
import WidgetsIcon from '@mui/icons-material/Widgets';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Slide from '@mui/material/Slide';
import { styled } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import lightLogo from '@bookr-technologies/ui/assets/lightBookrLogo.svg';
import { LayoutSidebarItem } from './LayoutSidebarItem';

export const SIDEBAR_WIDTH = 240;

interface Props {
    hide?: boolean;
}

const StyledRoot = styled('aside', { name: 'StyledRoot' })(({ theme }) => ({
    display: 'flex',
    backgroundColor: '#111',
    width: SIDEBAR_WIDTH,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    paddingBottom: theme.spacing(5),
    maxHeight: '100vh',
    overflowY: 'auto',
    position: 'sticky',
    top: 0,
    '& .brand': {
        display: 'flex',
        width: '100%',
        padding: theme.spacing(5, 4),
    },
    '& .sidebarList, & .sidebarList .MuiListItemButton-root, & .sidebarListIcon': {
        color: '#afafaf',
        transition: theme.transitions.create('color'),
    },
    '& .MuiListItemButton-root.active, & .MuiListItemButton-root.active .sidebarListIcon': {
        color: '#fff',
    },
    '& .MuiListItemButton-root:hover, & .MuiListItemButton-root:hover .sidebarListIcon': {
        color: '#fff',
    },
    '& .sidebarList': {
        width: '100%',
    },
    '& .sidebarList .MuiListItemButton-root': {
        width: '100%',
        padding: theme.spacing(1.5, 1.5, 1.5, 4),
    },
    '& .sidebarList .sidebarList .MuiListItemButton-root': {
        paddingLeft: theme.spacing(7),
    },
    '& .MuiListItemIcon-root': {
        minWidth: theme.spacing(4.25),
    },
    '& .MuiListItemText-root': {
        margin: 0,
    },
    '& .MuiListItemText-primary': {
        fontSize: 16,
        fontWeight: 600,
        lineHeight: '24px',
    },
}));

export function LayoutSidebar({ hide = false }: Props) {
    const { t } = useTranslation('layoutSidebar');

    return (
        <Slide in={!hide} unmountOnExit direction={'right'} exit={false}>
            <StyledRoot>
                <Link className={'brand'} to={'/'}>
                    <img src={lightLogo} alt={'Bookr'} height={27} width={120} className={'brandImage'} />
                </Link>

                <List className={'sidebarList'}>
                    <LayoutSidebarItem to={'/appointments'} label={t('calendar')} icon={EventNoteIcon} />
                    <LayoutSidebarItem to={'/clients'} label={t('clients')} icon={SupervisedUserCircleIcon} />
                    <LayoutSidebarItem to={'/sales'} label={t('sales.sales')} icon={AttachMoneyIcon}>
                        <LayoutSidebarItem to={'/sales/overview'} label={t('sales.overview')} icon={AttachMoneyIcon} />
                        <LayoutSidebarItem to={'/sales/activity'} label={t('sales.activity')} icon={HistoryIcon} />
                    </LayoutSidebarItem>
                    <LayoutSidebarItem to={'/settings'} label={t('settings.settings')} icon={SettingsIcon}>
                        <LayoutSidebarItem to={'/settings/business'} label={t('settings.business')} icon={StoreIcon} />
                        <LayoutSidebarItem to={'/settings/profile'} label={t('settings.profile')} icon={PersonIcon} />
                        <LayoutSidebarItem
                            to={'/settings/application'}
                            label={t('settings.application')}
                            icon={WidgetsIcon}
                        />
                    </LayoutSidebarItem>
                </List>

                <Box flexGrow={1} />

                <List className={'sidebarList'}>
                    <LayoutSidebarItem to={'/feedback'} label={t('feedback')} icon={FavoriteIcon} />
                </List>
            </StyledRoot>
        </Slide>
    );
}
