import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { formatNumber } from '@bookr-technologies/core/utils/formatters';
import { getInitials } from '@bookr-technologies/ui/hooks/useInitials';

interface ReadOnlyServicesManagerProps {
    staffMembers: UserModel[];
    selectedStaffMembers: UserModel[];
}

const Root = styled(List)({
    width: '100%',
});

const StyledListItem = styled(ListItem)(({ theme }) => ({
    border: `1px solid ${theme.palette.divider}`,
    borderRadius: theme.spacing(1),
    marginBottom: theme.spacing(1),
    backgroundColor: theme.palette.background.paper,
    '&:hover': {
        backgroundColor: theme.palette.action.hover,
    },
}));

const ServiceColorIndicator = styled('div')<{ color: string }>(({ color }) => ({
    width: 16,
    height: 16,
    borderRadius: '50%',
    backgroundColor: color,
    marginRight: 12,
    flexShrink: 0,
}));

const StaffMemberChip = styled(Chip)(({ theme }) => ({
    marginLeft: theme.spacing(1),
    '& .MuiChip-avatar': {
        width: 24,
        height: 24,
    },
}));

export function ReadOnlyServicesManager({ staffMembers, selectedStaffMembers }: ReadOnlyServicesManagerProps) {
    const { t } = useTranslation('servicesManager');
    const { t: commonT } = useTranslation('common');

    const filteredServices = useMemo(() => {
        const staffToShow = selectedStaffMembers.length > 0 ? selectedStaffMembers : staffMembers;

        const servicesWithStaff: Array<ServiceModel & { staffMember: UserModel }> = [];

        staffToShow.forEach((staff) => {
            if (staff.services && staff.services.length > 0) {
                staff.services.forEach((service) => {
                    servicesWithStaff.push({
                        ...service,
                        staffMember: staff,
                    });
                });
            }
        });

        return servicesWithStaff.sort((a, b) => {
            // First sort by staff member name, then by service rank
            const staffComparison = a.staffMember.displayName.localeCompare(b.staffMember.displayName);
            if (staffComparison !== 0) return staffComparison;
            return ServiceModel.sort(a, b);
        });
    }, [staffMembers, selectedStaffMembers]);

    const formatDuration = (minutes: number) => {
        if (minutes < 60) {
            return `${minutes} ${t('minutes')}`;
        }
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        if (remainingMinutes === 0) {
            return `${hours} ${hours === 1 ? t('hour') : t('hours')}`;
        }
        return `${hours}h ${remainingMinutes}m`;
    };

    const formatPrice = (price: number, currency: string) => {
        return `${formatNumber(price)} ${currency?.toUpperCase() || ''}`;
    };

    if (filteredServices.length === 0) {
        return (
            <Grid container direction={'column'} alignItems={'center'} justifyContent={'center'} py={6}>
                <Typography variant={'h5'} fontWeight={500} mb={1}>
                    {selectedStaffMembers.length > 0 ? t('noServicesForSelectedStaff') : t('noServices')}
                </Typography>
                <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                    {selectedStaffMembers.length > 0 ? t('selectedStaffHaveNoServices') : t('noServicesDescription')}
                </Typography>
            </Grid>
        );
    }

    return (
        <Root>
            {filteredServices.map((service) => (
                <StyledListItem key={`${service.staffMember.uid}-${service.id}`}>
                    <ServiceColorIndicator color={service.color} />
                    <ListItemText
                        primary={
                            <Grid container alignItems="center" justifyContent="space-between">
                                <Grid item>
                                    <Typography variant="subtitle1" fontWeight={600}>
                                        {service.name}
                                    </Typography>
                                </Grid>
                                <Grid item>
                                    <StaffMemberChip
                                        avatar={
                                            <Avatar src={service.staffMember.photoURL}>
                                                {getInitials(service.staffMember.displayName)}
                                            </Avatar>
                                        }
                                        label={service.staffMember.displayName}
                                        variant="outlined"
                                        size="small"
                                    />
                                </Grid>
                            </Grid>
                        }
                        secondary={
                            <Grid container spacing={2} mt={0.5}>
                                <Grid item>
                                    <Typography variant="body2" color="textSecondary">
                                        <strong>{t('duration')}:</strong> {formatDuration(service.duration)}
                                    </Typography>
                                </Grid>
                                <Grid item>
                                    <Typography variant="body2" color="textSecondary">
                                        <strong>{t('price')}:</strong> {formatPrice(service.price, service.currency)}
                                    </Typography>
                                </Grid>
                                {service.description && (
                                    <Grid item xs={12}>
                                        <Typography variant="body2" color="textSecondary">
                                            <strong>{t('description')}:</strong> {service.description}
                                        </Typography>
                                    </Grid>
                                )}
                                {service.inactive && (
                                    <Grid item>
                                        <Chip
                                            label={commonT('inactive')}
                                            size="small"
                                            color="warning"
                                            variant="outlined"
                                        />
                                    </Grid>
                                )}
                                {service.hiddenFromClients && (
                                    <Grid item>
                                        <Chip
                                            label={t('hiddenFromClients')}
                                            size="small"
                                            color="info"
                                            variant="outlined"
                                        />
                                    </Grid>
                                )}
                            </Grid>
                        }
                    />
                </StyledListItem>
            ))}
        </Root>
    );
}
