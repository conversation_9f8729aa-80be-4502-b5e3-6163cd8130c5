import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import LoadingButton from '@mui/lab/LoadingButton';
import Grid from '@mui/material/Grid';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import Typography from '@mui/material/Typography';
import { alpha, styled } from '@mui/material/styles';
import classNames from 'classnames';
import { useFormikContext } from 'formik';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { useIsMounted } from '@bookr-technologies/ui/hooks/useIsMounted';
import { DragElementProps, useDragElement } from '../../hooks/useDragElement';
import { ServiceDialog } from '../ServiceDialog';

interface ServiceRowProps extends DragElementProps {
    name: string;
    serviceName: string;
    duration: number;
    price: number;
    currency: string;
    onEdit(name: string, service: ServiceModel): void;
    onDelete(name: string, service: ServiceModel): void;
}

const Root = styled(ListItem)(({ theme }) => ({
    padding: theme.spacing(1.25, 0),
    '&.isDragging': {
        '&, & + .MuiListItemSecondaryAction-root': {
            opacity: 0,
        },
    },
}));

const StyledButton = styled(LoadingButton)(({ theme }) => ({
    borderRadius: 10,
    backgroundColor: alpha(theme.palette.accent.main, 0.1),
    color: theme.palette.accent.main,
    '&:hover': {
        backgroundColor: alpha(theme.palette.accent.main, 0.2),
    },
}));

export function ServiceRow({
    index,
    serviceName,
    duration,
    price,
    currency,
    name,
    onEdit,
    onDelete,
    onMove,
}: ServiceRowProps) {
    const { t } = useTranslation('common');
    const formik = useFormikContext();
    const isMounted = useIsMounted();
    const [loading, setLoading] = useState(false);
    const [activeService, setActiveService] = useState<ServiceModel | null>(null);

    const { handlerId, elRef, isDragging } = useDragElement({ index, onMove });

    const handleClose = useCallback(() => {
        isMounted.only(() => setActiveService(null));
    }, [isMounted]);

    const handleEdit = useCallback(async () => {
        const service = formik.getFieldMeta<ServiceModel>(name).value;
        setActiveService({ ...service });
    }, [formik, name]);

    const handleDelete = useCallback(
        async (service: ServiceModel) => {
            setLoading(true);
            await onDelete(name, service);
            isMounted.only(() => setLoading(false));
        },
        [isMounted, name, onDelete],
    );

    const handleSubmitService = useCallback(
        async (service: ServiceModel) => {
            setLoading(true);
            await onEdit(name, service);
            isMounted.only(() => setLoading(false));
        },
        [isMounted, name, onEdit],
    );

    return (
        <>
            <ServiceDialog
                open={!!activeService}
                service={activeService}
                onClose={handleClose}
                onSubmit={handleSubmitService}
                onDelete={handleDelete}
            />
            <Root ref={elRef} className={classNames({ isDragging })} data-handler-id={handlerId}>
                <ListItemIcon>
                    <DragIndicatorIcon />
                </ListItemIcon>
                <Grid container alignItems={'center'}>
                    <Grid item xs container alignItems={'center'}>
                        <Grid item xs>
                            <Typography variant={'subtitle1'} fontWeight={600}>
                                {serviceName}
                            </Typography>
                        </Grid>
                        <Grid item xs maxWidth={'140px !important'}>
                            <Typography variant={'body2'} color={'textSecondary'} fontWeight={500}>
                                {t('mins', { count: duration || 0 })}
                            </Typography>
                        </Grid>
                        <Grid item xs maxWidth={'140px !important'}>
                            <Typography variant={'subtitle1'} fontWeight={700}>
                                {price} {currency}
                            </Typography>
                        </Grid>
                    </Grid>
                    <Grid
                        item
                        xs
                        container
                        alignItems={'center'}
                        justifyContent={'flex-end'}
                        maxWidth={'120px !important'}
                    >
                        <StyledButton
                            size={'small'}
                            variant={'contained'}
                            loading={loading}
                            onClick={handleEdit}
                            fullWidth
                            disableElevation
                        >
                            {t('edit')}
                        </StyledButton>
                    </Grid>
                </Grid>
            </Root>
        </>
    );
}
