import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useFormikContext } from 'formik';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { ServiceRow } from './ServiceRow';

interface ServicesManagerProps {
    name: string;
    onEdit?: (service: ServiceModel) => void | Promise<void> | ServiceModel | Promise<ServiceModel>;
    onDelete?: (service: ServiceModel) => void | Promise<void>;
}

const Root = styled(List)({
    width: '100%',
});

export function ServicesManager({ name, onEdit, onDelete }: ServicesManagerProps) {
    const { t } = useTranslation('servicesManager');
    const formik = useFormikContext();
    const records = formik.getFieldMeta<ServiceModel[]>(name);
    const services = useMemo(() => {
        const list = records.value || [];
        return list.filter(Boolean).sort(ServiceModel.sort);
    }, [records.value]);

    const handleEdit = useCallback(
        async (fieldPath: string, service: ServiceModel) => {
            formik.setFieldValue(fieldPath, service);

            if (service?.id && onEdit) {
                const result = await onEdit(service);
                if (result) {
                    formik.setFieldValue(fieldPath, result);
                }
            }
        },
        [formik, onEdit],
    );

    const handleDelete = useCallback(
        async (fieldPath: string, service: ServiceModel) => {
            if (onDelete) {
                await onDelete(service);
            }

            const newRecords = formik
                .getFieldMeta<ServiceModel[]>(name)
                .value?.filter((data) => data.id !== service.id);
            formik.setFieldValue(name, newRecords);
        },
        [formik, name, onDelete],
    );

    const handleMoveCard = useCallback(
        (dragIndex: any, hoverIndex: any) => {
            const newRecords = formik.getFieldMeta<ServiceModel[]>(name).value.filter(Boolean);
            const dragRecord = newRecords[dragIndex];
            newRecords[dragIndex] = newRecords[hoverIndex];
            newRecords[hoverIndex] = dragRecord;

            formik.setFieldValue(
                name,
                newRecords.map((record, index) => ({ ...record, serviceRank: index })),
            );
        },
        [formik, name],
    );

    return (
        <Root>
            {services.length > 0 ? (
                services.map((service, index) => (
                    <ServiceRow
                        key={service.id}
                        serviceName={service.name}
                        duration={service.duration}
                        price={service.price}
                        currency={service.currency}
                        name={`${name}.${index}`}
                        index={index}
                        onMove={handleMoveCard}
                        onEdit={handleEdit}
                        onDelete={handleDelete}
                    />
                ))
            ) : (
                <Grid container direction={'column'} alignItems={'center'} justifyContent={'center'} py={6}>
                    <Typography variant={'h5'} fontWeight={500} mb={1}>
                        {t('noServices')}
                    </Typography>
                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                        {t('noServicesDescription')}
                    </Typography>
                </Grid>
            )}
        </Root>
    );
}
