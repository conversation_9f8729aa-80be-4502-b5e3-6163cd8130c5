import Avatar from '@mui/material/Avatar';
import Paper from '@mui/material/Paper';
import Skeleton from '@mui/material/Skeleton';
import Typography from '@mui/material/Typography';
import { alpha, styled } from '@mui/material/styles';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { PrivilegeBasedAccess } from '@bookr-technologies/ui/PrivilegeBasedAccess';

export interface StatsCard {
    icon: ReactNode;
    title: string;
    value: number;
    loading?: boolean;
}

const Root = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(2, 3.25),
    flex: '1 1 auto',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    '.StatsCard-avatar': {
        width: theme.spacing(6),
        height: theme.spacing(6),
        backgroundColor: alpha(theme.palette.accent.main, 0.1),
        color: theme.palette.accent.main,
    },
}));

export function StatsCard({ icon, title, value, loading }: StatsCard) {
    const [counter, setCounter] = useState(0);
    const skeletonWidth = useMemo(() => 40 + Math.random() * 100, []);

    useEffect(() => {
        let interval: NodeJS.Timeout;
        if (value >= 0 && !loading) {
            interval = setInterval(() => {
                const step = Math.ceil(value / 100);
                setCounter((counter) => {
                    const nextValue = counter + step;
                    if (nextValue > value) {
                        clearInterval(interval);
                        return value;
                    }

                    return nextValue;
                });
            }, 10);
        }

        return () => interval && clearInterval(interval);
    }, [value, loading]);

    return (
        <Root>
            <Avatar className={'StatsCard-avatar'}>{icon}</Avatar>
            <Typography variant={'subtitle1'} fontWeight={500} color={'textSecondary'} mt={0.75} flexGrow={1}>
                {title}
            </Typography>
            {loading ? (
                <Skeleton width={skeletonWidth} height={44} />
            ) : (
                <Typography variant={'h4'} fontWeight={700} mt={0.25}>
                    {counter}
                </Typography>
            )}
            <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]} />
        </Root>
    );
}
