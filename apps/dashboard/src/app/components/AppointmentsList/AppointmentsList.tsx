import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import AppointmentsListItem from '../AppointmentsListItem/AppointmentsListItem';

interface AppointmentsListProps {
    appointments: AppointmentModel[];
}

function AppointmentsList({ appointments }: AppointmentsListProps) {
    const { t } = useTranslation('clientPage');

    const AppointmentsItems = appointments?.map((appointment) => (
        <AppointmentsListItem data={appointment} key={appointment.id} />
    ));

    return (
        <>
            <Typography variant={'h5'} component={'h2'} lineHeight={'32px'} marginBottom={'24px'} fontWeight={'bold'}>
                {t('appointmentsHistory')}
            </Typography>
            <Stack display={'flex'} flexDirection={'column'} spacing={2}>
                {AppointmentsItems}
            </Stack>
        </>
    );
}

export default AppointmentsList;
