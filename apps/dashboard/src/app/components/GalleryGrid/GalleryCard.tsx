import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import CircularProgress from '@mui/material/CircularProgress';
import IconButton from '@mui/material/IconButton';
import Menu, { MenuProps } from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import React, { HTMLAttributes, MouseEvent, useCallback, useMemo, useState } from 'react';
import { useIsMounted } from '@bookr-technologies/ui/hooks/useIsMounted';

interface GalleryCardProps extends HTMLAttributes<HTMLDivElement> {
    image: string;
    alt: string;
    onDelete: (image: string) => void | Promise<void>;
}

const Root = styled('div')(({ theme }) => ({
    paddingBottom: '100%',
    position: 'relative',
    width: '100%',
    borderRadius: 6,
    overflow: 'hidden',
    backgroundColor: '#eee',
    '&:after': {
        content: '""',
        display: 'block',
        background: 'linear-gradient(180deg, rgba(0, 0, 0, .7) 0%, rgba(0, 0, 0, 0) 65%)',
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        paddingBottom: '64%',
        zIndex: 3,
        pointerEvents: 'none',
    },
    '&:after, .gallery-card-button': {
        opacity: 0,
        transition: theme.transitions.create(['opacity', 'transform']),
        transform: 'translate3d(0, -14px, 0)',
    },
    '&:hover': {
        '&:after, .gallery-card-button': {
            opacity: 1,
            transform: 'translate3d(0, 0, 0)',
        },
    },
    '.gallery-card-image': {
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        width: '100%',
        height: '100%',
        objectFit: 'cover',
        objectPosition: 'center',
    },
    '.gallery-card-button': {
        position: 'absolute',
        top: 3,
        right: 8,
        zIndex: 5,
        transitionDelay: '200ms',
    },
    '.gallery-card-button__icon': {
        color: '#fff',
    },
    '.gallery-card-loader, .gallery-card-loader__progress': {
        opacity: 0,
    },
    '.gallery-card-loader': {
        position: 'absolute',
        top: 0,
        left: 0,
        bottom: 0,
        right: 0,
        zIndex: 7,
        backgroundColor: 'rgba(0, 0, 0, .5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        transition: theme.transitions.create(['opacity']),
        pointerEvents: 'none',
    },
    '.gallery-card-loader__progress': {
        position: 'relative',
        color: '#fff',
        transition: theme.transitions.create(['opacity']),
        transitionDelay: '400ms',
    },
    '&.loading': {
        '.gallery-card-loader': {
            pointerEvents: 'none',
        },
        '.gallery-card-loader, .gallery-card-loader__progress': {
            opacity: 1,
        },
    },
}));

export function GalleryCard({ image, alt, className, onDelete, ...rest }: GalleryCardProps) {
    const [anchor, setAnchor] = useState<Element | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const isMounted = useIsMounted();
    const menuProps = useMemo<Partial<MenuProps>>(
        () => ({
            anchorOrigin: {
                vertical: 'top',
                horizontal: 'right',
            },
            transformOrigin: {
                vertical: 'top',
                horizontal: 'right',
            },
        }),
        [],
    );

    const handleOpen = useCallback(({ target }: MouseEvent) => setAnchor(target as Element), []);
    const handleClose = useCallback(() => setAnchor(null), []);
    const handleDelete = useCallback(async () => {
        try {
            handleClose();
            setLoading(true);
            await onDelete(image);
        } finally {
            isMounted.only(() => {
                setLoading(false);
            });
        }
    }, [onDelete, image, isMounted, handleClose]);

    return (
        <Root className={classNames(className, { loading })} {...rest}>
            <IconButton className={'gallery-card-button'} size={'small'} onClick={handleOpen}>
                <MoreHorizIcon className={'gallery-card-button__icon'} />
            </IconButton>
            <Menu open={!!anchor} anchorEl={anchor} onClose={handleClose} {...menuProps}>
                <MenuItem onClick={handleDelete} sx={styles.menuItem}>
                    Delete
                </MenuItem>
            </Menu>
            <img src={image} alt={alt} className={'gallery-card-image'} />
            <div className={'gallery-card-loader'}>
                {loading && <CircularProgress className={'gallery-card-loader__progress'} />}
            </div>
        </Root>
    );
}

const styles = {
    menuItem: {
        fontWeight: 500,
        fontSize: 14,
        letterSpacing: 0.4,
        color: '#444',
    },
};
