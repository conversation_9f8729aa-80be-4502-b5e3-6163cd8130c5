import Grid, { GridProps } from '@mui/material/Grid';
import { useFormikContext } from 'formik';
import React, { useCallback } from 'react';
import { GalleryAdd } from './GalleryAdd';
import { GalleryCard } from './GalleryCard';
import { GalleryFile } from './GalleryFile';

export interface GalleryGridProps extends GridProps {
    name: string;
    onDelete(image: string): void | Promise<void>;
}

export function GalleryGrid({ name, onDelete, ...rest }: GalleryGridProps) {
    const formik = useFormikContext();
    const photos = formik.getFieldMeta<string[]>(`${name}.photos`);
    const files = formik.getFieldMeta<GalleryFile[]>(`${name}.files`);

    const handleAdd = useCallback(
        (galleryFiles: GalleryFile[]) => {
            const oldPhotos = photos.value || [];
            const oldFiles = files.value || [];
            const previews = galleryFiles.map(({ preview }) => preview);

            formik.setFieldValue(`${name}.photos`, [...oldPhotos, ...previews]);
            formik.setFieldValue(`${name}.files`, [...oldFiles, ...galleryFiles]);
        },
        [files.value, formik, name, photos.value],
    );

    const handleDelete = useCallback(
        async (url: string) => {
            await onDelete(url);

            const oldPhotos = photos.value || [];
            const oldFiles = files.value || [];

            formik.setFieldValue(
                `${name}.photos`,
                oldPhotos.filter((photo) => photo !== url),
            );
            formik.setFieldValue(
                `${name}.files`,
                oldFiles.filter((file) => file.preview !== url),
            );
        },
        [files.value, formik, name, onDelete, photos.value],
    );

    return (
        <Grid container spacing={2.5} {...rest}>
            <Grid item xs={2}>
                <GalleryAdd name={`${name}.files`} onAdd={handleAdd} />
            </Grid>
            {(photos.value ?? []).map((image, index) => (
                <Grid item xs={2} key={index}>
                    <GalleryCard image={image} alt={'Gallery Image'} onDelete={handleDelete} />
                </Grid>
            ))}
        </Grid>
    );
}
