import AddCircleIcon from '@mui/icons-material/AddCircleOutline';
import ButtonBase, { ButtonBaseProps } from '@mui/material/ButtonBase';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import React, { ChangeEvent, useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { GalleryFile } from './GalleryFile';

const Root = styled(ButtonBase)({
    backgroundColor: '#eee',
    borderRadius: 6,
    position: 'relative',
    display: 'flex',
    flex: '1 1 auto',
    width: '100%',
    height: '100%',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 140,
    '& > input': {
        display: 'none',
    },
    '.gallery-add-icon': {
        marginTop: -24,
    },
    '.gallery-add-text': {
        position: 'absolute',
        left: 0,
        right: 0,
        bottom: 10,
        textAlign: 'center',
        fontSize: 12,
        fontWeight: 500,
    },
});

interface GalleryAddProps extends Omit<ButtonBaseProps, 'onClick'> {
    onAdd(preview: GalleryFile[]): void;
}

export function GalleryAdd({ onAdd, ...rest }: GalleryAddProps) {
    const { t } = useTranslation('galleryGrid');
    const inputRef = useRef<HTMLInputElement | null>(null);

    const handleClick = useCallback(() => {
        if (inputRef.current) {
            inputRef.current.click();
        }
    }, [inputRef]);

    const handleFile = useCallback(
        (event: ChangeEvent<HTMLInputElement>) => {
            const files = Array.from(event.target.files ?? []).map((file) => ({
                file,
                preview: URL.createObjectURL(file),
            }));
            onAdd(files);
        },
        [onAdd],
    );

    return (
        <Root onClick={handleClick} {...rest}>
            <input ref={inputRef} type="file" onChange={handleFile} />
            <AddCircleIcon color={'accent'} fontSize={'large'} className={'gallery-add-icon'} />
            <Typography color={'accent.main'} variant={'body2'} className={'gallery-add-text'}>
                {t('addNewPhoto')}
            </Typography>
        </Root>
    );
}
