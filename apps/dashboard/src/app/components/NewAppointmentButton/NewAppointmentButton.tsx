import AddIcon from '@mui/icons-material/Add';
import Button from '@mui/material/Button';
import { styled } from '@mui/material/styles';
import { useCallback, useState } from 'react';
import { useCustomEventListener } from 'react-custom-events';
import { useTranslation } from 'react-i18next';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { CustomEvent } from '../../enums/CustomEvent';
import NewAppointmentDialog from '../NewAppointmentDialog/NewAppointmentDialog';

const StyledButton = styled(Button)({
    height: 40,
    borderRadius: 8,
    fontWeight: 500,
    textTransform: 'capitalize',
    letterSpacing: '0.0125em',
});

interface NewAppointmentClientDateProps {
    staffId?: string;
    client?: UserModel;
    timestamp: number;
}

export function NewAppointmentButton() {
    const { t } = useTranslation('newAppointmentButton');
    const [isOpen, setIsOpen] = useState(false);
    const [client, setClient] = useState<UserModel>();
    const [timestamp, setTimestamp] = useState<number>();
    const [staffId, setStaffId] = useState<string>();

    const handleOpenNewAppointmentDialog = useCallback(() => {
        setIsOpen(!isOpen);
        if (isOpen) {
            setTimestamp(Date.now());
            setStaffId('');
        }
    }, [isOpen]);

    useCustomEventListener(CustomEvent.HIDE_SHOW_NEW_APPOINTMENT_DIALOG, (data?: NewAppointmentClientDateProps) => {
        if (data?.client) {
            setClient(data.client);
        }
        if (data?.timestamp) {
            setTimestamp(data.timestamp);
        }
        if (data?.staffId) {
            setStaffId(data.staffId);
        }
        handleOpenNewAppointmentDialog();
    });

    return (
        <>
            <StyledButton
                startIcon={<AddIcon />}
                variant={'contained'}
                color={'accent'}
                disableElevation
                onClick={handleOpenNewAppointmentDialog}
            >
                {t('newAppointment')}
            </StyledButton>
            <NewAppointmentDialog
                isOpen={isOpen}
                handleCloseDialog={handleOpenNewAppointmentDialog}
                client={client}
                timestamp={timestamp}
                staffId={staffId}
            />
        </>
    );
}
