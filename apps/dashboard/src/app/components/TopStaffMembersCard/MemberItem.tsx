import Avatar from '@mui/material/Avatar';
import ListItem from '@mui/material/ListItem';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { PropsWithChildren, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { StaffStat } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { formatNumber } from '@bookr-technologies/core/utils/formatters';
import { getInitials } from '@bookr-technologies/ui/hooks/useInitials';
import { calculateTotalByCurrency } from '../../utils/calculateTotalByCurrency';

interface MemberItemProps {
    staffStat: StaffStat;
    currency: string;
    index?: number;
    onClick?: (user: UserModel) => void;
}

export function MemberItem({ children, currency, onClick, staffStat, index = -1 }: PropsWithChildren<MemberItemProps>) {
    const { t } = useTranslation('topStaffMembersCard');
    const { staff, finished, totalSales } = staffStat;

    const total = useMemo(() => calculateTotalByCurrency(totalSales, currency), [currency, totalSales]);
    const handleClick = useCallback(() => onClick && onClick(staff), [onClick, staff]);

    return (
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        <ListItem button={!!onClick as any} className={'TopStaffMembersCard-listItem'} onClick={handleClick}>
            <ListItemAvatar className={'TopStaffMembersCard-avatarHolder'}>
                <Avatar src={staff.photoURL}>{getInitials(staff.displayName)}</Avatar>
                {index >= 0 ? (
                    <Typography className={`TopStaffMembersCard-badge rank-${index + 1}`}>{index + 1}</Typography>
                ) : null}
            </ListItemAvatar>
            <ListItemText
                primary={staff.displayName}
                secondary={
                    <>
                        {t('ordersNumber', { count: finished || 0 })} &nbsp;&#8226;&nbsp;
                        {formatNumber(total)}&nbsp;
                        {currency?.toUpperCase() || ''}
                    </>
                }
                primaryTypographyProps={{
                    variant: 'subtitle1',
                    fontWeight: 600,
                }}
                secondaryTypographyProps={{
                    variant: 'caption',
                    fontWeight: 500,
                    color: 'textSecondary',
                }}
            />
            {children}
        </ListItem>
    );
}
