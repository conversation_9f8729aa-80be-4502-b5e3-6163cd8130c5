import Grid from '@mui/material/Grid';
import List from '@mui/material/List';
import Paper from '@mui/material/Paper';
import Skeleton from '@mui/material/Skeleton';
import Typography from '@mui/material/Typography';
import { darken, styled, Theme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { StaffStat } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { PrivilegeBasedAccess } from '@bookr-technologies/ui/PrivilegeBasedAccess';
import { MemberItem } from './MemberItem';

export interface TopStaffMembersCardProps {
    loading: boolean;
    currency: string;
    staffStats: StaffStat[];

    onClickMember(member: UserModel): void;
}

const badgeShade = (theme: Theme, value: number) => {
    const backgroundColor = darken(theme.palette.accent.main, value);
    return {
        backgroundColor,
        color: theme.palette.getContrastText(backgroundColor),
    };
};

const Root = styled(Paper)(({ theme }) => ({
    flex: 1,
    position: 'relative',
    padding: theme.spacing(2.75, 3.25),
    '.TopStaffMembersCard-list': {
        display: 'flex',
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'flex-start',
        alignContent: 'flex-start',
        justifyContent: 'flex-start',
        padding: 0,
        width: 'auto',
        position: 'absolute',
        top: theme.spacing(9),
        left: theme.spacing(3.25),
        right: theme.spacing(3.25),
        bottom: theme.spacing(3.25),
        maxHeight: '100%',
        overflowY: 'auto',
    },
    '.TopStaffMembersCard-listItem': {
        borderRadius: 12,
        paddingTop: 4,
        paddingBottom: 4,
    },
    '.TopStaffMembersCard-avatarHolder': {
        position: 'relative',
    },
    '.TopStaffMembersCard-badge': {
        width: 24,
        height: 24,
        lineHeight: '20px',
        textAlign: 'center',
        border: '2px solid #fff',
        borderRadius: 12,
        fontSize: 11,
        fontWeight: 700,
        position: 'absolute',
        bottom: -2,
        right: 11,
        ...badgeShade(theme, 0),
        '&.rank-2': {
            ...badgeShade(theme, 0.2),
        },
        '&.rank-3': {
            ...badgeShade(theme, 0.4),
        },
        '&.rank-4': {
            ...badgeShade(theme, 0.6),
        },
    },
}));

export function TopStaffMembersCard({ loading, staffStats, currency, onClickMember }: TopStaffMembersCardProps) {
    const { t } = useTranslation('topStaffMembersCard');
    const diff = (min: number, max: number) => min + Math.floor(Math.random() * (max - min));

    return (
        <Root>
            <Typography variant={'h5'} fontWeight={700}>
                {t('topStaffMembers')}
            </Typography>
            {loading ? (
                <Grid container className={'TopStaffMembersCard-list'}>
                    {new Array(8).fill(0).map((_, i) => (
                        <Grid item xs={6} my={1} px={2} key={i} container alignItems={'center'}>
                            <Skeleton variant={'circular'} width={40} height={40} />
                            <Grid item xs container ml={2} direction={'column'}>
                                <Skeleton variant={'text'} width={diff(80, 180)} height={24} />
                                <Grid container alignItems={'center'}>
                                    <Skeleton variant={'text'} width={diff(32, 65)} height={20} />
                                    <Skeleton variant={'circular'} width={8} height={8} sx={{ mx: 1 }} />
                                    <Skeleton variant={'text'} width={diff(32, 65)} height={20} />
                                </Grid>
                            </Grid>
                        </Grid>
                    ))}
                </Grid>
            ) : (
                <List className={'TopStaffMembersCard-list'}>
                    {staffStats.map((staffStat, index) => (
                        <Grid item xs={6} key={staffStat.staff.uid} mb={0.75}>
                            <MemberItem
                                index={index}
                                onClick={onClickMember}
                                staffStat={staffStat}
                                currency={currency}
                            />
                        </Grid>
                    ))}
                </List>
            )}

            <PrivilegeBasedAccess privileges={UserPrivilegeType.Professional} />
        </Root>
    );
}
