import CloseIcon from '@mui/icons-material/Close';
import IconButton from '@mui/material/IconButton';
import List from '@mui/material/List';
import ListItemSecondaryAction from '@mui/material/ListItemSecondaryAction';
import Paper from '@mui/material/Paper';
import { styled } from '@mui/material/styles';
import { useCallback } from 'react';
import { StaffStat } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { MemberItem } from './MemberItem';

export interface ProfileCardProps {
    staffStat: StaffStat;
    currency: string;
    hideClose?: boolean;
    onClose(): void;
}

const Root = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(0, 1),
    position: 'relative',
    width: '100%',
}));

export function ProfileCard({ staffStat, currency, hideClose, onClose }: ProfileCardProps) {
    const handleClose = useCallback(() => onClose(), [onClose]);

    return (
        <Root>
            <List disablePadding>
                <MemberItem staffStat={staffStat} currency={currency}>
                    {!hideClose ? (
                        <ListItemSecondaryAction>
                            <IconButton onClick={handleClose}>
                                <CloseIcon />
                            </IconButton>
                        </ListItemSecondaryAction>
                    ) : null}
                </MemberItem>
            </List>
        </Root>
    );
}
