/* eslint-disable @typescript-eslint/no-explicit-any */
import CloseIcon from '@mui/icons-material/Close';
import Button from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { Form, Formik, FormikProps } from 'formik';
import { useSnackbar } from 'notistack';
import React, { useCallback, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { NoteModel } from '@bookr-technologies/api/models/NoteModel';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';

interface NoteDialogProps extends Omit<DialogProps, 'onSubmit'> {
    note: NoteModel | null;
    appointment?: AppointmentModel;
    clientId?: string;
    onSubmit(note: any): void | Promise<void>;
    onDelete?: () => void | Promise<void>;
}

const StyledInputAdornment = styled(InputAdornment)`
    position: absolute;
    bottom: 16px;
    right: 16px;

    span {
        font-size: 14px;
    }
`;

export function NoteDialog({ open, note, appointment, clientId, onSubmit, onClose, onDelete }: NoteDialogProps) {
    const isNew = !note?.id;
    const formikRef = useRef<FormikProps<NoteModel> | null>(null);
    const { t } = useTranslation('serviceDialog');
    const { enqueueSnackbar } = useSnackbar();

    const handleClose = useCallback(() => {
        if (onClose) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (onClose as any)();
        }
    }, [onClose]);

    const handleDelete = useCallback(async () => {
        if (!note?.id) {
            return;
        }

        try {
            if (appointment) {
                await appointmentsEndpoint.deleteNote(appointment?.id, note?.id);
            } else if (clientId) {
                await businessClientsEndpoint.deleteClientNote(clientId, note?.id);
            }

            if (onDelete) {
                await onDelete();
            }
            handleClose();
            enqueueSnackbar(t('successDeleteNote'), { variant: 'warning' });
        } catch (e) {
            handleClose();
            enqueueSnackbar(t('errorDeleteNote'), { variant: 'error' });
        }
    }, [note?.id, appointment, clientId, onDelete, handleClose, enqueueSnackbar, t]);

    const handleSubmit = useCallback(
        async (value: NoteModel) => {
            try {
                let result = null;
                if (appointment?.id) {
                    result = isNew
                        ? await appointmentsEndpoint.createNote(appointment?.id, value)
                        : await appointmentsEndpoint.updateNote(appointment?.id, value);
                } else if (clientId) {
                    result = isNew
                        ? await businessClientsEndpoint.createClientNote(clientId, value)
                        : await businessClientsEndpoint.updateClientNote(clientId, value);
                }

                enqueueSnackbar(t(isNew ? 'successAddNote' : 'successEditNote'), { variant: 'success' });
                onSubmit(result);
            } catch (e) {
                enqueueSnackbar(t(isNew ? 'errorAddNote' : 'errorEditNote'), { variant: 'error' });
            }
            handleClose();
        },
        [appointment?.id, clientId, enqueueSnackbar, handleClose, isNew, onSubmit, t],
    );

    if (!note) {
        return null;
    }

    return (
        <StyledDialog open={open} onClose={handleClose} fullWidth>
            <Grid container alignItems={'center'} justifyContent={'space-between'} px={4} pt={3}>
                <Typography variant={'h5'} fontWeight={700}>
                    {t(isNew ? 'addNoteTitle' : 'editNoteTitle')}
                </Typography>
                <IconButton onClick={handleClose}>
                    <CloseIcon style={{ fill: '#000000' }} />
                </IconButton>
            </Grid>
            <Formik initialValues={note} onSubmit={handleSubmit} innerRef={formikRef}>
                {({ values }) => (
                    <Form>
                        <DialogContent>
                            <FormikTextField
                                name={'text'}
                                size={'small'}
                                fullWidth
                                multiline
                                minRows={6}
                                maxRows={14}
                                placeholder={t('addNotePlaceholder')}
                                inputProps={{ maxlength: 255 }}
                                InputProps={{
                                    endAdornment: (
                                        <StyledInputAdornment position="end">
                                            <span>{values.text?.length || 0} / 255</span>
                                        </StyledInputAdornment>
                                    ),
                                }}
                            />
                        </DialogContent>

                        <DialogActions>
                            <Grid container alignItems={'center'} justifyContent={'space-evenly'} px={3} py={2}>
                                {isNew ? (
                                    <CancelButton onClick={handleClose}>{t('cancel')}</CancelButton>
                                ) : (
                                    <DeleteButton onClick={handleDelete} color={'error'}>
                                        {t('deleteNoteButton')}
                                    </DeleteButton>
                                )}

                                <SaveButton variant={'contained'} size={'small'} disableElevation>
                                    {t(isNew ? 'addNoteButton' : 'editNoteButton')}
                                </SaveButton>
                            </Grid>
                        </DialogActions>
                    </Form>
                )}
            </Formik>
        </StyledDialog>
    );
}

const buttonStyle = {
    height: 56,
    lineHeight: '40px',
    borderRadius: 20,
    paddingLeft: 60,
    paddingRight: 60,
    minWidth: '40%',
};

const StyledDialog = styled(Dialog)({
    '.MuiDialog-paper': {
        maxWidth: 580,
        borderRadius: 16,
    },
});

const SaveButton = styled(FormikButton)({ ...buttonStyle });

const DeleteButton = styled(Button)({
    ...buttonStyle,
    paddingLeft: 14,
    paddingRight: 14,
});

const CancelButton = styled(Button)({
    ...buttonStyle,
    color: '#afafaf',
    marginRight: 14,
});
