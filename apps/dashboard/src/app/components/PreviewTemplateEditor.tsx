import CircularProgress from '@mui/material/CircularProgress';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useFormikContext } from 'formik';
import debounce from 'lodash/debounce';
import { Fragment, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useMutation } from 'react-query';
import { notificationsEndpoint } from '@bookr-technologies/api/endpoints/notificationEndpoint';

interface Props {
    name: string;
}

const Root = styled(Paper)({
    color: '#545454',
    display: 'flex',
    alignItems: 'flex-start',
    justifyContent: 'center',
    minHeight: 180,
    '.MuiTypography-root': {
        width: '100%',
    },
});

const debouncedFunc = debounce((func) => func(), 500);

export function PreviewTemplateEditor({ name }: Props) {
    const { t } = useTranslation('businessSettingsPage');

    const formikContext = useFormikContext();
    const meta = formikContext.getFieldMeta<string>(name);

    const preview = useMutation('notifications/preview', (template: string) =>
        notificationsEndpoint.previewTemplate(template),
    );

    function renderContent(content?: string | null) {
        if (!content) {
            return t('noPreviewContent');
        }

        return content
            .trim()
            .split('\n')
            .map((line, index) => (
                <Fragment key={index}>
                    {line} <br />
                </Fragment>
            ))
            .flat();
    }

    useEffect(
        () => {
            if (meta.value) {
                debouncedFunc(() => preview.mutate(meta.value));
            }
        },
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [meta.value],
    );

    return (
        <Root variant={'outlined'}>
            {preview.isLoading ? (
                <CircularProgress color={'inherit'} size={24} />
            ) : (
                <Typography
                    variant={'body2'}
                    color={'inherit'}
                    fontWeight={500}
                    align={preview.data ? 'left' : 'center'}
                    p={preview.data ? 1 : 3}
                    display={'block'}
                >
                    {renderContent(meta.value ? preview.data : null)}
                </Typography>
            )}
        </Root>
    );
}
