import EditIcon from '@mui/icons-material/Edit';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';
import moment from 'moment/moment';
import * as React from 'react';
import { NoteModel } from '@bookr-technologies/api/models/NoteModel';

const StyledNoteBox = styled(Box)`
    padding: 10px 50px 10px 25px;
    background: #eeeeee;
    border-radius: 8px;
    margin-bottom: 16px;
    min-height: 74px;
    cursor: pointer;

    &:hover {
        background: #e6e6e6;
    }
`;

const StyledNoteDate = styled('span')`
    font-weight: 500;
    font-size: 14px;
    line-height: 30px;
    color: #afafaf;
`;

const StyledNoteText = styled('p')`
    max-height: 48px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    margin: 0;
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #545454;
`;

const StyledEditIcon = styled(IconButton)`
    position: absolute;
    top: 16px;
    right: 16px;
`;

interface NoteCardProps {
    note: NoteModel;
    setNoteData: (note: NoteModel) => void;
    setNoteDialogOpen: (open: boolean) => void;
}
export function NoteCard({ note, setNoteDialogOpen, setNoteData }: NoteCardProps) {
    return (
        <StyledNoteBox
            position={'relative'}
            key={note.id}
            onClick={() => {
                setNoteData(note);
                setNoteDialogOpen(true);
            }}
        >
            <StyledEditIcon aria-label="delete">
                <EditIcon />
            </StyledEditIcon>
            <StyledNoteDate>{moment(note.createdAt).format('DD MMM YYYY')}</StyledNoteDate>
            <StyledNoteText>{note.text}</StyledNoteText>
        </StyledNoteBox>
    );
}
