import LoadingButton from '@mui/lab/LoadingButton';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { InviteClientsSteps, useInviteClientsStore } from './inviteClientsStore';

interface Props {
    continueLabel: string;
    nextStep: InviteClientsSteps | (() => void | Promise<InviteClientsSteps | void | null> | null) | null;
    cancelLabel?: string;
}

export function InviteClientsDialogActions({ continueLabel, cancelLabel, nextStep }: Props) {
    const setStep = useInviteClientsStore((state) => state.setStep);
    const close = useInviteClientsStore((state) => state.close);
    const { t } = useTranslation('inviteClients');
    const [loading, setLoading] = useState(false);

    const handleContinue = useCallback(async () => {
        let step: InviteClientsSteps | void | null = null;

        if (typeof nextStep === 'function') {
            setLoading(true);
            try {
                step = await nextStep();
            } finally {
                //
            }
        } else {
            step = nextStep;
        }

        setLoading(false);

        if (!step && step !== null) {
            close();
            return;
        }

        if (step) {
            setStep(step);
        }
    }, [close, nextStep, setStep]);

    return (
        <Grid container alignItems={'center'}>
            <Grid item xs={6} pr={3}>
                <Button
                    variant={'contained'}
                    color={'inherit'}
                    className={'transparent-button'}
                    disableElevation
                    fullWidth
                    onClick={close}
                >
                    {cancelLabel ? cancelLabel : t('cancel')}
                </Button>
            </Grid>

            <Grid item xs={6} pl={3}>
                <LoadingButton
                    fullWidth
                    loading={loading}
                    variant={'contained'}
                    color={'primary'}
                    disableElevation
                    onClick={handleContinue}
                >
                    {continueLabel}
                </LoadingButton>
            </Grid>
        </Grid>
    );
}
