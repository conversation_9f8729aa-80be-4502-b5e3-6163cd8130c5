import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import RadioButton from '../RadioButton/RadioButton';
import { InviteClientsDialogActions } from './InviteClientsDialogActions';
import { InviteClientsDialogStepTransition } from './InviteClientsDialogStepTransition';
import { InviteClientsSteps } from './inviteClientsStore';

const StyledOptionsContainer = styled('div')`
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
`;

export function ChooseStep() {
    const { t } = useTranslation('inviteClients');
    const [isSingleClientOptionSelected, setIsSingleClientOptionSelected] = useState(true);
    const [isMultipleClientsOptionSelected, setIsMultipleClientsOptionSelected] = useState(false);

    const handleSingleClientOptionChanged = (newValue: boolean) => {
        setIsMultipleClientsOptionSelected(false);
        setIsSingleClientOptionSelected(newValue);
    };

    const handleMultipleClientSOptionChanged = (newValue: boolean) => {
        setIsSingleClientOptionSelected(false);
        setIsMultipleClientsOptionSelected(newValue);
    };

    return (
        <InviteClientsDialogStepTransition step={InviteClientsSteps.Choose}>
            <Stack maxWidth={506} marginBottom={'20px'}>
                <Typography variant={'h4'} fontWeight={700} mb={2}>
                    {t('dialogHeader')}
                </Typography>
                <Typography variant={'body1'} fontWeight={500} color={'textSecondary'}>
                    {t('dialogText')}
                </Typography>
            </Stack>
            <StyledOptionsContainer>
                <RadioButton
                    checked={isSingleClientOptionSelected}
                    onChange={handleSingleClientOptionChanged}
                    value={'singleClient'}
                    label={t('singleClientHeader')}
                    text={t('singleClientText')}
                    name="invite-single-client-option"
                />
                <RadioButton
                    checked={isMultipleClientsOptionSelected}
                    onChange={handleMultipleClientSOptionChanged}
                    value={'multipleClients'}
                    label={t('multipleClientsHeader')}
                    text={t('multipleClientsText')}
                    name="invite-multiple-clients-option"
                />
            </StyledOptionsContainer>
            <InviteClientsDialogActions
                continueLabel={t('continue')}
                nextStep={
                    isMultipleClientsOptionSelected
                        ? InviteClientsSteps.MultipleClientsGettingStarted
                        : InviteClientsSteps.SingleClientInput
                }
            />
        </InviteClientsDialogStepTransition>
    );
}
