import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { InviteClientsDialog } from './InviteClientsDialog';
import banner from './assets/banner.png';
import { useInviteClientsStore } from './inviteClientsStore';

const Root = styled(Paper)(({ theme }) => ({
    backgroundColor: '#1f1f1f',
    borderRadius: 16,
    overflow: 'hidden',
    color: '#fff',
    display: 'flex',
    flexDirection: 'row',

    '.MuiButton-root': {
        backgroundColor: '#fff',
        color: '#111',
        fontSize: 14,
        fontWeight: 500,
        padding: '8px 24px',
    },

    '.content': {
        padding: theme.spacing(3, 4, 4, 4),
    },

    '.heading': {
        lineHeight: '30px',
        marginBottom: theme.spacing(1.5),
    },

    '.description': {
        lineHeight: '24px',
        marginBottom: theme.spacing(1.5),
        color: '#e2e2e2',
    },

    '.banner': {
        minWidth: 200,
        maxWidth: 200,
    },

    '.banner-image': {
        width: '100%',
        height: '100%',
        objectFit: 'contain',
        objectPosition: 'right center',
        display: 'flex',
    },

    '.dialog-button': {
        borderRadius: 10,
    },
}));

export function InviteClients() {
    const { t } = useTranslation('inviteClients');
    const open = useInviteClientsStore((state) => state.open);

    return (
        <Root>
            <Grid item xs className={'content'}>
                <Typography className={'heading'} variant={'h5'} fontWeight={700}>
                    {t('heading')}
                </Typography>
                <Typography className={'description'} variant={'body2'} fontWeight={500}>
                    {t('description')}
                </Typography>
                <Button variant={'contained'} onClick={open} className={'dialog-button'} disableElevation>
                    {t('addNewClients')}
                </Button>
                <InviteClientsDialog />
            </Grid>

            <Grid item xs className={'banner'}>
                <img src={banner} alt={'Banner'} className={'banner-image'} />
            </Grid>
        </Root>
    );
}
