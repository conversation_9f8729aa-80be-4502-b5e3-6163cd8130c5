import CloseIcon from '@mui/icons-material/Close';
import Dialog from '@mui/material/Dialog';
import IconButton from '@mui/material/IconButton';
import { styled } from '@mui/material/styles';
import * as React from 'react';
import { ChooseStep } from './ChooseStep';
import { MultipleClientsGettingStartedStep } from './InviteMultipleClientsDialogSteps/MultipleClientsGettingStartedStep';
import { MultipleClientsReviewStep } from './InviteMultipleClientsDialogSteps/MultipleClientsReviewStep';
import { MultipleClientsUploadStep } from './InviteMultipleClientsDialogSteps/MultipleClientsUploadStep';
import { SingleClientInputStep } from './InviteSingleClientDialogSteps/SingleClientInputStep';
import { SingleClientSuccessStep } from './InviteSingleClientDialogSteps/SingleClientSuccessStep';
import { useInviteClientsStore } from './inviteClientsStore';

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '.MuiDialogContent-root': {
        padding: theme.spacing(5.5),
        flexShrink: 1,
    },
    '.MuiDialog-paper': {
        boxShadow: '0 4px 4px rgba(0, 0, 0, 0.25)',
        borderRadius: 16,
        width: '100%',
        maxWidth: 724,
        display: 'flex',
        flexDirection: 'row',
        flex: '1 1 auto',
        overflow: 'hidden',
    },
    '.template-banner': {
        backgroundColor: '#eee',
        borderRadius: 16,
        padding: theme.spacing(2, 2, 2, 3),
        '.MuiButton-root': {
            borderRadius: 10,
            fontWeight: 500,
        },
    },
    '.transparent-button': {
        backgroundColor: 'transparent',
    },
    '.close-button': {
        position: 'absolute',
        top: theme.spacing(5),
        right: theme.spacing(5),
    },
}));

export function InviteClientsDialog() {
    const isOpen = useInviteClientsStore((state) => state.isOpen);
    const close = useInviteClientsStore((state) => state.close);

    return (
        <StyledDialog open={isOpen} onClose={close}>
            <ChooseStep />
            <SingleClientInputStep />
            <SingleClientSuccessStep />
            <MultipleClientsGettingStartedStep />
            <MultipleClientsUploadStep />
            <MultipleClientsReviewStep />
            <IconButton className={'close-button'} onClick={close}>
                <CloseIcon />
            </IconButton>
        </StyledDialog>
    );
}
