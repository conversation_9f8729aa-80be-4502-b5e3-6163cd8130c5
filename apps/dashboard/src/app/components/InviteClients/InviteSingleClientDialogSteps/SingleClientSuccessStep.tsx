import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import { InviteClientsDialogActions } from '../InviteClientsDialogActions';
import { InviteClientsDialogStepTransition } from '../InviteClientsDialogStepTransition';
import { InviteClientsSteps } from '../inviteClientsStore';

const StyledDialogTextContainer = styled(Stack)`
    .dialogHeaderText {
        max-width: 350px;
    }

    .checkIcon {
        width: 60px;
        height: 60px;
        margin-top: 30px;
        margin-bottom: 20px;
        color: ${({ theme }) => theme.palette.accent.main};
    }
`;

export function SingleClientSuccessStep() {
    const { t } = useTranslation('inviteClients');

    return (
        <InviteClientsDialogStepTransition step={InviteClientsSteps.SingleClientSuccess}>
            <StyledDialogTextContainer justifyContent={'center'} alignItems={'center'} marginBottom={'30px'}>
                <CheckCircleIcon className="checkIcon" />
                <Typography
                    className="dialogHeaderText"
                    variant={'h4'}
                    fontWeight={700}
                    maxWidth={450}
                    align={'center'}
                    mb={2}
                >
                    {t('singleClientSuccessDialogHeader')}
                </Typography>
                <Typography variant={'body1'} fontWeight={500} maxWidth={450} align={'center'} color={'textSecondary'}>
                    {t('singleClientSuccessText')}
                </Typography>
            </StyledDialogTextContainer>
            <InviteClientsDialogActions
                cancelLabel={t('close')}
                continueLabel={t('inviteAgain')}
                nextStep={InviteClientsSteps.Choose}
            />
        </InviteClientsDialogStepTransition>
    );
}
