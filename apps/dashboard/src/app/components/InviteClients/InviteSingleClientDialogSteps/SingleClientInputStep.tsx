import CloseIcon from '@mui/icons-material/Close';
// eslint-disable-next-line no-restricted-imports
import { AutocompleteValue } from '@mui/material';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { Form, Formik } from 'formik';
import { t } from 'i18next';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { RestCountryModel } from '@bookr-technologies/api/models/RestCountryModel';
import { CountrySelectVariant, FormikCountrySelect } from '@bookr-technologies/ui/CountrySelect';
import { FormikTextField } from '@bookr-technologies/ui/Fields';
import { InviteClientsDialogActions } from '../InviteClientsDialogActions';
import { InviteClientsDialogStepTransition } from '../InviteClientsDialogStepTransition';
import { InvitationRecord, InviteClientsSteps } from '../inviteClientsStore';

export const validationSchema = Yup.object().shape({
    name: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    callingCode: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    phoneNumber: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
});

export function SingleClientInputStep() {
    const { t } = useTranslation('inviteClients');
    const [name, setName] = useState('');
    const [callingCode, setCallingCode] = useState('+40');
    const [phoneNumber, setPhoneNumber] = useState('');
    const { enqueueSnackbar, closeSnackbar } = useSnackbar();

    const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setName(event.target.value);
    };

    const handleCallingCodeChange = (value: AutocompleteValue<RestCountryModel, false, false, false>) => {
        if (value) {
            setCallingCode(value.callingCode);
        }
    };

    const handlePhoneNumberChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setPhoneNumber(event.target.value);
    };

    const handleContinue = useCallback(
        () =>
            new Promise<InviteClientsSteps>((resolve, reject) => {
                if (!name || !callingCode || !phoneNumber) {
                    enqueueSnackbar(t('singleClientImportInvalidFieldsError'), { variant: 'error' });
                    resolve(InviteClientsSteps.SingleClientInput);
                    return;
                }

                const invitation = {
                    displayName: name,
                    phoneNumber: `${callingCode}${phoneNumber}`,
                } as InvitationRecord;

                const infoKey = enqueueSnackbar(t('importingClient'), {
                    variant: 'info',
                    persist: true,
                    action: (key) => (
                        <Grid container alignItems={'center'}>
                            <CircularProgress size={24} color={'inherit'} />
                            <IconButton color={'inherit'} sx={{ marginLeft: 1 }} onClick={() => closeSnackbar(key)}>
                                <CloseIcon />
                            </IconButton>
                        </Grid>
                    ),
                });

                businessClientsEndpoint.importBulk([invitation]).then(
                    (data) => {
                        infoKey && closeSnackbar(infoKey);
                        if (data.error) {
                            enqueueSnackbar(t('inviteClientWarning'), { variant: 'warning' });
                            reject();
                        } else {
                            resolve(InviteClientsSteps.SingleClientSuccess);
                        }
                    },
                    () => {
                        infoKey && closeSnackbar(infoKey);
                        enqueueSnackbar(t('inviteClientError'), { variant: 'error' });
                        reject();
                    },
                );
            }),
        [enqueueSnackbar, closeSnackbar, name, callingCode, phoneNumber, t],
    );

    return (
        <InviteClientsDialogStepTransition step={InviteClientsSteps.SingleClientInput}>
            <Formik
                initialValues={{ name: '', callingCode: '+40', phoneNumber: '' }}
                onSubmit={handleContinue}
                validationSchema={validationSchema}
                validateOnMount
            >
                <Form className={'form'}>
                    <Stack maxWidth={506} marginBottom={'30px'}>
                        <Typography variant={'h4'} fontWeight={700} mb={2}>
                            {t('singleClientDialogHeader')}
                        </Typography>
                        <Typography variant={'body1'} fontWeight={500} color={'textSecondary'}>
                            {t('singleClientDialogText')}
                        </Typography>
                    </Stack>
                    <Stack spacing={3} width={'100%'}>
                        <FormikTextField
                            name="name"
                            id="name"
                            margin="dense"
                            label={t('clientName')}
                            fullWidth
                            variant="outlined"
                            color="secondary"
                            onChange={handleNameChange}
                        />
                        <Stack direction={'row'} spacing={3} width={'100%'} marginBottom={'60px !important'}>
                            <Grid item xs maxWidth={'140px !important'}>
                                <FormikCountrySelect
                                    name={'callingCode'}
                                    label={t('callingCodeField')}
                                    variant={CountrySelectVariant.CallingCode}
                                    color={'secondary'}
                                    onChange={(event, value, reason, details) => handleCallingCodeChange(value)}
                                />
                            </Grid>
                            <Grid item xs>
                                <FormikTextField
                                    name={'phoneNumber'}
                                    label={t('phoneNumberField')}
                                    fullWidth
                                    color={'secondary'}
                                    onChange={handlePhoneNumberChange}
                                />
                            </Grid>
                        </Stack>
                    </Stack>
                    <InviteClientsDialogActions continueLabel={t('continue')} nextStep={handleContinue} />
                </Form>
            </Formik>
        </InviteClientsDialogStepTransition>
    );
}
