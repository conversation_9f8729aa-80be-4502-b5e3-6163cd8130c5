import CloseIcon from '@mui/icons-material/Close';
import CircularProgress from '@mui/material/CircularProgress';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import { useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { businessClientsEndpoint } from '@bookr-technologies/api/endpoints/businessClientsEndpoint';
import { InviteClientsDialogActions } from '../InviteClientsDialogActions';
import { InviteClientsDialogStepTransition } from '../InviteClientsDialogStepTransition';
import { InviteClientsSteps, useInviteClientsStore } from '../inviteClientsStore';

const StyledPaper = styled(Paper)(({ theme }) => ({
    background: '#eee',
    borderRadius: 14,
    padding: theme.spacing(1, 4.75),
    marginBottom: theme.spacing(4),
    display: 'flex',
    flexDirection: 'column',
    maxHeight: 340,
    overflow: 'auto',

    '.upload-icon': {
        fontSize: theme.spacing(8),
        marginBottom: theme.spacing(2),
    },
}));

export function MultipleClientsReviewStep() {
    const { t } = useTranslation('inviteClients');
    const invitations = useInviteClientsStore((state) => state.invitations);
    const step = useInviteClientsStore((state) => state.step);
    const setStep = useInviteClientsStore((state) => state.setStep);
    const { enqueueSnackbar, closeSnackbar } = useSnackbar();

    const handleSubmit = useCallback(() => {
        const infoKey = enqueueSnackbar(t('importingClients'), {
            variant: 'info',
            persist: true,
            action: (key) => (
                <Grid container alignItems={'center'}>
                    <CircularProgress size={24} color={'inherit'} />
                    <IconButton color={'inherit'} sx={{ marginLeft: 1 }} onClick={() => closeSnackbar(key)}>
                        <CloseIcon />
                    </IconButton>
                </Grid>
            ),
        });

        businessClientsEndpoint.importBulk(invitations).then(
            (data) => {
                infoKey && closeSnackbar(infoKey);
                if (data.error) {
                    enqueueSnackbar(t('inviteClientsWarning'), { variant: 'warning' });
                } else {
                    enqueueSnackbar(t('inviteClientsSuccess'), { variant: 'success' });
                    window.location.reload();
                }
            },
            () => {
                infoKey && closeSnackbar(infoKey);
                enqueueSnackbar(t('inviteClientsError'), { variant: 'error' });
            },
        );
    }, [closeSnackbar, enqueueSnackbar, invitations, t]);

    useEffect(() => {
        if (step === InviteClientsSteps.MultipleClientsReview && invitations.length === 0) {
            setStep(InviteClientsSteps.MultipleClientsUpload);
        }
    }, [invitations, setStep, step]);

    return (
        <InviteClientsDialogStepTransition step={InviteClientsSteps.MultipleClientsReview}>
            <Stack maxWidth={506} mb={5}>
                <Typography variant={'h4'} fontWeight={700}>
                    {t('reviewTitle')}
                </Typography>
            </Stack>

            <StyledPaper elevation={0}>
                {invitations.map(({ displayName, phoneNumber }, index) => (
                    <Grid container alignItems={'center'} key={index} py={2}>
                        <Grid item xs>
                            <Typography variant={'body1'} fontWeight={600} color={displayName ? 'primary' : 'error'}>
                                {displayName || t('notSpecified')}
                            </Typography>
                        </Grid>
                        <Grid item xs>
                            <Typography
                                variant={'body1'}
                                fontWeight={500}
                                color={phoneNumber ? 'textSecondary' : 'error'}
                            >
                                {phoneNumber || t('notSpecified')}
                            </Typography>
                        </Grid>
                    </Grid>
                ))}
            </StyledPaper>

            <InviteClientsDialogActions continueLabel={t('finish')} nextStep={handleSubmit} />
        </InviteClientsDialogStepTransition>
    );
}
