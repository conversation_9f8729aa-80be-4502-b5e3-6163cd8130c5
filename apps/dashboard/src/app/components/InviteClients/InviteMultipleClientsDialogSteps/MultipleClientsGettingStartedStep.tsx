import DescriptionIcon from '@mui/icons-material/Description';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { InviteClientsDialogActions } from '../InviteClientsDialogActions';
import { InviteClientsDialogStepTransition } from '../InviteClientsDialogStepTransition';
import { InviteClientsSteps } from '../inviteClientsStore';

export function MultipleClientsGettingStartedStep() {
    const { t } = useTranslation('inviteClients');

    const handleDownload = useCallback(() => {
        const content = [
            `${t('displayName')}, ${t('phone')}`,
            `${t('displayNamePlaceholder')}, ${t('phonePlaceholder')}`,
        ].join(`\n`);

        const file = new File([content], `${t('templateFileName')}`, { type: 'text/csv' });
        const url = URL.createObjectURL(file);

        const a = document.createElement('a');
        a.href = url;
        a.download = `${t('templateFileName')}`;
        a.click();
    }, [t]);

    return (
        <InviteClientsDialogStepTransition step={InviteClientsSteps.MultipleClientsGettingStarted}>
            <Stack maxWidth={506}>
                <Typography variant={'h4'} fontWeight={700} mb={2}>
                    {t('gettingStartedTitle')}
                </Typography>
                <Typography variant={'body1'} fontWeight={500} color={'textSecondary'}>
                    {t('gettingStartedDescription')}
                </Typography>
            </Stack>
            <Grid py={5.5}>
                <Paper elevation={0} className={'template-banner'}>
                    <Grid container alignItems={'center'}>
                        <Grid item xs={8} container alignItems={'center'}>
                            <DescriptionIcon />
                            <Typography variant={'body1'} fontWeight={500} ml={3}>
                                {t('templateFileName')}
                            </Typography>
                        </Grid>
                        <Grid
                            item
                            xs={4}
                            container
                            alignItems={'center'}
                            justifyContent={'flex-end'}
                            onClick={handleDownload}
                        >
                            <Button variant={'contained'} color={'accent'} size={'small'} disableElevation>
                                {t('download')}
                            </Button>
                        </Grid>
                    </Grid>
                </Paper>
            </Grid>
            <InviteClientsDialogActions
                continueLabel={t('iHaveDownloaded')}
                nextStep={InviteClientsSteps.MultipleClientsUpload}
            />
        </InviteClientsDialogStepTransition>
    );
}
