import CloudDownloadIcon from '@mui/icons-material/CloudDownload';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import DescriptionIcon from '@mui/icons-material/Description';
import Fade from '@mui/material/Fade';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import Link from '@mui/material/Link';
import Paper from '@mui/material/Paper';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { useSnackbar } from 'notistack';
import { MouseEvent, useCallback, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { parseCsv } from '@bookr-technologies/core/utils/parseCsv';
import { useDragAndDrop } from '@bookr-technologies/ui/hooks/useDragAndDrop';
import { useFilePicker } from '@bookr-technologies/ui/hooks/useFilePicker';
import { InviteClientsDialogActions } from '../InviteClientsDialogActions';
import { InviteClientsDialogStepTransition } from '../InviteClientsDialogStepTransition';
import { InvitationRecord, InviteClientsSteps, useInviteClientsStore } from '../inviteClientsStore';

const StyledPaper = styled(Paper)(({ theme }) => ({
    background: '#eee',
    borderRadius: 14,
    padding: theme.spacing(1.5, 2, 1.5, 2.75),
    marginBottom: theme.spacing(3),
    position: 'relative',

    '.dropContentInner': {
        position: 'absolute',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        top: '50%',
        left: 0,
        transform: 'translateY(-50%)',
    },
    '.upload-icon': {
        fontSize: theme.spacing(8),
        marginBottom: theme.spacing(2),
    },
}));

export function MultipleClientsUploadStep() {
    const [file, setFile] = useState<File | null>(null);

    const { t } = useTranslation('inviteClients');
    const setStep = useInviteClientsStore((state) => state.setStep);
    const setInvitations = useInviteClientsStore((state) => state.setInvitations);
    const filePicker = useFilePicker({
        accept: 'text/csv',
        onChange([file]) {
            setFile(file);
        },
    });

    const [dragProps, { isDragging }] = useDragAndDrop<HTMLDivElement>({
        onDrop({ files }) {
            setFile(files[0] ?? null);
        },
    });

    const handleTemplateStep = useCallback(
        (e: MouseEvent<HTMLAnchorElement>) => {
            e.preventDefault();
            setStep(InviteClientsSteps.MultipleClientsGettingStarted);
        },
        [setStep],
    );

    const { enqueueSnackbar } = useSnackbar();

    const handleRemove = useCallback(() => setFile(null), []);

    const handleContinue = useCallback(
        () =>
            new Promise<InviteClientsSteps>((resolve, reject) => {
                if (!file) {
                    enqueueSnackbar(t('noFileSelected'), { variant: 'error' });
                    return;
                }

                const reader = new FileReader();
                reader.addEventListener('load', (event) => {
                    const result = event.target?.result as string;
                    const data = parseCsv<InvitationRecord>(result ?? '', {
                        columns: ['displayName', 'phoneNumber'],
                    });

                    setInvitations(data);
                    resolve(InviteClientsSteps.MultipleClientsReview);
                });
                reader.addEventListener('error', () => {
                    enqueueSnackbar(t('errorParsingFile'), { variant: 'error' });
                    reject();
                });
                reader.readAsText(file);
            }),
        [enqueueSnackbar, file, setInvitations, t],
    );

    return (
        <InviteClientsDialogStepTransition step={InviteClientsSteps.MultipleClientsUpload}>
            <Stack maxWidth={506}>
                <Typography variant={'h4'} fontWeight={700} mb={!file ? 2 : 5}>
                    {t('uploadTitle')}
                </Typography>
            </Stack>
            {file ? (
                <>
                    <StyledPaper elevation={0}>
                        <Grid container alignItems={'center'}>
                            <Grid item xs={8} container alignItems={'center'}>
                                <DescriptionIcon />
                                <Typography variant={'body1'} fontWeight={500} ml={3}>
                                    {file.name}
                                </Typography>
                            </Grid>
                            <Grid item xs={4} container alignItems={'center'} justifyContent={'flex-end'}>
                                <IconButton onClick={handleRemove}>
                                    <DeleteOutlineIcon />
                                </IconButton>
                            </Grid>
                        </Grid>
                    </StyledPaper>
                    <Typography variant={'body1'} fontWeight={600} color={'textSecondary'} align={'center'} mb={8}>
                        <Trans
                            i18nKey={'uploadedWrongFile'}
                            t={t}
                            components={{
                                cta: (
                                    <Link
                                        href={'#'}
                                        underline={'hover'}
                                        color={'accent.main'}
                                        onClick={filePicker.handleClick}
                                    />
                                ),
                            }}
                        />
                    </Typography>
                    <InviteClientsDialogActions continueLabel={t('preview')} nextStep={handleContinue} />
                </>
            ) : (
                <>
                    <StyledPaper
                        elevation={0}
                        sx={{ padding: 10, border: `1px dashed ${isDragging ? '#444' : '#afafaf'}` }}
                        {...dragProps}
                    >
                        <Fade in={isDragging} unmountOnExit>
                            <div className={'dropContentInner'} style={{ pointerEvents: 'none' }}>
                                <CloudDownloadIcon className={'upload-icon'} />
                                <Typography variant={'body1'} fontWeight={600} color={'textSecondary'}>
                                    {t('dropContent')}
                                </Typography>
                            </div>
                        </Fade>
                        <Fade in={!isDragging} unmountOnExit>
                            <div className={'dropContentInner'}>
                                <CloudUploadIcon className={'upload-icon'} pointerEvents={'none'} />
                                <Typography variant={'body1'} fontWeight={600} color={'textSecondary'}>
                                    <Trans
                                        i18nKey={'dragAndDropHere'}
                                        t={t}
                                        components={{
                                            cta: (
                                                <Link
                                                    href={'#'}
                                                    underline={'hover'}
                                                    color={'accent.main'}
                                                    onClick={filePicker.handleClick}
                                                />
                                            ),
                                        }}
                                    />
                                </Typography>
                            </div>
                        </Fade>
                    </StyledPaper>

                    <Link
                        href={'#'}
                        onClick={handleTemplateStep}
                        fontWeight={500}
                        color={'textSecondary'}
                        underline={'hover'}
                    >
                        {t('dontHaveTemplate')}
                    </Link>
                </>
            )}
        </InviteClientsDialogStepTransition>
    );
}
