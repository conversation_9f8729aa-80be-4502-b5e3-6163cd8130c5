import { computeStepDirection, computeStepsDirection, InviteClientsSteps } from './inviteClientsStore';

describe('InviteClientsStore', () => {
    // TODO: fix directions
    it('should detect the right direction of the step', function () {
        expect(computeStepDirection(2, 2, 1)).toBe('left');
        expect(computeStepDirection(2, 1, 2)).toBe('left');
    });

    it('should detect the right direction of the steps', function () {
        expect(computeStepsDirection(2, 1)).toMatchObject({
            [InviteClientsSteps.None]: 'right',
            [InviteClientsSteps.MultipleClientsGettingStarted]: 'left',
            [InviteClientsSteps.MultipleClientsUpload]: 'left',
            [InviteClientsSteps.MultipleClientsReview]: 'left',
        });

        expect(computeStepsDirection(1, 2)).toMatchObject({
            [InviteClientsSteps.None]: 'right',
            [InviteClientsSteps.MultipleClientsGettingStarted]: 'left',
            [InviteClientsSteps.MultipleClientsUpload]: 'left',
            [InviteClientsSteps.MultipleClientsReview]: 'left',
        });
    });
});
