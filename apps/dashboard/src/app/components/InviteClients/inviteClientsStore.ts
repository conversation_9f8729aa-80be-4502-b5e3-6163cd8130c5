import { SlideProps } from '@mui/material/Slide';
import { create } from 'zustand';

export enum InviteClientsSteps {
    None = 0,
    Choose = 1,
    MultipleClientsGettingStarted = 2,
    MultipleClientsUpload = 3,
    MultipleClientsReview = 4,
    SingleClientInput = 5,
    SingleClientSuccess = 6,
}

export interface InvitationRecord {
    displayName: string;
    phoneNumber: string;
}

interface InviteClientsStore {
    isOpen: boolean;
    step: InviteClientsSteps;
    directions: Record<InviteClientsSteps, SlideProps['direction']>;
    prevStep: InviteClientsSteps | null;
    nextStep: InviteClientsSteps | null;
    invitations: InvitationRecord[];
    close(): void;
    open(): void;
    setStep(step: InviteClientsSteps, skipNextStep?: boolean): void;
    reset(): void;
    setInvitations(data: InvitationRecord[]): void;
}

export function computeStepDirection(
    step: InviteClientsSteps,
    currentStep: InviteClientsSteps,
    previousStep: InviteClientsSteps,
) {
    const isForward = currentStep > previousStep;

    if (isForward && step >= currentStep) {
        return 'left';
    }

    if (!isForward && step >= previousStep) {
        return 'left';
    }

    return 'right';
}

export function computeStepsDirection(currentStep: InviteClientsSteps, previousStep: InviteClientsSteps) {
    const steps = [
        InviteClientsSteps.None,
        InviteClientsSteps.Choose,
        InviteClientsSteps.MultipleClientsGettingStarted,
        InviteClientsSteps.MultipleClientsUpload,
        InviteClientsSteps.MultipleClientsReview,
        InviteClientsSteps.SingleClientInput,
        InviteClientsSteps.SingleClientSuccess,
    ];

    return steps.reduce(
        (acc, step) => ({
            ...acc,
            [step]: computeStepDirection(step, currentStep, previousStep ?? InviteClientsSteps.None),
        }),
        {} as InviteClientsStore['directions'],
    );
}

export const useInviteClientsStore = create<InviteClientsStore>((set, get) => ({
    isOpen: false,
    step: InviteClientsSteps.Choose,
    prevStep: InviteClientsSteps.None,
    nextStep: null,
    directions: {
        [InviteClientsSteps.None]: 'left',
        [InviteClientsSteps.Choose]: 'right',
        [InviteClientsSteps.MultipleClientsGettingStarted]: 'left',
        [InviteClientsSteps.MultipleClientsUpload]: 'left',
        [InviteClientsSteps.MultipleClientsReview]: 'left',
        [InviteClientsSteps.SingleClientInput]: 'left',
        [InviteClientsSteps.SingleClientSuccess]: 'left',
    },
    invitations: [],
    close() {
        get().reset();
    },
    open() {
        set({ isOpen: true });
    },
    setStep(step: InviteClientsSteps, skipNextStep = false) {
        if (skipNextStep) {
            set({ step, nextStep: null });
            const directions = computeStepsDirection(step, get().prevStep ?? InviteClientsSteps.None);
            set({ directions });
        } else {
            const currentStep = get().step;
            set({ prevStep: currentStep, step: InviteClientsSteps.None, nextStep: step });
        }
    },
    reset() {
        set({ isOpen: false, invitations: [] });
        setTimeout(() => set({ step: InviteClientsSteps.Choose }), 300);
    },
    setInvitations(invitations: InvitationRecord[]) {
        set({ invitations });
    },
}));
