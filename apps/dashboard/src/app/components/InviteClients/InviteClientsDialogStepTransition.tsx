import DialogContent from '@mui/material/DialogContent';
import Slide from '@mui/material/Slide';
import { PropsWithChildren, useCallback, useRef } from 'react';
import { InviteClientsSteps, useInviteClientsStore } from './inviteClientsStore';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function InviteClientsDialogStepTransition({
    children,
    step: transitionStep,
}: PropsWithChildren<{ step: InviteClientsSteps }>) {
    const directions = useInviteClientsStore((state) => state.directions);
    const step = useInviteClientsStore((state) => state.step);
    const nextStep = useInviteClientsStore((state) => state.nextStep);
    const setStep = useInviteClientsStore((state) => state.setStep);
    const prevRef = useRef<InviteClientsSteps | null>(null);

    const handleExit = useCallback(() => {
        if (step === InviteClientsSteps.None && nextStep) {
            setStep(nextStep, true);
        }
    }, [step, nextStep, setStep]);

    const handleEnter = useCallback(() => {
        if (step !== InviteClientsSteps.None) {
            prevRef.current = step;
        }
    }, [step]);

    return (
        <Slide
            in={step === transitionStep}
            direction={directions[transitionStep]}
            onEnter={handleEnter}
            onExited={handleExit}
            unmountOnExit
        >
            <DialogContent>{children}</DialogContent>
        </Slide>
    );
}
