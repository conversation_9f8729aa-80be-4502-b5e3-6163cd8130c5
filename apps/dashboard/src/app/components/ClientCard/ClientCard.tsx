import BlockIcon from '@mui/icons-material/Block';
import DeleteIcon from '@mui/icons-material/Delete';
import BookIcon from '@mui/icons-material/Event';
import EmailIcon from '@mui/icons-material/MailOutline';
import Avatar from '@mui/material/Avatar';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import MuiLink from '@mui/material/Link';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { AxiosError } from 'axios';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useCallback, useEffect, useState } from 'react';
import { emitCustomEvent } from 'react-custom-events';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { StatsModel } from '@bookr-technologies/api/models/StatsModel';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { useBusinessStore } from '@bookr-technologies/store/businessStore';
import { ConfirmationDialog } from '@bookr-technologies/ui/ConfirmationDialog';
import { CustomEvent } from '../../enums/CustomEvent';
import { useBlockClientMutation, useDeleteClientMutation } from '../../utils/clients';

const StyledAvatar = styled(Avatar)`
    width: 84px;
    height: 84px;
    margin-right: 24px;
`;

const AppointmentItem = styled('span')`
    font-size: 14px;
    font-weight: 500;
    color: #757575;
    margin: 0;
    flex: 1;
`;

const AppointmentNumber = styled('span')`
    color: #111111;
    font-weight: 500;
    padding-right: 4px;
`;

const StyledContainer = styled('div')`
    width: 100%;
    border-radius: 8px;
    background: #f6f6f6;
    padding: 12px 14px;
    margin-top: 18px;
`;

const StyleButton = styled(Button)`
    background: #ffffff;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1) !important;
    border-radius: 13px;
    padding: 16px;
    min-width: 53px;
`;

const StyledButtonLabel = styled('span')`
    margin-top: 8px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    color: #757575;
`;

interface ClientCardProps {
    client?: UserModel;
    stats?: StatsModel;
}

function ClientCard({ client, stats }: ClientCardProps) {
    const { t } = useTranslation('clientPage');
    const { t: commonT } = useTranslation('common');
    const { enqueueSnackbar } = useSnackbar();
    const business = useBusinessStore((state) => state.business);
    const fetchCurrentUserBusiness = useBusinessStore((state) => state.fetchCurrentUserBusiness);
    const [isBlockModalOpen, setBlockModalOpen] = useState(false);
    const [isDeleteClientModalOpen, setDeleteClientModal] = useState(false);
    const blockClientMutation = useBlockClientMutation();
    const deleteClientMutation = useDeleteClientMutation();
    const navigate = useNavigate();

    const TotalRevenues = Object.keys(stats?.totalRevenue || {}).map((key) => (
        <span key={key}>
            {stats?.totalRevenue[key]} {key}
        </span>
    ));

    const handleBlock = useCallback(() => {
        blockClientMutation.mutate({ uid: client?.uid || '', value: true });
    }, [blockClientMutation, client?.uid]);

    const handleDelete = useCallback(() => {
        deleteClientMutation.mutate(client?.uid || '');
        setDeleteClientModal(false);
        navigate('/clients');
    }, [deleteClientMutation, client?.uid, navigate]);

    useEffect(() => {
        if (blockClientMutation.isSuccess) {
            fetchCurrentUserBusiness();
            enqueueSnackbar(
                commonT(blockClientMutation.variables?.value ? 'blockedSuccessfully' : 'unblockedSuccessfully', {
                    displayName: client?.displayName,
                }),
                {
                    variant: 'success',
                },
            );
            blockClientMutation.reset();
            setBlockModalOpen(false);
        } else if (blockClientMutation.isError) {
            enqueueSnackbar((blockClientMutation.error as AxiosError<any>).response?.data.message, {
                variant: 'error',
            });
            setBlockModalOpen(false);
        }
    }, [blockClientMutation, client?.displayName, enqueueSnackbar, fetchCurrentUserBusiness, commonT]);

    return (
        <>
            <Box display={'flex'} alignContent={'center'}>
                <ListItemAvatar>
                    <StyledAvatar alt={client?.displayName} src={client?.photoURL} />
                </ListItemAvatar>
                <ListItemText
                    primary={
                        <Typography
                            variant={'h5'}
                            component={'h2'}
                            lineHeight={'32px'}
                            marginBottom={'24px'}
                            fontWeight={'bold'}
                        >
                            {client?.displayName}
                        </Typography>
                    }
                    secondary={
                        <AppointmentItem>
                            <AppointmentNumber>{stats?.totalAppointments}</AppointmentNumber>
                            {t('totalAppointments')}
                        </AppointmentItem>
                    }
                />
            </Box>
            <Box display={'flex'} sx={{ my: 2 }}>
                <AppointmentItem>
                    <AppointmentNumber>{stats?.totalFinished}</AppointmentNumber>
                    {t('completed')}
                </AppointmentItem>
                <AppointmentItem>
                    <AppointmentNumber>{stats?.totalCancelled}</AppointmentNumber>
                    {t('cancelled')}
                </AppointmentItem>
                <AppointmentItem>
                    <AppointmentNumber>{stats?.totalNoShows}</AppointmentNumber>
                    {t('noShow')}
                </AppointmentItem>
            </Box>
            <StyledContainer>
                <Box display={'flex'} justifyContent={'space-between'} width={'100%'} padding={2}>
                    <Typography variant={'body1'} color={'#757575'} fontWeight={500}>{`${t(
                        'clientRevenue',
                    )}:`}</Typography>
                    <Typography variant={'body1'} fontWeight={600} fontSize={18}>
                        {TotalRevenues}
                    </Typography>
                </Box>
                {client?.phoneNumber ? (
                    <Box display={'flex'} justifyContent={'space-between'} width={'100%'} padding={2}>
                        <Typography variant={'body1'} color={'#757575'} fontWeight={500}>
                            {commonT('phone')}
                        </Typography>
                        <Typography variant={'body1'} fontWeight={600} fontSize={18}>
                            {client?.phoneNumber}
                        </Typography>
                    </Box>
                ) : null}
            </StyledContainer>
            <Box display={'flex'} flexDirection={'row'} justifyContent={'space-evenly'} marginTop={'26px'}>
                {client?.email && (
                    <Box display={'flex'} flex={1} alignItems={'center'} flexDirection={'column'}>
                        <MuiLink href={`mailto:${client.email}`} target={'_blank'}>
                            <StyleButton size="small">
                                <EmailIcon />
                            </StyleButton>
                        </MuiLink>
                        <StyledButtonLabel>{commonT('email')}</StyledButtonLabel>
                    </Box>
                )}
                <Box display={'flex'} flex={1} alignItems={'center'} flexDirection={'column'}>
                    <StyleButton
                        size="small"
                        onClick={() => emitCustomEvent(CustomEvent.HIDE_SHOW_NEW_APPOINTMENT_DIALOG, { client })}
                    >
                        <BookIcon />
                    </StyleButton>
                    <StyledButtonLabel>{commonT('book')}</StyledButtonLabel>
                </Box>
                <Box display={'flex'} flex={1} alignItems={'center'} flexDirection={'column'}>
                    <StyleButton
                        size="small"
                        onClick={() => {
                            if (business?.blocked.includes(client?.uid || '')) {
                                blockClientMutation.mutate({ uid: client?.uid || '', value: false });
                            } else {
                                setBlockModalOpen(true);
                            }
                        }}
                    >
                        <BlockIcon />
                    </StyleButton>
                    <StyledButtonLabel>
                        {business?.blocked.includes(client?.uid || '') ? commonT('unblock') : commonT('block')}
                    </StyledButtonLabel>
                </Box>
                <Box display={'flex'} flex={1} alignItems={'center'} flexDirection={'column'}>
                    <StyleButton size="small" onClick={() => setDeleteClientModal(true)}>
                        <DeleteIcon />
                    </StyleButton>
                    <StyledButtonLabel>{commonT('delete')}</StyledButtonLabel>
                </Box>
            </Box>
            <ConfirmationDialog
                open={isBlockModalOpen}
                title={commonT('blockClientTitle', { displayName: client?.displayName.split(' ')[0] })}
                message={commonT('blockClientMessage', { displayName: client?.displayName.split(' ')[0] })}
                cancelText={commonT('cancel')}
                submitText={commonT('block')}
                isDestructiveAction
                onSubmit={handleBlock}
                onClose={() => setBlockModalOpen(false)}
            />
            <ConfirmationDialog
                open={isDeleteClientModalOpen}
                title={commonT('deleteClientTitle', { displayName: client?.displayName.split(' ')[0] })}
                message={commonT('deleteClientMessage', { displayName: client?.displayName.split(' ')[0] })}
                cancelText={commonT('cancel')}
                submitText={commonT('delete')}
                isDestructiveAction
                onSubmit={handleDelete}
                onClose={() => setDeleteClientModal(false)}
            />
        </>
    );
}

export default ClientCard;
