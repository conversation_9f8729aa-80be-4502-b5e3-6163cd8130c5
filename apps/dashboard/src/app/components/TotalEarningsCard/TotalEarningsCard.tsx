import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Typography from '@mui/material/Typography';
import { alpha, styled, useTheme } from '@mui/material/styles';
import classNames from 'classnames';
import moment, { Moment } from 'moment';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Area, AreaChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { formatNumber } from '@bookr-technologies/core/utils/formatters';
import { PrivilegeBasedAccess } from '@bookr-technologies/ui/PrivilegeBasedAccess';
import { StyledCustomTooltip } from './atoms';

export interface TotalEarningsCardProps {
    total: number;
    percent: number;
    currency: string;
    series: {
        time: Moment;
        total: number;
    }[];
}

const Root = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3, 4),
    flex: '1 1 auto',
    position: 'relative',
    minHeight: 372,
    '.TotalEarningsCard-chart': {
        position: 'absolute',
        top: theme.spacing(14),
        left: theme.spacing(4),
        right: theme.spacing(4),
        bottom: theme.spacing(3),
    },
    '.TotalEarningsCard-percentDiff': {
        padding: theme.spacing(0.25, 1),
        marginLeft: theme.spacing(1),
        lineHeight: theme.spacing(2.5),
        borderRadius: 7,
        color: theme.palette.accent.main,
        backgroundColor: alpha(theme.palette.accent.main, 0.1),
        '&.lower': {
            color: theme.palette.error.main,
            backgroundColor: alpha(theme.palette.error.main, 0.1),
        },
        '&.higher': {
            color: theme.palette.success.main,
            backgroundColor: alpha(theme.palette.success.main, 0.1),
        },
        '&.link': {
            cursor: 'pointer',
            transition: theme.transitions.create(['background-color']),
            '&:hover': {
                backgroundColor: alpha(theme.palette.accent.main, 0.2),
            },
        },
    },
}));

export function TotalEarningsCard({ total, percent, currency, series }: TotalEarningsCardProps) {
    const { t } = useTranslation('totalEarningsCard');
    const theme = useTheme();
    const percentValue = useMemo(() => formatNumber(percent), [percent]);
    const totalValue = useMemo(() => formatNumber(total), [total]);
    const data = useMemo(
        () =>
            series.map(({ total, time }) => ({
                name: time.format('D'),
                label: time.year() !== moment().year() ? time.format('ll') : time.format('MMM DD'),
                total: Number(total),
                currency,
            })),
        [series, currency],
    );

    return (
        <Root>
            <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                {t('totalEarnings')}
            </Typography>
            <Grid container alignItems={'center'}>
                <Typography variant={'h4'} fontWeight={700}>
                    {totalValue} {currency ?? ''}
                </Typography>

                {percent !== 0 ? (
                    <Typography
                        className={classNames('TotalEarningsCard-percentDiff', {
                            lower: percent < 0,
                            higher: percent > 0,
                        })}
                        variant={'caption'}
                        fontWeight={600}
                    >
                        {percentValue}%
                    </Typography>
                ) : null}
            </Grid>
            <div className={'TotalEarningsCard-chart'}>
                <ResponsiveContainer>
                    <AreaChart data={data}>
                        <defs>
                            <linearGradient id="colorUv" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="-263%" stopColor="rgba(47, 128, 250, 0.6)" />
                                <stop offset="100%" stopColor="rgba(47, 128, 250, 0)" />
                            </linearGradient>
                        </defs>

                        <CartesianGrid vertical={false} stroke={'#eee'} />
                        <XAxis dataKey="name" axisLine={false} tickLine={false} tickMargin={16} minTickGap={24} />
                        <YAxis axisLine={false} tickLine={false} tickMargin={16} />
                        <Tooltip content={<CustomTooltip />} animationDuration={600} />
                        <Area
                            animationDuration={600}
                            dataKey="total"
                            stroke={theme.palette.accent.main}
                            strokeWidth={4}
                            strokeLinejoin={'round'}
                            strokeLinecap={'round'}
                            fill="url(#colorUv)"
                        />
                    </AreaChart>
                </ResponsiveContainer>
            </div>
            <PrivilegeBasedAccess privileges={[UserPrivilegeType.Standard, UserPrivilegeType.Professional]} />
        </Root>
    );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomTooltip = ({ active, payload }: Record<string, any>) => {
    if (active && payload && payload.length) {
        const { total, currency, label } = payload[0].payload;
        const totalValue = formatNumber(total);
        return (
            <StyledCustomTooltip>
                <Typography variant={'caption'} fontWeight={600}>
                    {totalValue}&nbsp;{currency ?? ''}
                </Typography>
                <Typography variant={'caption'} color={'textSecondary'} fontWeight={500}>
                    {label}
                </Typography>
            </StyledCustomTooltip>
        );
    }

    return null;
};
