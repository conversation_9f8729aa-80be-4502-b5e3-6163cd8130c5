import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import moment from 'moment';
import * as React from 'react';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { NoteModel } from '@bookr-technologies/api/models/NoteModel';
import { NoteCard } from '../NoteCard/NoteCard';
import { NoteDialog } from '../NoteDialog/NoteDialog';
import { AppointmentDetailsActions } from './AppointmentDetailsActions';

const StyledBox = styled(Box)`
    width: 100%;
`;

const StyleSpan = styled('p')`
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
    color: #111111;
    letter-spacing: 0.005em;

    :first-letter {
        text-transform: capitalize;
    }
`;

const StyledServiceTime = styled('span')`
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    color: #757575;
`;

const StyledStaffMember = styled('span')`
    color: #2f80fb;
`;

const StyledServicePrice = styled('span')`
    color: #000;
    font-weight: 600;
    font-size: 24px;
`;

const StyledChip = styled(Chip)`
    background: #fff;
    padding: 12px 4px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;

    &.completed {
        background: #e6f5ee;
        color: #05944f;
    }

    &.cancelled {
        background: #feeeee;
        color: #f05253;
    }

    &.noShow {
        background: #feeeee;
        color: #f05253;
    }

    &.confirmed {
        background: rgba(47, 128, 251, 0.1);
        color: #2f80fb;
    }
`;

const StyledServiceName = styled('span')`
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
    color: #111111;
`;

const StyledButton = styled(Button)`
    box-shadow: none;
    height: 39px;
    border-radius: 10px;

    &:hover {
        background-color: #eeeeee;
        box-shadow: none;
    }
`;

interface AppointmentDetailsProps {
    data: AppointmentModel;
    onChange(): void | Promise<void>;
}

function AppointmentDetails({ data, onChange }: AppointmentDetailsProps) {
    const { t } = useTranslation('appointmentDetailsPage');
    const { t: commonT } = useTranslation('common');

    const [noteDialogOpen, setNoteDialogOpen] = useState(false);
    const [noteData, setNoteData] = useState(new NoteModel());

    const serviceDate = moment(data.dateTime).format('dddd, DD MMM YYYY');
    const serviceStartTime = moment(data.dateTime).format('HH:mm');
    const serviceEndTime = moment(data.dateTime).add(data.service.duration, 'minutes').format('HH:mm');

    const isAppointmentCompleted = moment(data.dateTime).add(data.service.duration, 'minutes').diff(Date.now()) < 0;

    let serviceLabel;
    if (data.cancelled) {
        serviceLabel = 'cancelled';
    } else if (data.noShow) {
        serviceLabel = 'noShow';
    } else if (isAppointmentCompleted) {
        serviceLabel = 'completed';
    } else {
        serviceLabel = 'confirmed';
    }

    useEffect(() => {
        return () => {
            if (noteDialogOpen) {
                setNoteData(new NoteModel());
            }
        };
    }, [noteDialogOpen]);

    const NoteList = (data.notes || []).map((note: NoteModel) => (
        <NoteCard note={note} setNoteData={setNoteData} setNoteDialogOpen={setNoteDialogOpen} />
    ));

    return (
        <>
            <NoteDialog
                note={noteData}
                appointment={data}
                onSubmit={onChange}
                onDelete={onChange}
                onClose={() => setNoteDialogOpen(false)}
                open={noteDialogOpen}
            />
            <StyledBox
                borderRadius={4}
                display={'flex'}
                flexDirection={'column'}
                justifyContent={'flex-start'}
                alignItems={'flex-start'}
                minHeight={'100%'}
            >
                <Box display={'flex'} width={'100%'} justifyContent={'space-between'} alignItems={'center'}>
                    <Typography variant={'h5'} component={'h2'} lineHeight={'32px'} fontWeight={'bold'}>
                        {t('title')}
                    </Typography>
                    <Box display={'flex'} alignItems={'center'}>
                        <StyledChip label={commonT(`appointmentLabels.${serviceLabel}`)} className={serviceLabel} />
                        {data.paidOnline && (
                            <StyledChip
                                label={commonT('paidOnline')}
                                className={'completed'}
                                sx={{ marginLeft: '8px' }}
                            />
                        )}
                    </Box>
                </Box>
                <Box
                    display={'flex'}
                    flexDirection={'column'}
                    justifyContent={'space-evenly'}
                    marginTop={'66px'}
                    paddingLeft={'16px'}
                    width={'100%'}
                >
                    <StyleSpan>{serviceDate}</StyleSpan>
                    <StyledBox display={'flex'} alignItems={'center'} justifyContent={'space-between'}>
                        <Box display={'flex'} alignItems={'baseline'}>
                            <StyleSpan>{serviceStartTime}</StyleSpan>
                            <Box
                                display={'flex'}
                                flexDirection={'column'}
                                justifyContent={'space-evenly'}
                                marginLeft={'41px'}
                            >
                                <StyledServiceName>{data.service.name}</StyledServiceName>
                                <StyledServiceTime>
                                    {`${serviceStartTime}-${serviceEndTime} ${commonT('with')} `}
                                    <StyledStaffMember> {`@${data.staff.displayName}`}</StyledStaffMember>
                                </StyledServiceTime>
                            </Box>
                        </Box>

                        <StyledServicePrice>{`${data.service.price} ${data.service.currency}`}</StyledServicePrice>
                    </StyledBox>
                </Box>
                <Box
                    display={'flex'}
                    justifyContent={'space-between'}
                    alignItems={'center'}
                    marginTop={'61px'}
                    width={'100%'}
                >
                    <Typography variant={'h5'} component={'h2'} lineHeight={'32px'} fontWeight={'bold'}>
                        {commonT('notes')}
                    </Typography>
                    <StyledButton
                        variant={'text'}
                        color={'accent'}
                        size={'small'}
                        sx={{ px: '24px' }}
                        onClick={() => setNoteDialogOpen(true)}
                    >
                        {t('addNote')}
                    </StyledButton>
                </Box>
                <Box marginTop={'34px'} width={'100%'}>
                    {NoteList}
                </Box>
                <Box flexGrow={1} />
                <AppointmentDetailsActions appointment={data} />
            </StyledBox>
        </>
    );
}

export default AppointmentDetails;
