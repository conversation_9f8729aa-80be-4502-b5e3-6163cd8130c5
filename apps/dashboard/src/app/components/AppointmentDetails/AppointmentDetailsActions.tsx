/* eslint-disable @typescript-eslint/no-explicit-any */
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import LoadingButton from '@mui/lab/LoadingButton';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Grid from '@mui/material/Grid';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import { SxProps } from '@mui/system/styleFunctionSx';
import moment from 'moment-timezone';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { ChangeEvent, MouseEvent, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';

interface Props {
    appointment: AppointmentModel;
}

export function AppointmentDetailsActions({ appointment }: Props) {
    const { t } = useTranslation('appointmentDetailsPage');
    const { t: common } = useTranslation('common');
    const { t: appointmentPage } = useTranslation('appointmentsPage');
    const [buttonWidth, setButtonWidth] = useState<number>(0);
    const [internalCancelled, setInternalCancelled] = useState(false);
    const [loading, setLoading] = useState(false);
    const [rescheduleDialog, setRescheduleDialog] = useState(false);
    const [appointmentDate, setAppointmentDate] = useState(appointment.dateTime);
    const [imReadyLoading, setImReadyLoading] = useState(false);
    const [anchor, setAnchor] = useState<HTMLElement | null>(null);
    const { enqueueSnackbar } = useSnackbar();
    const clientName = appointment.client.displayName;
    const cancelled = appointment.cancelled;

    const confirm = useConfirmation();

    const handleClose = useCallback(() => setAnchor(null), []);
    const handleOpen = useCallback((event: MouseEvent<HTMLButtonElement>) => {
        setAnchor(event.currentTarget as HTMLElement);
        setButtonWidth(event.currentTarget.clientWidth);
    }, []);

    const handleOptionNoShow = useCallback(async () => {
        setLoading(true);
        handleClose();
        try {
            await appointmentsEndpoint.noShow(appointment.id);
            window.location.reload();
        } catch (e) {
            enqueueSnackbar(t('noShowError'), { variant: 'error' });
        }
        setLoading(false);
    }, [appointment.id, enqueueSnackbar, handleClose, t]);

    const handleOptionCancel = useCallback(async () => {
        setLoading(true);

        try {
            const result = await confirm({
                title: common('cancelConfirmTitle', { clientName }),
                message: common('cancelConfirmMessage'),
                isDestructiveAction: true,
            });

            if (result) {
                await appointmentsEndpoint.cancel(appointment.id);
                setInternalCancelled(true);
                window.location.reload();
            }
        } catch (e) {
            enqueueSnackbar(t('cancelError'), { variant: 'error' });
        }

        handleClose();
        setLoading(false);
    }, [handleClose, confirm, common, clientName, appointment.id, enqueueSnackbar, t]);

    const handleOptionReschedule = useCallback(() => {
        setRescheduleDialog(true);
        handleClose();
    }, [handleClose]);

    const handleAppointmentDate = useCallback(
        (e: ChangeEvent<HTMLInputElement>) => {
            setAppointmentDate(e.target.value);
            handleClose();
        },
        [handleClose],
    );

    const handleCloseRescheduleDialog = useCallback(() => setRescheduleDialog(false), []);

    const handleSubmitRescheduleDialog = useCallback(async () => {
        const timestamp = moment(appointmentDate).tz('UTC').unix();
        const reschedule = () => {
            appointmentsEndpoint
                .update(appointment.id, { timestamp })
                .then(() => window.location.reload())
                .catch(() => {
                    enqueueSnackbar(appointmentPage('appointmentUpdateError'), { variant: 'error' });
                });
        };

        const data = await appointmentsEndpoint.isTimeslotFree(
            appointment.staff.uid,
            timestamp,
            appointment.service.id,
        );

        if (data.free) {
            reschedule();
        } else {
            const { appointment } = data;
            const result = await confirm({
                title: common('slotExpired'),
                message: common('slotExpiredMessage', {
                    start: moment(appointment.dateTime).format('HH:mm'),
                    end: moment(appointment.dateTime).add(appointment.service.duration, 'minutes').format('HH:mm'),
                    displayName: appointment.client.displayName,
                }),
            });

            if (result) {
                reschedule();
            }
        }
        handleCloseRescheduleDialog();
        window.location.reload();
    }, [
        appointmentDate,
        appointment?.staff?.uid,
        appointment?.service?.id,
        appointment?.id,
        handleCloseRescheduleDialog,
        enqueueSnackbar,
        appointmentPage,
        confirm,
        common,
    ]);

    const handleImReady = useCallback(async () => {
        setImReadyLoading(true);

        try {
            const result = await confirm({
                title: t('imReadyConfirmTitle', { clientName }),
                message: t('imReadyConfirmMessage', { clientName }),
                cancelText: t('imReadyConfirmCancel'),
                submitText: t('imReadyConfirmSubmit'),
            });

            if (result) {
                await appointmentsEndpoint.imReady(appointment.id);
            }
        } catch (e) {
            enqueueSnackbar(t('imReadyError'), { variant: 'error' });
        }

        setImReadyLoading(false);
    }, [appointment.id, clientName, confirm, enqueueSnackbar, t]);

    return (
        <Grid container>
            <Dialog open={rescheduleDialog} fullWidth maxWidth={'xs'}>
                <DialogTitle>{appointmentPage('rescheduleAppointment')}</DialogTitle>
                <DialogContent>
                    <Grid container>
                        <TextField
                            type={'datetime-local'}
                            value={moment(appointmentDate).format('yyyy-MM-DD[T]HH:mm')}
                            onChange={handleAppointmentDate}
                            fullWidth
                        />
                    </Grid>
                    <Grid container mt={3} alignItems={'center'} justifyContent={'flex-end'}>
                        <Button onClick={handleCloseRescheduleDialog} color={'error'} size={'small'} sx={{ mr: 2 }}>
                            {common('cancel')}
                        </Button>
                        <Button
                            variant={'contained'}
                            color={'primary'}
                            size={'small'}
                            onClick={handleSubmitRescheduleDialog}
                        >
                            {common('save')}
                        </Button>
                    </Grid>
                </DialogContent>
            </Dialog>
            <Grid item xs pr={2}>
                <Button
                    fullWidth
                    variant="outlined"
                    color="primary"
                    onClick={handleOpen}
                    disabled={loading || cancelled || internalCancelled}
                    endIcon={
                        loading ? (
                            <div style={{ ...(styles.dropDownIcon as any), display: 'inline-flex' }}>
                                <CircularProgress size={24} color={'inherit'} />
                            </div>
                        ) : (
                            <KeyboardArrowDownIcon
                                color={'inherit'}
                                sx={{
                                    ...styles.dropDownIcon,
                                    transform: `translateY(-50%) rotate(${anchor ? 180 : 0}deg)`,
                                }}
                            />
                        )
                    }
                >
                    {t('moreOptions')}
                </Button>
                <Menu
                    open={!!anchor}
                    anchorEl={anchor}
                    onClose={handleClose}
                    anchorOrigin={{
                        vertical: 'top',
                        horizontal: 'center',
                    }}
                    transformOrigin={{
                        vertical: 'bottom',
                        horizontal: 'center',
                    }}
                    sx={{
                        '.MuiMenu-paper': {
                            marginTop: -2,
                            minWidth: buttonWidth ?? 0,
                        },
                    }}
                >
                    <MenuItem onClick={handleOptionNoShow}>{t('absentClient')}</MenuItem>
                    <MenuItem onClick={handleOptionReschedule}>{t('reschedule')}</MenuItem>
                    <MenuItem onClick={handleOptionCancel}>{t('cancel')}</MenuItem>
                </Menu>
            </Grid>
            <Grid item xs pl={2}>
                <LoadingButton
                    loading={imReadyLoading}
                    variant={'contained'}
                    color={'primary'}
                    onClick={handleImReady}
                    disabled={loading || cancelled || internalCancelled}
                    fullWidth
                    disableElevation
                >
                    {t('imReady')}
                </LoadingButton>
            </Grid>
        </Grid>
    );
}

const styles: Record<string, SxProps> = {
    dropDownIcon: {
        position: 'absolute',
        top: '50%',
        right: 16,
        transform: 'translateY(-50%)',
    },
};
