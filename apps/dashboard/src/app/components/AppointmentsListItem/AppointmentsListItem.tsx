import Box from '@mui/material/Box';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import { styled } from '@mui/material/styles';
import moment from 'moment';
import * as React from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';

const StyledBox = styled(Box)`
    width: 100%;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
`;

const StyledServiceDate = styled('div')`
    background: #f6f6f6;
    border-radius: 13px;
    padding: 9px;
    min-width: 75px;
    min-height: 75px;
    display: flex;
    flex-direction: column;
    align-items: center;
`;

const StyledDay = styled('span')`
    font-family: 'Plus Jakarta Display', Poppins, serif;
    font-weight: bold;
    font-size: 24px;
    line-height: 31px;
`;

const StyledMonth = styled('span')`
    font-weight: 500;
    font-size: 18px;
    text-transform: uppercase;
    color: #757575;
`;

const StyledServiceName = styled('span')`
    font-weight: 600;
    font-size: 18px;
    line-height: 30px;
    color: #111111;
`;

const StyledServiceTime = styled('span')`
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    color: #757575;
`;

const StyledStaffMember = styled('span')`
    color: #2f80fb;
`;

const StyledServicePrice = styled('span')`
    color: #000;
    font-weight: 600;
    font-size: 18px;
`;

const StyledChip = styled(Chip)`
    background: #fff;
    padding: 12px 4px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;

    &.completed {
        background: #e6f5ee;
        color: #05944f;
    }

    &.cancelled {
        background: #feeeee;
        color: #f05253;
    }

    &.noShow {
        background: #feeeee;
        color: #f05253;
    }

    &.confirmed {
        background: rgba(47, 128, 251, 0.1);
        color: #2f80fb;
    }
`;

const StyledLink = styled(Link)`
    text-decoration: none;
    color: unset;

    &:focus,
    &:hover,
    &:visited,
    &:link,
    &:active {
        text-decoration: none;
        color: unset;
    }
`;

interface AppointmentsListItemProps {
    data: AppointmentModel;
}

function AppointmentsListItem({ data }: AppointmentsListItemProps) {
    const { t } = useTranslation('clientPage');
    const { t: commonT } = useTranslation('common');
    const serviceMonth = moment(data.dateTime).format('MMM').replace('.', '');
    const serviceDay = moment(data.dateTime).format('DD');
    const serviceStartTime = moment(data.dateTime).format('HH:mm');
    const serviceEndTime = moment(data.dateTime).add(data.service.duration, 'minutes').format('HH:mm');
    const staffMember = data.staff.displayName;

    const isAppointmentCompleted = moment(data.dateTime).add(data.service.duration, 'minutes').diff(Date.now()) < 0;

    let serviceLabel;
    if (data.cancelled) {
        serviceLabel = 'cancelled';
    } else if (data.noShow) {
        serviceLabel = 'noShow';
    } else if (isAppointmentCompleted) {
        serviceLabel = 'completed';
    } else {
        serviceLabel = 'confirmed';
    }

    return (
        <StyledLink to={`/appointments/${data.id}`}>
            <StyledBox display={'flex'} alignItems={'center'} padding={2} borderRadius={4}>
                <StyledServiceDate>
                    <StyledDay>{serviceDay}</StyledDay>
                    <StyledMonth>{serviceMonth}</StyledMonth>
                </StyledServiceDate>
                <Box display={'flex'} flexDirection={'column'} justifyContent={'space-evenly'} marginLeft={1}>
                    <StyledServiceName>{data.service.name}</StyledServiceName>
                    <StyledServiceTime>
                        {`${serviceStartTime}-${serviceEndTime} ${commonT('with')} `}
                        <StyledStaffMember> {`@${staffMember}`}</StyledStaffMember>
                    </StyledServiceTime>
                </Box>
                <Stack display={'flex'} flexDirection={'column'} alignItems={'end'} spacing={1} width={'100%'} flex={1}>
                    <StyledChip label={t(serviceLabel)} className={serviceLabel} />
                    <StyledServicePrice>{`${data.service.price} ${data.service.currency}`}</StyledServicePrice>
                </Stack>
            </StyledBox>
        </StyledLink>
    );
}

export default AppointmentsListItem;
