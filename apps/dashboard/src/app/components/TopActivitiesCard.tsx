import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Skeleton from '@mui/material/Skeleton';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import classNames from 'classnames';
import { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Cell, Pie, PieChart } from 'recharts';
import { SalesResponse } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';

export interface TopActivitiesCardProps {
    serviceSalesActivity: SalesResponse['staffStats'][number]['serviceSalesActivity'];
    totalServices: number;
    loading?: boolean;
}

const Root = styled(Paper)(({ theme }) => ({
    flex: '1 1 auto',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
    padding: theme.spacing(2.75, 3.25),
    '.TopActivitiesCard-chart': {
        position: 'absolute',
        top: 48,
        left: 0,
        bottom: 0,
        width: 248,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        '&.empty': {
            right: 0,
            width: '100%',
        },

        '.recharts-layer.recharts-pie-sector': {
            cursor: 'pointer',
        },

        '.recharts-layer.recharts-sector': {
            transition: theme.transitions.create(['fill', 'stroke']),
        },
    },
    '.TopActivitiesCard-percentage': {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
    },
    '.TopActivitiesCard-legend': {
        paddingLeft: 224,
        display: 'flex',
        flexWrap: 'wrap',
    },
    '.TopActivitiesCard-legendItem': {
        display: 'inline-flex',
        alignItems: 'center',
        padding: theme.spacing(1, 0.75),
        transition: theme.transitions.create(['opacity', 'transform']),
        transform: 'scale(1, 1)',
        '&.scaled': {
            transform: 'scale(1.1, 1.1)',
        },
        '&:not(.highlighted)': {
            opacity: 0.2,
            '.TopActivitiesCard-legendBadge': {
                borderColor: '#ccc !important',
            },
        },
    },
    '.TopActivitiesCard-legendBadge': {
        borderWidth: 5,
        borderStyle: 'solid',
        backgroundColor: '#fff',
        width: 18,
        height: 18,
        borderRadius: 9,
        marginRight: 8,
        transition: theme.transitions.create('border-color'),
    },
}));

export function TopActivitiesCard({ serviceSalesActivity, totalServices, loading }: TopActivitiesCardProps) {
    const { t } = useTranslation('topActivitiesCard');
    const [highlighted, setHighlighted] = useState<ServiceModel | null>(null);
    const data = useMemo(
        () => serviceSalesActivity.sort((a, b) => b.percentage - a.percentage),
        [serviceSalesActivity],
    );

    const handleOver = useCallback(({ payload }: any) => setHighlighted(payload.service), []);
    const handleLeave = useCallback(() => setHighlighted(null), []);

    return (
        <Root>
            <Typography variant={'h5'} fontWeight={700}>
                {t('topActivities')}
            </Typography>
            <Grid container alignItems={'center'}>
                <div className={classNames('TopActivitiesCard-chart', { empty: data.length === 0 })}>
                    <PieChart width={200} height={200}>
                        <Pie
                            data={data}
                            dataKey={'percentage'}
                            nameKey={'service.name'}
                            outerRadius={98}
                            innerRadius={82}
                            paddingAngle={2}
                            onMouseOver={handleOver}
                            onMouseLeave={handleLeave}
                            animationDuration={600}
                        >
                            {data.map((entry, index) => {
                                const isSelected = highlighted?.id === entry.service.id;
                                const color = entry.service.color;
                                return (
                                    <Cell
                                        key={`cell-${index}`}
                                        fill={color}
                                        stroke={color}
                                        strokeWidth={isSelected ? 4 : 0}
                                    />
                                );
                            })}
                        </Pie>
                    </PieChart>
                    {data.length > 0 ? (
                        <div className={'TopActivitiesCard-percentage'}>
                            <Typography variant={'body1'} fontWeight={500} align={'center'} color={'textSecondary'}>
                                {t('totalServices')}
                            </Typography>
                            <Typography variant={'h6'} fontWeight={700} align={'center'}>
                                {loading ? <Skeleton width={84} height={42} /> : totalServices}
                            </Typography>
                        </div>
                    ) : (
                        <div className={'TopActivitiesCard-percentage'}>
                            <Typography variant={'body1'} fontWeight={500} align={'center'} color={'textSecondary'}>
                                {t('noActivities')}
                            </Typography>
                        </div>
                    )}
                </div>

                {data?.length > 0 ? (
                    <Grid item xs mt={3} className={'TopActivitiesCard-legend'}>
                        {data.map((activity) => (
                            <div
                                className={classNames('TopActivitiesCard-legendItem', {
                                    highlighted: !highlighted || activity.service?.id === highlighted?.id,
                                    scaled: activity.service?.id === highlighted?.id,
                                })}
                                key={activity.service.id}
                            >
                                <div
                                    className={'TopActivitiesCard-legendBadge'}
                                    style={{ borderColor: activity.service.color }}
                                />
                                <Tooltip title={activity.service.name}>
                                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'} noWrap>
                                        {activity.service.name}
                                    </Typography>
                                </Tooltip>
                                <Typography variant={'body2'} fontWeight={700} ml={0.5}>
                                    {activity.percentage}%
                                </Typography>
                            </div>
                        ))}
                    </Grid>
                ) : null}
            </Grid>
        </Root>
    );
}
