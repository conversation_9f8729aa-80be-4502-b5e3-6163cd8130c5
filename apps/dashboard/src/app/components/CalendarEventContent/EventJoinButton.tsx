import Videocam from '@mui/icons-material/Videocam';
import Button, { ButtonProps } from '@mui/material/Button';
import { styled } from '@mui/material/styles';
import { tint } from 'polished';
import { useTranslation } from 'react-i18next';

const StyledJoinButton = styled(Button)(({ theme }) => ({
    backgroundColor: tint(0.9, theme.palette.accent.main),
    color: theme.palette.accent.main,
    width: '100%',
    fontSize: 12,
    height: 38,
    justifyContent: 'space-between',
    fontWeight: 600,
    borderRadius: 12,
    marginBottom: 8,
    padding: '0 16px 0 12px',
    '&:hover': {
        backgroundColor: tint(0.8, theme.palette.accent.main),
    },
}));

const StyledJoinLink = styled('a')({ display: 'block', width: '100%', textDecoration: 'none' });

export function EventJoinButton({ href, ...rest }: Omit<ButtonProps, 'endIcon'>) {
    const { t } = useTranslation('common');

    return (
        <StyledJoinLink href={href} target={'_blank'} rel={'noopener noreferrer'}>
            <StyledJoinButton endIcon={<Videocam color={'inherit'} />} {...rest}>
                {t('joinOnlineEvent')}
            </StyledJoinButton>
        </StyledJoinLink>
    );
}
