/* eslint-disable @typescript-eslint/no-explicit-any */
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import EventIcon from '@mui/icons-material/Event';
import LocationOn from '@mui/icons-material/LocationOn';
import NoAccountsIcon from '@mui/icons-material/NoAccounts';
import StickyNote2Icon from '@mui/icons-material/StickyNote2Outlined';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import FormControl from '@mui/material/FormControl';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputLabel from '@mui/material/InputLabel';
import Popover, { PopoverOrigin } from '@mui/material/Popover';
import Tooltip from '@mui/material/Tooltip/Tooltip';
import Typography from '@mui/material/Typography';
import { styled } from '@mui/material/styles';
import { AxiosError } from 'axios';
import { Form, Formik } from 'formik';
import moment from 'moment-timezone';
import { useSnackbar } from 'notistack';
import * as React from 'react';
import { useState } from 'react';
import { emitCustomEvent } from 'react-custom-events';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { EventApi } from '@fullcalendar/react';
import { appointmentsEndpoint } from '@bookr-technologies/api/endpoints/appointmentsEndpoint';
import { AppointmentModel } from '@bookr-technologies/api/models/AppointmentModel';
import { BreakModel } from '@bookr-technologies/api/models/BreakModel';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { useAuthStore } from '@bookr-technologies/store';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';
import { useConfirmation } from '@bookr-technologies/ui/ConfirmationDialog';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { Switch } from '@bookr-technologies/ui/Switch';
import { UserProfile } from '@bookr-technologies/ui/UserProfile';
import { CustomEvent } from '../../enums/CustomEvent';
import { cancelAppointment } from '../../utils/appointments';
import { deleteBreak } from '../../utils/breaks';
import { SelectField } from '../NewAppointmentDialog/SelectField';
import { EventJoinButton } from './EventJoinButton';

const StyledEvent = styled('div')`
    display: flex;
    flex-direction: column;
    border: 0;
    padding-left: 4px;
    height: 100%;
    width: 100%;

    .row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
    }

    .title {
        letter-spacing: 0.005em;
        font-style: normal;
        font-weight: 600;
        font-size: 12px;
        line-height: 20px;
    }

    .timeslot-details {
        display: flex;
        flex: 1;
        justify-content: flex-end;
        font-size: 12px;
    }

    .break-title {
        color: white;
    }
`;

const StyledPopover = styled(Popover)`
    box-shadow: 0 11px 20px rgba(5, 16, 55, 0.1);
    margin-left: 12px;
    margin-top: -4px;

    .MuiPopover-paper {
        border-radius: 12px;
        padding: 16px;
        width: 324px;
    }
`;

interface EventContentProps {
    event: EventApi;
    refetchEvents?: () => void;
}

const StyledEventDetailHeader = styled(Typography)`
    min-width: 180px;
    color: #111;
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 20px;
    letter-spacing: 0.005em;
`;
const StyledEventDetailHeaderTime = styled(Typography)`
    min-width: 180px;
    color: ${({ theme }) => theme.palette.grey['400']};
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0.005em;
    margin-bottom: 8px;
`;

const StyledEventDetailContent = styled('div')`
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    flex-direction: column;
    padding: 12px;
    background-color: #f6f6f6;
    margin-top: 8px;
    margin-bottom: 8px;
    border-radius: 12px;
`;

const StyledRowTitle = styled(Typography)`
    font-style: normal;
    font-weight: 600;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0.005em;
    color: #000;
`;

const StyledRowContent = styled(Typography)`
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: 0.005em;

    color: #757575;
`;

const StyledUserDetailsContainer = styled('div')`
    display: flex;
    flex-direction: row;
    align-items: center;
    background: #f6f6f6;
    border-radius: 12px;
    padding: 12px;

    .user-details {
        padding-left: 16px;
    }
`;

const StyledButton = styled(Button)`
    display: flex;
    padding: 10px 35px;
    width: 100%;
    background: #eee;
    border-radius: 10px;
    color: ${({ theme }) => theme.palette.grey['600']};
    text-transform: none;
    font-weight: 600;
    font-size: 12px;

    &.cancel {
        color: #f05253;
        background: transparent;
    }
`;

const StyledIconButton = styled(IconButton)(({ theme }) => ({
    paddingTop: '10px',
    paddingBottom: '10px',
    background: '#eee',
    color: theme.palette.primary.main,
    width: theme.spacing(92 / 8),
    '&, & .MuiTouchRipple-root *': {
        borderRadius: '12px !important',
        '&.cancel': {
            color: '#f05253',
            background: '#f052531a',
        },
    },
}));

const EventDetailedContentRow = ({
    title = '',
    content = null,
    editView = null,
    initialValues,
    onEdit,
}: Record<string, any>) => {
    const { t } = useTranslation('common');
    const [isEditing, setIsEditing] = useState(false);

    const handleSubmit = async (values: any) => {
        setIsEditing(false);
        if (onEdit) {
            await onEdit(values);
        }
    };

    return (
        <Grid container width={'100%'} mb={1.5}>
            <Grid container alignItems={'center'} justifyContent={'space-between'}>
                <StyledRowTitle>{title}</StyledRowTitle>
                {editView && (
                    <IconButton onClick={() => setIsEditing(true)} size={'small'}>
                        <EditIcon fontSize={'small'} />
                    </IconButton>
                )}
            </Grid>
            <StyledRowContent>{content}</StyledRowContent>

            {editView ? (
                <Dialog open={isEditing} onClose={() => setIsEditing(false)}>
                    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
                        <DialogContent>
                            <Grid component={Form} container>
                                <Grid container direction={'column'} mt={1}>
                                    {editView}
                                </Grid>
                                <Grid container justifyContent={'flex-end'} mt={3}>
                                    <Button
                                        onClick={() => setIsEditing(false)}
                                        color={'error'}
                                        size={'small'}
                                        sx={{ mr: 2 }}
                                    >
                                        {t('cancel')}
                                    </Button>
                                    <FormikButton variant={'contained'} color={'primary'} size={'small'}>
                                        {t('save')}
                                    </FormikButton>
                                </Grid>
                            </Grid>
                        </DialogContent>
                    </Formik>
                </Dialog>
            ) : null}
        </Grid>
    );
};

const EditAppointmentIcon = ({ editView = null, initialValues, onEdit }: Record<string, any>) => {
    const { t } = useTranslation('common');
    const [isEditing, setIsEditing] = useState(false);

    const handleSubmit = async (values: any) => {
        setIsEditing(false);
        if (onEdit) {
            await onEdit(values);
        }
    };

    return (
        <Grid>
            <StyledIconButton onClick={() => setIsEditing(true)}>
                <EventIcon />
            </StyledIconButton>

            {editView ? (
                <Dialog open={isEditing} onClose={() => setIsEditing(false)}>
                    <Formik initialValues={initialValues} onSubmit={handleSubmit}>
                        <DialogContent>
                            <Grid component={Form} container>
                                <Grid container direction={'column'} mt={1}>
                                    {editView}
                                </Grid>
                                <Grid container justifyContent={'flex-end'} mt={3}>
                                    <Button
                                        onClick={() => setIsEditing(false)}
                                        color={'error'}
                                        size={'small'}
                                        sx={{ mr: 2 }}
                                    >
                                        {t('cancel')}
                                    </Button>
                                    <FormikButton variant={'contained'} color={'primary'} size={'small'}>
                                        {t('save')}
                                    </FormikButton>
                                </Grid>
                            </Grid>
                        </DialogContent>
                    </Formik>
                </Dialog>
            ) : null}
        </Grid>
    );
};

export const CalendarEventContent = ({ event, refetchEvents, ...rest }: EventContentProps) => {
    const { t } = useTranslation('common');
    const { t: appointmentPageT } = useTranslation('appointmentsPage');
    const { enqueueSnackbar } = useSnackbar();
    const [anchorEl, setAnchorEl] = useState(null);
    const [clientArrivedAtLocation, setClientArrivedAtLocation] = useState(
        (event.extendedProps.appointment as AppointmentModel)?.clientArrivedAtLocation,
    );
    const navigate = useNavigate();
    const handleEventClick = (e: any) => setAnchorEl(e.currentTarget);
    const handleClose = () => setAnchorEl(null);
    const confirm = useConfirmation();
    const user = useCurrentUser();

    const appointment = event.extendedProps.appointment as AppointmentModel;

    const handleCancelBreak = async (b: BreakModel) => {
        try {
            await deleteBreak(b.id);
            useAuthStore.getState().resolveUser();
            if (refetchEvents) {
                await refetchEvents();
            }
        } catch (err) {
            console.log(err);
            const { response } = err as AxiosError<any>;
            enqueueSnackbar(response?.data?.message, { variant: 'error' });
        }
    };

    const handleView = () => {
        navigate(`/appointments/${appointment.id}`);
    };

    const handleCancelAppointment = async (appointment: AppointmentModel) => {
        try {
            const result = await confirm({
                title: t('cancelConfirmTitle', { clientName: appointment.client.displayName }),
                message: t('cancelConfirmMessage'),
                isDestructiveAction: true,
            });

            if (result) {
                await cancelAppointment(appointment.id);
                if (refetchEvents) {
                    await refetchEvents();
                }
            }
        } catch (err) {
            const { response } = err as AxiosError<any>;
            enqueueSnackbar(response?.data?.message, { variant: 'error' });
        }
    };

    const handleNoShow = async (appointment: AppointmentModel) => {
        try {
            const result = await confirm({
                title: t('noShowConfirmTitle', { clientName: appointment.client.displayName }),
                message: t('noShowConfirmMessage'),
            });

            if (result) {
                await appointmentsEndpoint.noShow(appointment.id);
                if (refetchEvents) {
                    await refetchEvents();
                }
            }
        } catch (err) {
            const { response } = err as AxiosError<any>;
            enqueueSnackbar(response?.data?.message, { variant: 'error' });
        }
    };

    const handleUpdateAppointment = async (values: Record<string, any>) => {
        const timestamp = moment(values.start).tz('UTC').unix();
        const currentTimestamp = moment(appointment.dateTime).tz('UTC').unix();
        const selectedServiceId = values.service ? String(values.service) : null;
        const currentServiceId = String(appointment.service.id);

        // Determine what has changed
        const timestampChanged = timestamp !== currentTimestamp;
        const serviceChanged = selectedServiceId && selectedServiceId !== currentServiceId;

        // Prepare update payload
        const updatePayload: { timestamp: number; serviceId?: string } = { timestamp };
        if (serviceChanged) {
            updatePayload.serviceId = selectedServiceId;
        }

        const updateAppointment = () => {
            appointmentsEndpoint
                .update(appointment.id, updatePayload)
                .then(() => {
                    enqueueSnackbar(appointmentPageT('appointmentUpdatedSuccessfully'), { variant: 'success' });
                    emitCustomEvent(CustomEvent.REFETCH_CALENDAR_EVENTS);
                })
                .catch(() => {
                    enqueueSnackbar(appointmentPageT('appointmentUpdateError'), { variant: 'error' });
                });
        };

        // Only check timeslot availability if the timestamp has changed
        if (timestampChanged) {
            // Use the selected service ID for timeslot check, or fall back to current service
            const serviceIdForCheck = serviceChanged ? Number(selectedServiceId) : appointment.service.id;

            const data = await appointmentsEndpoint.isTimeslotFree(appointment.staff.uid, timestamp, serviceIdForCheck);

            if (data.free) {
                updateAppointment();
            } else {
                const { appointment: conflictingAppointment } = data;
                const result = await confirm({
                    title: t('slotExpired'),
                    message: t('slotExpiredMessage', {
                        start: moment(conflictingAppointment.dateTime).format('HH:mm'),
                        end: moment(conflictingAppointment.dateTime)
                            .add(conflictingAppointment.service.duration, 'minutes')
                            .format('HH:mm'),
                        displayName: conflictingAppointment.client.displayName,
                    }),
                });

                if (result) {
                    updateAppointment();
                }
            }
        } else {
            // If only service changed (no timestamp change), proceed directly with update
            updateAppointment();
        }
    };

    const handleClientArrivedAtLocation = (event: React.ChangeEvent<HTMLInputElement>) => {
        setClientArrivedAtLocation(event.target.checked);
        appointmentsEndpoint.clientArrivedAtLocation(appointment.id, event.target.checked);
    };

    const open = Boolean(anchorEl);
    const id = open ? 'simple-popover' : undefined;

    const { isBreak } = event.extendedProps;

    const timeRow = (eventId: number, start: any, end: any, format: string, isBreak?: boolean) => (
        <EventDetailedContentRow
            title={t('time')}
            content={`${moment(start).format(format)} - ${end.format(format)}`}
            initialValues={{
                start: start.format('yyyy-MM-DD[T]HH:mm'),
                service: !isBreak ? appointment.service.id : undefined,
            }}
            onEdit={handleUpdateAppointment}
            editView={
                isBreak ? null : (
                    <>
                        <Typography variant={'body1'} fontWeight={500}>
                            {appointmentPageT('rescheduleAppointment')}
                        </Typography>
                        <Grid container mt={1} spacing={2}>
                            <Grid item xs={12}>
                                <FormikTextField type={'datetime-local'} name={'start'} size={'small'} fullWidth />
                            </Grid>
                            <Grid item xs={12}>
                                <FormControl fullWidth>
                                    <InputLabel id="service-select-label-inline">{t('selectService')}</InputLabel>
                                    <SelectField
                                        name={'service'}
                                        labelId={'service-select-label-inline'}
                                        style={{ paddingTop: 16, paddingBottom: 4 }}
                                        color={'secondary'}
                                        options={appointment.staff.services
                                            .filter((service: ServiceModel) => !service.inactive)
                                            .sort(ServiceModel.sort)
                                            .map((service: ServiceModel) => ({
                                                id: service.id,
                                                name: `${service.name} - ${service.duration} mins`,
                                                color: service.color,
                                            }))}
                                    />
                                </FormControl>
                            </Grid>
                        </Grid>
                    </>
                )
            }
        />
    );

    if (isBreak) {
        const br = event.extendedProps.break as BreakModel;

        return (
            <>
                <StyledEvent aria-describedby={id} onClick={handleEventClick} {...rest}>
                    <div className={'row'}>
                        <div className={`title break-title`}>{event.title}</div>
                        <div className={'timeslot-details'}>
                            {moment(br.fromDateTime).format('HH:mm')}
                            {'-'}
                            {moment(br.toDateTime).format('HH:mm')}
                        </div>
                    </div>
                </StyledEvent>
                <StyledPopover
                    id={id}
                    open={open}
                    anchorEl={anchorEl}
                    onClose={handleClose}
                    anchorOrigin={popoverStyle}
                >
                    <StyledEventDetailHeader>{t('break')}</StyledEventDetailHeader>
                    <StyledEventDetailContent>
                        {timeRow(br.id, moment(br.fromDateTime), moment(br.toDateTime), 'DD MMM, HH:mm a', true)}
                    </StyledEventDetailContent>
                    <StyledButton
                        className={'cancel'}
                        size={'large'}
                        variant={'text'}
                        onClick={() => handleCancelBreak(br)}
                    >
                        {t('cancel')}
                    </StyledButton>
                </StyledPopover>
            </>
        );
    }

    const isAppointmentCompleted =
        moment(appointment.dateTime).add(appointment.service.duration, 'minutes').diff(Date.now()) < 0;
    let serviceLabel;
    if (appointment.cancelled) {
        serviceLabel = 'cancelled';
    } else if (appointment.noShow) {
        serviceLabel = 'noShow';
    } else if (isAppointmentCompleted) {
        serviceLabel = 'completed';
    } else {
        serviceLabel = 'confirmed';
    }

    const Notes = () => (
        <div style={{ display: 'flex', flexDirection: 'column' }}>
            {appointment.notes.map((n) => (
                <>
                    <Typography variant={'caption'} fontWeight={600}>
                        {moment(n.createdAt).format('DD MMM YY, HH:mm')}
                    </Typography>
                    <Typography variant={'caption'}>{n.text}</Typography>
                </>
            ))}
        </div>
    );

    return (
        <>
            <StyledEvent aria-describedby={id} onClick={handleEventClick} {...rest}>
                {clientArrivedAtLocation && (
                    <Tooltip title={t('clientArrivedToLocation')} placement={'top'}>
                        <LocationOn sx={{ fontSize: '16px' }} />
                    </Tooltip>
                )}
                <div className={'row'}>
                    <div className={`title`}>{event.title}</div>
                    <div className={'timeslot-details'}>
                        {appointment.notes && appointment.notes.length > 0 && (
                            <StickyNote2Icon sx={{ width: 16, height: 16, marginRight: '8px' }} />
                        )}
                        {moment(appointment.dateTime).format('HH:mm')}
                        {'-'}
                        {moment(appointment.dateTime).add(appointment.service.duration, 'minutes').format('HH:mm')}
                    </div>
                </div>
            </StyledEvent>
            <StyledPopover id={id} open={open} anchorEl={anchorEl} onClose={handleClose} anchorOrigin={popoverStyle}>
                <Grid display={'flex'} justifyContent={'space-between'} alignItems={'center'} flexDirection={'row'}>
                    <StyledEventDetailHeader>{appointment.service.name}</StyledEventDetailHeader>
                    <IconButton onClick={handleClose}>
                        <CloseIcon />
                    </IconButton>
                </Grid>
                <StyledEventDetailHeaderTime>
                    {moment(appointment.dateTime).format('DD MMM, HH:mm')}
                    {' - '}
                    {moment(appointment.dateTime).add(appointment.service.duration, 'minutes').format('HH:mm')}
                </StyledEventDetailHeaderTime>
                {appointment.onlineEvent?.joinUrl && <EventJoinButton href={appointment.onlineEvent?.joinUrl} />}
                <StyledUserDetailsContainer>
                    <UserProfile
                        src={appointment.client.photoURL}
                        alt={appointment.client.displayName}
                        disableElevation
                    />
                    <div className={'user-details'}>
                        <StyledRowTitle>{appointment.client.displayName}</StyledRowTitle>
                        <StyledRowContent color={'#afafaf !important'}>
                            {appointment.client.phoneNumber}
                        </StyledRowContent>
                    </div>
                </StyledUserDetailsContainer>
                <StyledEventDetailContent>
                    <EventDetailedContentRow
                        title={t('dateAndTime')}
                        content={`${moment(appointment.dateTime).format('DD MMM, HH:mm')} - ${moment(
                            appointment.dateTime,
                        )
                            .add(appointment.service.duration, 'minutes')
                            .format('HH:mm')}`}
                    />
                    <EventDetailedContentRow title={t('staffName')} content={appointment.staff.displayName} />
                    <EventDetailedContentRow
                        title={t('price')}
                        content={`${appointment.service.price} ${appointment.service.currency}`.toLowerCase()}
                    />
                    <EventDetailedContentRow title={t('status')} content={t(`appointmentLabels.${serviceLabel}`)} />
                    {appointment.notes && appointment.notes.length > 0 && (
                        <EventDetailedContentRow title={t('notes')} content={<Notes />} />
                    )}
                    <EventDetailedContentRow
                        title={t('paidOnline')}
                        content={t(appointment.paidOnline ? 'yes' : 'no')}
                    />
                </StyledEventDetailContent>

                <StyledEventDetailContent>
                    <Grid container width={'100%'} alignItems={'center'} justifyContent={'space-between'}>
                        <StyledRowTitle>{t('clientArrivedToLocation')}</StyledRowTitle>
                        <Switch defaultChecked={clientArrivedAtLocation} onChange={handleClientArrivedAtLocation} />
                    </Grid>
                </StyledEventDetailContent>

                <Grid
                    container
                    width={'100%'}
                    alignItems={'center'}
                    justifyContent={'space-between'}
                    flexDirection={'row'}
                    mb={1}
                >
                    <EditAppointmentIcon
                        initialValues={{
                            start: moment(appointment.dateTime).format('yyyy-MM-DD[T]HH:mm'),
                            service: appointment.service.id,
                        }}
                        onEdit={handleUpdateAppointment}
                        editView={
                            <>
                                <Typography variant={'body1'} fontWeight={500}>
                                    {appointmentPageT('rescheduleAppointment')}
                                </Typography>
                                <Grid container mt={1} spacing={2}>
                                    <Grid item xs={12}>
                                        <FormikTextField
                                            type={'datetime-local'}
                                            name={'start'}
                                            size={'small'}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid item xs={12}>
                                        <FormControl fullWidth>
                                            <InputLabel id="service-select-label">{t('selectService')}</InputLabel>
                                            <SelectField
                                                name={'service'}
                                                labelId={'service-select-label'}
                                                style={{ paddingTop: 16, paddingBottom: 4 }}
                                                color={'secondary'}
                                                options={
                                                    user?.services
                                                        ?.filter((service: ServiceModel) => !service.inactive)
                                                        ?.sort(ServiceModel.sort)
                                                        ?.map((service: ServiceModel) => ({
                                                            id: service.id,
                                                            name: `${service.name} - ${service.duration} mins`,
                                                            color: service.color,
                                                        })) ?? []
                                                }
                                            />
                                        </FormControl>
                                    </Grid>
                                </Grid>
                            </>
                        }
                    />
                    <StyledIconButton onClick={() => handleNoShow(appointment)}>
                        <NoAccountsIcon />
                    </StyledIconButton>
                    <StyledIconButton className={'cancel'} onClick={() => handleCancelAppointment(appointment)}>
                        <DeleteIcon />
                    </StyledIconButton>
                </Grid>

                <Button onClick={handleView} size={'small'} fullWidth variant={'contained'} color={'accent'}>
                    {t('viewDetails')}
                </Button>
            </StyledPopover>
        </>
    );
};

const popoverStyle: PopoverOrigin = {
    vertical: 'top',
    horizontal: 'right',
};
