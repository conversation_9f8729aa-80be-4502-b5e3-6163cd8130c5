/* eslint-disable @typescript-eslint/ban-ts-comment */
import Container, { ContainerProps } from '@mui/material/Container';
import { styled } from '@mui/material/styles';
import { forwardRef, HTMLAttributes, PropsWithChildren, useEffect, useMemo } from 'react';
import { createGlobalStyle } from 'styled-components';
import { PageStoreType, usePageStore } from '@bookr-technologies/store/pageStore';

export interface PageProps extends Omit<HTMLAttributes<HTMLElement>, 'title'> {
    title?: PageStoreType['title'];
    subtitle?: PageStoreType['subtitle'];

    backToLabel?: string;
    backToHref?: string;
    backgroundColor?: string;
    disableGutters?: boolean;
    fluid?: boolean;
    fullscreen?: boolean;

    onFullscreen?: (fullscreen: boolean) => void;
}

const GlobalStyle = createGlobalStyle`
    body {
        background-color: ${(props: PageProps) => props.backgroundColor || '#eee'};
    }
`;

const StyledRoot = styled('main')(({ theme }) => ({
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    paddingBottom: theme.spacing(4),
    '&, .Page-container': {
        flex: '1 1 auto',
    },
}));

async function requestFullscreen(element: HTMLElement) {
    if (element.requestFullscreen) {
        await element.requestFullscreen();
        // @ts-ignore
    } else if (element.mozRequestFullScreen) {
        // @ts-ignore
        await element.mozRequestFullScreen();
        // @ts-ignore
    } else if (element.webkitRequestFullscreen) {
        // @ts-ignore
        await element.webkitRequestFullscreen();
        // @ts-ignore
    } else if (element.webkitRequestFullScreen) {
        // @ts-ignore
        await element.webkitRequestFullScreen();
    }
}

async function exitFullscreen() {
    if (document.exitFullscreen) {
        await document.exitFullscreen();
        // @ts-ignore
    } else if (document.mozCancelFullscreen) {
        // @ts-ignore
        await document.mozCancelFullscreen();
        // @ts-ignore
    } else if (document.webkitExitFullscreen) {
        // @ts-ignore
        await document.webkitExitFullscreen();
        // @ts-ignore
    } else if (document.webkitCancelFullscreen) {
        // @ts-ignore
        await document.webkitCancelFullscreen();
        // @ts-ignore
    } else if (document.webkitExitFullScreen) {
        // @ts-ignore
        await document.webkitExitFullScreen();
        // @ts-ignore
    } else if (document.webkitCancelFullScreen) {
        // @ts-ignore
        await document.webkitCancelFullScreen();
    }
}

function fullscreenElement() {
    return (
        document.fullscreenElement ||
        // @ts-ignore
        document.webkitFullscreenElement ||
        // @ts-ignore
        document.webkitCurrentFullScreenElement ||
        // @ts-ignore
        document.mozFullScreenElement
    );
}

export const Page = forwardRef<HTMLElement, PropsWithChildren<PageProps>>(
    (
        {
            subtitle,
            title,
            backToHref,
            backToLabel,
            fullscreen,
            onFullscreen,
            children,
            fluid,
            backgroundColor,
            disableGutters,
            ...rest
        },
        ref,
    ) => {
        const setTitle = usePageStore((state) => state.setTitle);
        const setSubtitle = usePageStore((state) => state.setSubtitle);
        const setBackToHref = usePageStore((state) => state.setBackToHref);
        const setBackToLabel = usePageStore((state) => state.setBackToLabel);
        const setFullscreen = usePageStore((state) => state.setFullscreen);

        useEffect(() => {
            setTitle(title ?? null);
        }, [setTitle, title]);

        useEffect(() => {
            setSubtitle(subtitle ?? null);
        }, [setSubtitle, subtitle]);

        useEffect(() => {
            setBackToHref(backToHref ?? null);
        }, [setBackToHref, backToHref]);

        useEffect(() => {
            setBackToLabel(backToLabel ?? null);
        }, [setBackToLabel, backToLabel]);

        const content = useMemo(() => {
            const rest: ContainerProps = {
                disableGutters,
            };

            if (fluid || fullscreen) {
                rest.maxWidth = false;
            }

            return (
                <Container className={'Page-container'} {...rest}>
                    {children}
                </Container>
            );
        }, [fluid, fullscreen, children, disableGutters]);

        useEffect(() => {
            const handleFullscreen = () => {
                if (onFullscreen) {
                    const isFullscreen = !!fullscreenElement();
                    onFullscreen(isFullscreen);
                    setFullscreen(isFullscreen);
                }
            };

            document.body.addEventListener('fullscreenchange', handleFullscreen);
            document.body.addEventListener('webkitfullscreenchange', handleFullscreen);
            document.body.addEventListener('mozfullscreenchange', handleFullscreen);
            return () => {
                document.body.removeEventListener('fullscreenchange', handleFullscreen);
                document.body.removeEventListener('webkitfullscreenchange', handleFullscreen);
                document.body.removeEventListener('mozfullscreenchange', handleFullscreen);
            };
        }, [onFullscreen, setFullscreen]);

        useEffect(
            () => {
                (async () => {
                    const makeFullscreen = fullscreen && !fullscreenElement();
                    if (makeFullscreen) {
                        try {
                            await requestFullscreen(document.body);
                            setFullscreen(true);
                        } catch (e) {
                            console.error('error open fullscreen', e);
                        }
                    } else if (fullscreenElement()) {
                        try {
                            await exitFullscreen();
                            setFullscreen(false);
                        } catch (e) {
                            console.error('error close fullscreen', e);
                        }
                    }
                    setTimeout(() => {
                        window.dispatchEvent(new Event('resize'));
                    }, 100);
                })();
            },
            // eslint-disable-next-line react-hooks/exhaustive-deps
            [fullscreen],
        );

        return (
            <StyledRoot ref={ref} {...rest}>
                <GlobalStyle backgroundColor={backgroundColor} />
                {content}
            </StyledRoot>
        );
    },
);
