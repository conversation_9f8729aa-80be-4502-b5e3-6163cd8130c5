import { t } from 'i18next';
import * as Yup from 'yup';

export const serviceDialogValidationSchema = Yup.object().shape({
    name: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    price: Yup.number()
        .required(t('errors.requiredField', { ns: 'common' }))
        .min(0, t('errors.minValue', { ns: 'common', value: 0 })),
    currency: Yup.string().required(t('errors.requiredField', { ns: 'common' })),
    duration: Yup.number()
        .required(t('errors.requiredField', { ns: 'common' }))
        .min(1, t('errors.minValue', { ns: 'common', minValue: 1 })),
    breakBetweenServices: Yup.number().min(0, t('errors.minValue', { ns: 'common', minValue: 0 })),
});
