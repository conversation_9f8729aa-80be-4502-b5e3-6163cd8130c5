import CloseIcon from '@mui/icons-material/Close';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import LoadingButton from '@mui/lab/LoadingButton';
import Box from '@mui/material/Box';
import Button, { ButtonProps } from '@mui/material/Button';
import Dialog, { DialogProps } from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import Grid from '@mui/material/Grid';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import MenuItem from '@mui/material/MenuItem';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import { alpha, lighten, styled } from '@mui/material/styles';
import { Form, Formik, FormikProps } from 'formik';
import { useSnackbar } from 'notistack';
import React, { useCallback, useMemo, useRef, useState } from 'react';
import { ChromePicker, ColorResult } from 'react-color';
import { useTranslation } from 'react-i18next';
import { AvailableCurrencies } from '@bookr-technologies/api/constants/AvailableCurrencies';
import { servicesEndpoint } from '@bookr-technologies/api/endpoints/servicesEndpoint';
import { IntegrationTypeEnum } from '@bookr-technologies/api/integrations';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { FormikButton, FormikTextField } from '@bookr-technologies/ui/Fields';
import { FormikCheckbox } from '@bookr-technologies/ui/Fields/FormikCheckbox';
import FormikSelect from '@bookr-technologies/ui/Fields/FormikSelect';
import { useIsMounted } from '@bookr-technologies/ui/hooks/useIsMounted';
import { serviceDialogValidationSchema } from './serviceDialogValidationSchema';

interface ServiceDialogProps extends Omit<DialogProps, 'onSubmit'> {
    service: ServiceModel | null;

    onSubmit(service: ServiceModel): void | Promise<void>;

    onDelete?: (service: ServiceModel) => void | Promise<void>;
}

const PREDEFINED_COLORS = ['#2F80FB', '#05944F', '#F26C28', '#9233F0', '#09C0CC', '#59616D'];

const StyledColorButton = styled(
    ({
        value,
        onClick,
        ...rest
    }: Omit<ButtonProps, 'onClick'> & {
        value: string;
        onClick(color: string): void;
    }) => {
        const handleClick = useCallback(() => onClick(value), [onClick, value]);
        return <Button onClick={handleClick} {...rest} />;
    },
)(({ value }) => ({
    width: 30,
    height: 30,
    minWidth: 30,
    padding: 0,
    margin: '0 2px 2px',
    borderRadius: 8,
    backgroundColor: value,
    '&:hover': {
        backgroundColor: lighten(value, 0.4),
    },
}));

export function ServiceDialog({ open, service, onSubmit, onClose, onDelete }: ServiceDialogProps) {
    const isNew = !service?.id;
    const formikRef = useRef<FormikProps<ServiceModel> | null>(null);
    const { t } = useTranslation('serviceDialog');
    const { enqueueSnackbar } = useSnackbar();
    const isMounted = useIsMounted();
    const [isDeleting, setIsDeleting] = useState(false);

    const minutesAdornment = useMemo(
        () => ({
            endAdornment: (
                <InputAdornment position={'end'} sx={{ marginTop: -2 }}>
                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                        {t('minutes')}
                    </Typography>
                </InputAdornment>
            ),
        }),
        [t],
    );

    const handleClose = useCallback(() => {
        if (onClose) {
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            (onClose as any)();
        }
    }, [onClose]);

    const handleSetColor = useCallback((color: string) => {
        formikRef.current?.setFieldValue('color', color);
    }, []);

    const handleColor = useCallback(({ hex }: ColorResult) => handleSetColor(hex), [handleSetColor]);

    const handleResetColor = useCallback(() => {
        formikRef.current?.setFieldValue('color', service?.color);
    }, [service?.color]);

    const handleDelete = useCallback(async () => {
        if (!service?.id) {
            return;
        }

        setIsDeleting(true);
        try {
            await servicesEndpoint.destroy(service.id);
            if (onDelete) {
                await onDelete(service);
            }
        } catch (e) {
            const message = getErrorMessage(e, 'errorDeletingService');
            enqueueSnackbar(t(message), { variant: 'error' });
        }
        isMounted.only(() => setIsDeleting(false));
    }, [enqueueSnackbar, isMounted, onDelete, service, t]);

    const handleSubmit = useCallback(
        async (values: ServiceModel) => {
            values.onlineEventIntegrationType = values.onlineEventIntegrationType || IntegrationTypeEnum.GoogleCalendar;
            const result = await servicesEndpoint.upsert(values?.id, values);
            await onSubmit(result);
            handleClose();
        },
        [handleClose, onSubmit],
    );

    if (!service) {
        return null;
    }

    return (
        <StyledDialog open={open} onClose={handleClose} fullWidth>
            <Grid container alignItems={'center'} justifyContent={'space-between'} px={4} pt={3}>
                <Typography variant={'h5'} fontWeight={700}>
                    {t(isNew ? 'addServiceTitle' : 'editServiceTitle')}
                </Typography>
                <IconButton onClick={handleClose}>
                    <CloseIcon />
                </IconButton>
            </Grid>
            <Formik
                initialValues={service}
                onSubmit={handleSubmit}
                innerRef={formikRef}
                validationSchema={serviceDialogValidationSchema}
                validateOnMount
                validateOnChange
            >
                {({ values }) => (
                    <Form>
                        <DialogContent>
                            <Grid container px={1}>
                                <Grid item xs pr={8}>
                                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                        {t('serviceInformation')}
                                    </Typography>

                                    <Grid container mt={1}>
                                        <StyledTextField
                                            name={'name'}
                                            label={t('serviceName')}
                                            size={'small'}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid container spacing={2} mt={1}>
                                        <Grid item xs>
                                            <StyledTextField
                                                type={'number'}
                                                inputProps={{ min: 0 }}
                                                name={'price'}
                                                label={t('price')}
                                                size={'small'}
                                                fullWidth
                                            />
                                        </Grid>
                                        <Grid item xs>
                                            <StyledFormikSelect
                                                name={'currency'}
                                                label={t('currency')}
                                                size={'small'}
                                                fullWidth
                                            >
                                                {Object.values(AvailableCurrencies).map((currency) => (
                                                    <MenuItem key={currency} value={currency}>
                                                        {currency}
                                                    </MenuItem>
                                                ))}
                                            </StyledFormikSelect>
                                        </Grid>
                                    </Grid>
                                    <Grid container mt={3}>
                                        <StyledTextField
                                            type={'number'}
                                            inputProps={{ min: 1 }}
                                            InputProps={minutesAdornment}
                                            name={'duration'}
                                            label={t('duration')}
                                            size={'small'}
                                            fullWidth
                                        />
                                    </Grid>
                                    <Grid container alignItems={'center'} mt={4}>
                                        <Typography variant={'body2'} fontWeight={500} color={'textSecondary'} mr={1}>
                                            {t('breakBetweenServices')}
                                        </Typography>
                                        <Tooltip title={t('breakBetweenServicesTooltip') as string} placement={'top'}>
                                            <Grid container item xs alignItems={'center'} maxWidth={20}>
                                                <ErrorOutlineIcon fontSize={'small'} color={'accent'} />
                                            </Grid>
                                        </Tooltip>
                                    </Grid>
                                    <Grid container mt={1}>
                                        <StyledTextField
                                            type={'number'}
                                            inputProps={{ min: 0 }}
                                            InputProps={minutesAdornment}
                                            name={'breakBetweenServices'}
                                            label={t('breakBetweenServices')}
                                            size={'small'}
                                            fullWidth
                                        />
                                    </Grid>
                                </Grid>
                                <Grid item xs maxWidth={'220px !important'}>
                                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'}>
                                        {t('chooseColor')}
                                    </Typography>
                                    <Grid container justifyContent={'space-between'}>
                                        {PREDEFINED_COLORS.map((color) => (
                                            <StyledColorButton key={color} value={color} onClick={handleSetColor} />
                                        ))}
                                    </Grid>

                                    <Grid container mt={1} mb={2}>
                                        <ChromePicker color={values.color} onChangeComplete={handleColor} />
                                    </Grid>

                                    <Grid container justifyContent={'flex-end'}>
                                        <Button size={'small'} color={'accent'} onClick={handleResetColor}>
                                            {t('resetColor')}
                                        </Button>
                                    </Grid>
                                </Grid>

                                <Stack mt={1}>
                                    <Grid container alignItems={'center'}>
                                        <StyledFormikCheckbox
                                            name={'onlineEvent'}
                                            color={'accent'}
                                            value={true}
                                            id={'onlineServiceField'}
                                        />
                                        <Typography
                                            variant={'body2'}
                                            fontWeight={500}
                                            color={'textSecondary'}
                                            mx={1}
                                            component={'label'}
                                            htmlFor={'onlineServiceField'}
                                            sx={{ cursor: 'pointer' }}
                                        >
                                            {t('serviceOnlineEvent')}
                                        </Typography>
                                        <Tooltip title={t('serviceOnlineEventTooltip') as string} placement={'top'}>
                                            <Grid container item xs alignItems={'center'} maxWidth={20}>
                                                <ErrorOutlineIcon fontSize={'small'} color={'accent'} />
                                            </Grid>
                                        </Tooltip>
                                    </Grid>
                                    <Grid container alignItems={'center'}>
                                        <StyledFormikCheckbox
                                            name={'acceptsOnlinePayments'}
                                            color={'accent'}
                                            id={'paymentServiceField'}
                                        />
                                        <Typography
                                            variant={'body2'}
                                            fontWeight={500}
                                            color={'textSecondary'}
                                            mx={1}
                                            component={'label'}
                                            htmlFor={'paymentServiceField'}
                                            sx={{ cursor: 'pointer' }}
                                        >
                                            {t('serviceOnlinePayments')}
                                        </Typography>
                                        <Tooltip title={t('serviceOnlinePaymentsTooltip') as string} placement={'top'}>
                                            <Grid container item xs alignItems={'center'} maxWidth={20}>
                                                <ErrorOutlineIcon fontSize={'small'} color={'accent'} />
                                            </Grid>
                                        </Tooltip>
                                    </Grid>

                                    <Grid container alignItems={'center'}>
                                        <StyledFormikCheckbox
                                            name={'hiddenFromClients'}
                                            color={'accent'}
                                            value={true}
                                            id={'fieldHidden'}
                                        />
                                        <Typography
                                            variant={'body2'}
                                            fontWeight={500}
                                            color={'textSecondary'}
                                            mx={1}
                                            component={'label'}
                                            htmlFor={'fieldHidden'}
                                            sx={{ cursor: 'pointer' }}
                                        >
                                            {t('serviceHiddenFromClient')}
                                        </Typography>
                                        <Tooltip
                                            title={t('serviceHiddenFromClientTooltip') as string}
                                            placement={'top'}
                                        >
                                            <Grid container item xs alignItems={'center'} maxWidth={20}>
                                                <ErrorOutlineIcon fontSize={'small'} color={'accent'} />
                                            </Grid>
                                        </Tooltip>
                                    </Grid>
                                </Stack>

                                <Grid container alignItems={'center'} mt={2}>
                                    <Typography variant={'body2'} fontWeight={500} color={'textSecondary'} mr={1}>
                                        {t('description')}
                                    </Typography>
                                    <Typography variant={'body2'} color={alpha('#757575', 0.7)} fontSize={12}>
                                        ({t('optional')})
                                    </Typography>
                                </Grid>
                                <Grid container mt={1}>
                                    <StyledTextField
                                        InputProps={{
                                            style: DescriptionTextFieldInputStyle,
                                        }}
                                        name={'description'}
                                        rows={2}
                                        maxRows={4}
                                        fullWidth
                                        multiline
                                    />
                                </Grid>
                            </Grid>
                        </DialogContent>
                        <DialogActions>
                            <Grid container alignItems={'center'} justifyContent={'flex-end'} px={3} py={2}>
                                {!isNew ? (
                                    <StyledButton onClick={handleDelete} loading={isDeleting} color={'error'}>
                                        {t('deleteService')}
                                    </StyledButton>
                                ) : null}
                                <Box flexGrow={1} />
                                <CancelButton onClick={handleClose}>{t('cancel')}</CancelButton>
                                <SaveButton variant={'contained'} size={'small'} disableElevation>
                                    {t(isNew ? 'addServiceButton' : 'editServiceButton')}
                                </SaveButton>
                            </Grid>
                        </DialogActions>
                    </Form>
                )}
            </Formik>
        </StyledDialog>
    );
}

const buttonStyle = {
    height: 40,
    lineHeight: '40px',
    borderRadius: 10,
    paddingLeft: 22,
    paddingRight: 22,
};

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '.MuiDialog-paper': {
        maxWidth: 700,
        borderRadius: 16,
    },
    '.chrome-picker': {
        width: '100% !important',
        borderRadius: '10px !important',
        boxShadow: 'none !important',
        backgroundColor: '#eee !important',
        fontFamily: `${theme.typography.fontFamily} !important`,
        overflow: 'hidden',
    },
    '#rc-editable-input-2 + label': {
        fontWeight: 600,
    },
}));

const SaveButton = styled(FormikButton)({ ...buttonStyle });
const StyledButton = styled(LoadingButton)({
    ...buttonStyle,
    paddingLeft: 14,
    paddingRight: 14,
});

const CancelButton = styled(Button)({
    ...buttonStyle,
    color: '#afafaf',
    marginRight: 14,
});

const StyledTextField = styled(FormikTextField)({
    '.MuiOutlinedInput-root': {
        backgroundColor: '#eee',
    },
    '.MuiOutlinedInput-notchedOutline': {
        border: 'none',
    },
});

const StyledFormikSelect = styled(FormikSelect)({
    backgroundColor: '#eee',
    '.MuiOutlinedInput-notchedOutline': {
        border: 'none',
    },
});

const StyledFormikCheckbox = styled(FormikCheckbox)`
    padding-right: 0;
    padding-left: 0;
`;

const DescriptionTextFieldInputStyle = {
    height: 120,
    maxHeight: 120,
    alignItems: 'flex-start',
};
