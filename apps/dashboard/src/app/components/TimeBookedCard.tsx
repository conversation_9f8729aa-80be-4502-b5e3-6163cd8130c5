/* eslint-disable @typescript-eslint/no-explicit-any */
import Grid from '@mui/material/Grid';
import Paper from '@mui/material/Paper';
import Skeleton from '@mui/material/Skeleton';
import Typography from '@mui/material/Typography';
import { styled, useTheme } from '@mui/material/styles';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Cell, Pie, PieChart, Tooltip } from 'recharts';
import { UserPrivilegeType } from '@bookr-technologies/api/constants/UserPrivilegeType';
import { BookingStats } from '@bookr-technologies/api/endpoints/businessEndpoint';
import { getHoursFromMinutesRounded } from '@bookr-technologies/core/datetime';
import { formatNumber } from '@bookr-technologies/core/utils/formatters';
import { PrivilegeBasedAccess } from '@bookr-technologies/ui/PrivilegeBasedAccess';
import { StyledCustomTooltip } from './TotalEarningsCard';

export interface TimeBookedCardProps {
    bookingStats: BookingStats;
    loading: boolean;
}

const Root = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(3),
    flex: '1 1 auto',
    '.recharts-sector': {
        cursor: 'pointer',
    },
    '.TimeBookedCard-chart': {
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: theme.spacing(2),
    },
    '.TimeBookedCard-percentage': {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
    },
    '.TimeBookedCard-label': {
        maxWidth: '124px',
        margin: theme.spacing(0, 4),
        textAlign: 'center',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
    },
    '.TimeBookedCard-labelColor': {
        backgroundColor: theme.palette.accent.main,
        display: 'inline-block',
        width: 10,
        height: 10,
        marginBottom: theme.spacing(0.5),
        borderRadius: 5,
        '&.wasted': {
            backgroundColor: '#e2e2e2',
        },
    },
}));

export function TimeBookedCard({ bookingStats, loading }: TimeBookedCardProps) {
    const percentage = bookingStats.percentage;

    const { t } = useTranslation('timeBookedCard');
    const theme = useTheme();
    const [data, setData] = useState([
        {
            percentage: 100,
            color: '#e2e2e2',
            label: 'Time wasted in schedule',
            minutes: 0,
        },
        {
            percentage: 0,
            color: theme.palette.accent.main,
            label: 'Time used in schedule',
            minutes: 0,
            strokeLinejoin: 'round',
        },
    ]);

    useEffect(() => {
        const timeout = setTimeout(() => {
            if (!loading) {
                const diff = percentage < 100 && percentage >= 95 ? 2 : 0;
                setData([
                    {
                        percentage: 100 - percentage + diff,
                        color: '#e2e2e2',
                        label: t('timeWasted'),
                        minutes: bookingStats.totalWorkingMinutes - bookingStats.totalBookedMinutes,
                    },
                    {
                        percentage: percentage - diff,
                        color: theme.palette.accent.main,
                        label: t('timeUsed'),
                        minutes: bookingStats.totalBookedMinutes,
                        strokeLinejoin: 'round',
                    },
                ]);
            }
        }, 300);

        return () => clearTimeout(timeout);
    }, [
        bookingStats.totalWorkingMinutes,
        bookingStats.totalBookedMinutes,
        theme.palette.accent.main,
        percentage,
        loading,
        t,
    ]);

    return (
        <Root>
            <Grid container direction={'column'}>
                <Typography variant={'h5'} fontWeight={600}>
                    {t('timeBooked')}
                </Typography>

                <Typography variant={'caption'} color={'textSecondary'} fontWeight={500}>
                    {t('totalTime', { count: getHoursFromMinutesRounded(bookingStats.totalWorkingMinutes) })}
                </Typography>
            </Grid>
            <div className={'TimeBookedCard-chart'}>
                <Typography className={'TimeBookedCard-percentage'} variant={'h4'} fontWeight={600}>
                    {loading ? <Skeleton width={84} height={42} /> : <>{formatNumber(percentage)}%</>}
                </Typography>
                <PieChart width={200} height={200}>
                    <Pie
                        data={data}
                        dataKey={'percentage'}
                        nameKey={'label'}
                        outerRadius={92}
                        innerRadius={92}
                        strokeWidth={16}
                        animationDuration={600}
                    >
                        {data.reverse().map((entry, index) => (
                            <Cell
                                key={`cell-${index}`}
                                stroke={entry.color}
                                strokeLinejoin={entry.strokeLinejoin as any}
                            />
                        ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} animationDuration={600} />
                </PieChart>
            </div>

            <Grid container mt={3} alignItems={'flex-start'} justifyContent={'center'}>
                <Typography className={'TimeBookedCard-label'} variant={'caption'} fontWeight={500}>
                    <span className={'TimeBookedCard-labelColor used'} />
                    {t('timeUsed')}
                </Typography>
                <Typography className={'TimeBookedCard-label'} variant={'caption'} fontWeight={500}>
                    <span className={'TimeBookedCard-labelColor wasted'} />
                    {t('timeWasted')}
                </Typography>
            </Grid>

            <PrivilegeBasedAccess privileges={UserPrivilegeType.Professional} />
        </Root>
    );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const CustomTooltip = ({ active, payload }: Record<string, any>) => {
    const { t } = useTranslation('common');

    if (active && payload && payload.length) {
        const { minutes, label } = payload[0].payload;
        const hours = getHoursFromMinutesRounded(minutes);

        return (
            <StyledCustomTooltip>
                <Typography variant={'caption'} fontWeight={600}>
                    {label}
                </Typography>
                <Typography variant={'caption'} color={'textSecondary'} fontWeight={500}>
                    {formatNumber(hours)} {t('hoursLabel', { count: hours })}
                </Typography>
            </StyledCustomTooltip>
        );
    }

    return null;
};
