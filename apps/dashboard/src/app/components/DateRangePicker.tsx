import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import moment, { Moment } from 'moment-timezone';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { DateRangePicker as ReactDatesDateRangePicker, FocusedInputShape } from 'react-dates';

export interface DateRangePickerProps {
    startDate: moment.Moment | null;
    endDate: moment.Moment | null;
    onDatesChange: (value: { startDate: moment.Moment | null; endDate: moment.Moment | null }) => void;
    id?: string;
}

export function DateRangePicker({ startDate, endDate, onDatesChange, id, ...rest }: DateRangePickerProps) {
    const [focusedInput, setFocusedInput] = useState<FocusedInputShape | null>(null);
    const [start, setStart] = useState(startDate ?? null);
    const [end, setEnd] = useState(endDate ?? null);
    const name = useMemo(() => id || `date-range-picker-${1000 + Math.floor(Math.random() * 8999)}`, [id]);
    const now = useMemo(() => moment(), []);
    const handleFocusChange = useCallback((value: any) => setFocusedInput(value), []);
    const handleDatesChange = useCallback<DateRangePickerProps['onDatesChange']>(({ startDate, endDate }) => {
        setStart(startDate);
        setEnd(endDate);
    }, []);

    useEffect(() => {
        setStart(startDate);
        setEnd(endDate);
    }, [endDate, startDate]);

    return (
        <ReactDatesDateRangePicker
            startDate={start ?? null}
            startDateId={`${name}_start`}
            endDate={end ?? null}
            endDateId={`${name}_end`}
            onDatesChange={handleDatesChange}
            onClose={onDatesChange}
            onFocusChange={handleFocusChange}
            focusedInput={focusedInput}
            isOutsideRange={(day: Moment) => day.startOf('day').isAfter(now.startOf('day'))}
            customArrowIcon={<ArrowForwardIcon />}
            {...rest}
        />
    );
}
