import { useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuthStore } from '@bookr-technologies/store';
import { BusinessSignUpForm, useBusinessStore } from '@bookr-technologies/store/businessStore';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';

export function useSignUpBusiness(options?: { redirectTo: string }): {
    initialState: BusinessSignUpForm;
    handleSubmit: (values: BusinessSignUpForm) => Promise<void>;
    nextPath: string | null;
    checking: boolean;
} {
    const navigate = useNavigate();
    const location = useLocation();
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const locationState = location.state as any;
    const user = useCurrentUser();
    const metadata = useAuthStore((state) => state.metadata);
    const authenticated = useAuthStore((state) => state.authenticated);

    const hasBusiness = !!user?.business?.id;
    const commitSignUpForm = useBusinessStore((state) => state.commitSignUpForm);
    const initialState = useBusinessStore((state) => state.signUpForm);
    const commitOperationsCounter = useBusinessStore((state) => state.commitOperationsCounter);

    const handleSubmit = useCallback(
        async (values: BusinessSignUpForm) => {
            if (options?.redirectTo) {
                commitSignUpForm(values);
                navigate(options.redirectTo, { state: locationState ?? {} });
            }
        },
        [commitSignUpForm, locationState, navigate, options?.redirectTo],
    );

    const redirectPath = useMemo(() => {
        if (location.pathname === '/auth/sign-up/business' || commitOperationsCounter > 0) {
            return null;
        }

        return '/auth/sign-up/business';
    }, [commitOperationsCounter, location.pathname]);

    return {
        initialState,
        handleSubmit,
        checking: authenticated === null,
        nextPath: !metadata?.verified
            ? '/auth/sign-up/verification'
            : hasBusiness
            ? locationState?.postSignInUrl ?? '/'
            : redirectPath,
    };
}
