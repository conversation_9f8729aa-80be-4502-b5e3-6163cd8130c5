import { mapLimit } from 'async';
import { useSnackbar } from 'notistack';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useQuery } from 'react-query';
import { businessEndpoint } from '@bookr-technologies/api/endpoints';
import { UserModel } from '@bookr-technologies/api/models/UserModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useBusinessStore } from '@bookr-technologies/store/businessStore';
import {
    workingHoursToWorkingProgram,
    workingProgramToWorkingHours,
} from '@bookr-technologies/store/utils/workingProgram';
import { DEFAULT_LOCATION } from '@bookr-technologies/ui/LocationDialog';
import { GalleryFile } from '../components/GalleryGrid/GalleryFile';
import { ImagePickerRecord } from '../components/ImagePicker';

export function useBusinessSettings() {
    const { t } = useTranslation('businessSettingsPage');
    const fetchCurrentUserBusiness = useBusinessStore((state) => state.fetchCurrentUserBusiness);
    const { data, isLoading } = useQuery(`readBusiness:current`, () => fetchCurrentUserBusiness());
    const { enqueueSnackbar } = useSnackbar();
    const initialValues = useMemo(
        () => ({
            // General
            profilePicture: {
                image: data?.profilePicture,
                file: null,
            } as ImagePickerRecord,
            businessName: data?.name ?? '',
            phoneNumber: data?.phoneNumber ?? '',
            businessDescription: data?.description ?? '',
            locationName: data?.formattedAddress ?? '',
            facebook: data?.facebookURL ?? '',
            instagram: data?.instagramURL ?? '',
            website: data?.websiteURL ?? '',
            location: {
                lat: data?.latitude ?? DEFAULT_LOCATION.lat,
                lng: data?.longitude ?? DEFAULT_LOCATION.lng,
            },

            // Categories
            categories: (data?.categories ?? []).map((category) => category.name),

            // Gallery
            gallery: {
                files: [],
                photos: data?.photos ?? [],
            } as { files: GalleryFile[]; photos: string[] },

            // Working Hours
            workingHours: workingHoursToWorkingProgram(data?.workingHours ?? []),

            // Staff Members
            staffMembers: data?.staffMembers ?? [],
            email: '',
        }),
        [
            data?.categories,
            data?.description,
            data?.facebookURL,
            data?.formattedAddress,
            data?.instagramURL,
            data?.latitude,
            data?.longitude,
            data?.name,
            data?.phoneNumber,
            data?.photos,
            data?.profilePicture,
            data?.staffMembers,
            data?.websiteURL,
            data?.workingHours,
        ],
    );

    const handleSubmitGeneral = useCallback(
        async (values: typeof initialValues) => {
            if (!data?.id) {
                return;
            }

            try {
                await businessEndpoint.update(data.id, {
                    name: values.businessName,
                    description: values.businessDescription,
                    phoneNumber: values.phoneNumber,
                    facebookURL: values.facebook,
                    instagramURL: values.instagram,
                    websiteURL: values.website,
                    latitude: values.location.lat,
                    longitude: values.location.lng,
                    formattedAddress: values.locationName,
                });
            } catch (e) {
                const message = getErrorMessage(e, 'errorUpdatingSettings');
                enqueueSnackbar(t(message), { variant: 'error' });
            }

            if (values.profilePicture.file) {
                try {
                    await businessEndpoint.attachProfilePicture(values.profilePicture.file);
                } catch (e) {
                    const message = getErrorMessage(e, 'errorUpdatingProfilePicture');
                    enqueueSnackbar(t(message), { variant: 'error' });
                }
            }
        },
        [data?.id, enqueueSnackbar, t],
    );

    const handleSubmitCategories = useCallback(
        async (values: typeof initialValues) => {
            if (!data?.id) {
                return;
            }

            try {
                await businessEndpoint.updateCategories(values.categories.map((name) => ({ name })));
            } catch (e) {
                const message = getErrorMessage(e, 'errorUpdatingCategories');
                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [data?.id, enqueueSnackbar, t],
    );

    const handleSubmitStaff = useCallback(
        async (values: typeof initialValues) => {
            if (!data?.id) {
                return;
            }

            const staff = values.staffMembers.sort(UserModel.sort);
            const oldStaffHash = initialValues.staffMembers.map(({ uid }) => uid).join(',');
            const newStaffHash = staff.map(({ uid }) => uid).join(',');

            if (newStaffHash !== oldStaffHash) {
                try {
                    await businessEndpoint.staffRank(staff.map(({ uid }) => ({ uid })));
                } catch (e) {
                    const message = getErrorMessage(e, 'errorUpdatingMembers');
                    enqueueSnackbar(t(message), { variant: 'error' });
                }
            }

            if (values.email) {
                try {
                    await businessEndpoint.inviteUser({
                        email: `${values.email}`,
                    });
                    enqueueSnackbar('Invitation sent!', { variant: 'success' });
                } catch (e) {
                    const message = getErrorMessage(e, 'errorInvitingEmployee');
                    enqueueSnackbar(t(message), { variant: 'error' });
                }
            }
        },
        [data?.id, enqueueSnackbar, initialValues.staffMembers, t],
    );

    const handleSubmitGallery = useCallback(
        async (values: typeof initialValues) => {
            if (!data?.id) {
                return;
            }

            let failed = 0;
            await mapLimit(values.gallery.files, 3, async ({ file }: { file: File }) => {
                try {
                    await businessEndpoint.attachWorkplacePicture(file);
                } catch (e) {
                    failed++;
                }
            });

            if (failed > 0) {
                let message = 'warningUploadingImages';
                if (failed === values.gallery.files?.length) {
                    message = 'errorUploadingImages';
                }

                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [data?.id, enqueueSnackbar, t],
    );

    const handleSubmitWorkingHours = useCallback(
        async (values: typeof initialValues) => {
            if (!data?.id) {
                return;
            }

            try {
                await businessEndpoint.updateWorkingHours(workingProgramToWorkingHours(values.workingHours));
            } catch (e) {
                const message = getErrorMessage(e, 'errorUpdatingWorkingHours');
                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [data?.id, enqueueSnackbar, t],
    );

    return {
        businessId: data?.id,
        initialValues,
        isLoading,
        handleSubmitGeneral,
        handleSubmitCategories,
        handleSubmitStaff,
        handleSubmitGallery,
        handleSubmitWorkingHours,
    };
}
