import { useRef } from 'react';
import { DropTargetMonitor, useDrag, useDrop, XYCoord } from 'react-dnd';

interface DragItem {
    index: number;
    id: string;
    type: string;
}

export interface DragElementProps {
    index: number;

    onMove(dragIndex: number, hoverIndex: number): void;
}

export function useDragElement({ index, onMove }: DragElementProps) {
    const elRef = useRef<HTMLLIElement | null>(null);

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const [{ handlerId }, drop] = useDrop<DragItem, any, any>({
        accept: 'card',
        collect: (monitor: DropTargetMonitor) => ({
            handlerId: monitor.getHandlerId(),
        }),
        hover(item: DragItem, monitor: DropTargetMonitor) {
            if (!elRef.current) {
                return;
            }
            const dragIndex = item.index;
            const hoverIndex = index;

            // Don't replace items with themselves
            if (dragIndex === hoverIndex) {
                return;
            }

            // Determine rectangle on screen
            const hoverBoundingRect = elRef.current?.getBoundingClientRect();

            // Get vertical middle
            const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

            // Determine mouse position
            const clientOffset = monitor.getClientOffset();

            // Get pixels to the top
            const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

            if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
                return;
            }

            if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
                return;
            }

            // Time to actually perform the action
            onMove(dragIndex, hoverIndex);
            item.index = hoverIndex;
        },
    });

    const [{ isDragging }, drag] = useDrag({
        type: 'card',
        item: () => ({ index }),
        collect: (monitor) => ({
            isDragging: monitor.isDragging(),
        }),
    });

    drag(drop(elRef));

    return {
        handlerId,
        elRef,
        isDragging,
    };
}
