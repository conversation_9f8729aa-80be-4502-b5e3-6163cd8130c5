import { useSnackbar } from 'notistack';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { servicesEndpoint } from '@bookr-technologies/api/endpoints/servicesEndpoint';
import { usersEndpoint } from '@bookr-technologies/api/endpoints/usersEndpoint';
import { ServiceModel } from '@bookr-technologies/api/models/ServiceModel';
import { getErrorMessage } from '@bookr-technologies/api/utils/getErrorMessage';
import { useAuthStore } from '@bookr-technologies/store';
import { useCurrentUser } from '@bookr-technologies/store/hooks/useUser';
import {
    workingHoursToWorkingProgram,
    workingProgramToWorkingHours,
} from '@bookr-technologies/store/utils/workingProgram';
import { ImagePickerRecord } from '../components/ImagePicker';

export function useProfileSettings() {
    const { t } = useTranslation('profileSettingsPage');
    const { enqueueSnackbar } = useSnackbar();
    const user = useCurrentUser();
    const resolveUser = useAuthStore((state) => state.resolveUser);
    const setUser = useAuthStore((state) => state.setUser);
    const initialValues = useMemo(() => {
        return {
            profilePicture: {
                image: user?.photoURL,
                file: null,
            } as ImagePickerRecord,
            displayName: user?.displayName ?? '',
            phoneNumber: user?.phoneNumber ?? '',
            email: user?.email ?? '',
            maxFutureDaysAppointment: user?.maxFutureDaysAppointment ?? 14,
            workingHours: workingHoursToWorkingProgram(user?.workingHours ?? []),
            services: user?.services ?? [],
        };
    }, [
        user?.displayName,
        user?.email,
        user?.maxFutureDaysAppointment,
        user?.phoneNumber,
        user?.photoURL,
        user?.services,
        user?.workingHours,
    ]);

    const handleSubmitGeneral = useCallback(
        async (values: typeof initialValues) => {
            if (!user?.uid) {
                return;
            }

            try {
                const data = {
                    email: values.email,
                    phoneNumber: values.phoneNumber,
                    displayName: values.displayName,
                    maxFutureDaysAppointment: values.maxFutureDaysAppointment,
                };

                await usersEndpoint.update(user?.uid, data);
            } catch (e) {
                const message = getErrorMessage(e, 'errorUpdatingProfileSettings');
                enqueueSnackbar(t(message), { variant: 'error' });
            }

            if (values.profilePicture.file) {
                try {
                    await usersEndpoint.attachProfilePicture(values.profilePicture.file);
                } catch (e) {
                    const message = getErrorMessage(e, 'errorUpdatingProfilePicture');
                    enqueueSnackbar(t(message), { variant: 'error' });
                }
            }

            await resolveUser();
        },
        [enqueueSnackbar, resolveUser, t, user?.uid],
    );

    const handleSubmitServices = useCallback(
        async (values: typeof initialValues) => {
            if (!user?.uid) {
                return;
            }

            const staff = values.services.sort(ServiceModel.sort);
            const oldStaffHash = initialValues.services.map(({ id }) => id).join(',');
            const newStaffHash = staff.map(({ id }) => id).join(',');

            if (newStaffHash !== oldStaffHash) {
                try {
                    await servicesEndpoint.updateRank(staff);
                } catch (e) {
                    const message = getErrorMessage(e);
                    enqueueSnackbar(t(message), { variant: 'error' });
                }
                await resolveUser();
            }
        },
        [enqueueSnackbar, initialValues.services, resolveUser, t, user?.uid],
    );

    const handleSubmitWorkingHours = useCallback(
        async (values: typeof initialValues) => {
            if (!user?.uid) {
                return;
            }

            try {
                const newUser = await usersEndpoint.updateWorkingHours(
                    workingProgramToWorkingHours(values.workingHours),
                );

                setUser(newUser);
            } catch (e) {
                const message = getErrorMessage(e, 'errorUpdatingWorkingHours');
                enqueueSnackbar(t(message), { variant: 'error' });
            }
        },
        [enqueueSnackbar, setUser, t, user?.uid],
    );

    return {
        userId: user?.uid,
        initialValues,
        handleSubmitGeneral,
        handleSubmitServices,
        handleSubmitWorkingHours,
    };
}
