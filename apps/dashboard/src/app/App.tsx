import React, { lazy, Suspense } from 'react';
import { Navigate, Outlet, Route, Routes } from 'react-router-dom';
import { PageLoader } from '@bookr-technologies/ui/PageLoader';
import { ProtectedRoute } from '@bookr-technologies/ui/ProtectedRoute';
import { Layout } from './components/Layout';
import { LogoutPage } from './pages/LogoutPage';

// Auth pages
const SignInPage = lazy(() => import('./pages/SignInPage'));
const AuthConfirmationPage = lazy(() => import('./pages/AuthConfirmationPage'));
const SignUpPage = lazy(() => import('./pages/SignUp/SignUpPage'));
const SignUpVerificationPage = lazy(() => import('./pages/SignUp/SignUpVerificationPage'));
const SignUpBusinessPage = lazy(() => import('./pages/SignUp/BusinessPage'));
const SignUpBusinessTypePage = lazy(() => import('./pages/SignUp/BusinessTypePage'));
const SignUpBusinessLocationPage = lazy(() => import('./pages/SignUp/BusinessLocationPage'));
const SignUpBusinessProgramPage = lazy(() => import('./pages/SignUp/BusinessProgramPage'));
const SignUpBusinessIdentityPage = lazy(() => import('./pages/SignUp/BusinessIdentityPage'));

// Appointments pages
const AppointmentsPage = lazy(() => import('./pages/Appointments/AppointmentsPage'));
const AppointmentDetailsPage = lazy(() => import('./pages/Appointments/AppointmentDetailsPage'));
const AppointmentsPrintPage = lazy(() => import('./pages/Appointments/AppointmentsPrintPage'));

// Clients pages
const ClientsPage = lazy(() => import('./pages/Clients/ClientsPage'));
const ClientPage = lazy(() => import('./pages/Clients/ClientPage'));

// Feedback pages
const FeedbackPage = lazy(() => import('./pages/FeedbackPage'));

// Sales Pages
const SalesOverviewPage = lazy(() => import('./pages/Sales/OverviewPage'));
const SalesActivityPage = lazy(() => import('./pages/Sales/ActivityPage/ActivityPage'));

// Settings Pages
const BusinessSettingsCategoriesPage = lazy(() => import('./pages/BusinessSettings/CategoriesPage'));
const BusinessSettingsGalleryPage = lazy(() => import('./pages/BusinessSettings/GalleryPage'));
const BusinessSettingsGeneralPage = lazy(() => import('./pages/BusinessSettings/GeneralPage'));
const BusinessSettingsWorkingHoursPage = lazy(() => import('./pages/BusinessSettings/WorkingHoursPage'));
const BusinessSettingsStaffPage = lazy(() => import('./pages/BusinessSettings/StaffPage'));
const BusinessSettingsVirtualTourPage = lazy(() => import('./pages/BusinessSettings/VirtualTourPage'));
const BusinessSettingsNotificationsPage = lazy(() => import('./pages/BusinessSettings/NotificationsPage'));
const BusinessSettingsIntegrationsPage = lazy(() => import('./pages/BusinessSettings/IntegrationsPage'));
const ProfileSettingsGeneralPage = lazy(() => import('./pages/ProfileSettings/GeneralPage'));
const ProfileSettingsServicesPage = lazy(() => import('./pages/ProfileSettings/ServicesPage'));
const ProfileSettingsPaymentsPage = lazy(() => import('./pages/ProfileSettings/PaymentsPage'));
const ProfileSettingsWorkingHoursPage = lazy(() => import('./pages/ProfileSettings/WorkingHoursPage'));
const ApplicationSettingsGeneralPage = lazy(() => import('./pages/ApplicationSettings/GeneralPage'));
const ApplicationSettingsSubscriptionPage = lazy(() => import('./pages/ApplicationSettings/SubscriptionPage'));
const ApplicationSettingsMessagesPage = lazy(() => import('./pages/ApplicationSettings/MessagesPage'));
const ApplicationSettingsOthersPage = lazy(() => import('./pages/ApplicationSettings/OthersPage'));

// Subscriptions Pages
const SubscriptionsPlanSelectPage = lazy(() => import('./pages/Subscriptions/PlanSelectPage'));
const CalendarPage = lazy(() => import('./pages/CalendarPage'));

export function App() {
    return (
        <Routes>
            <Route path={'/'} element={<Layout />}>
                <Route path={''} element={<Navigate to={'/appointments'} replace />} />
                <Route path={'appointments'}>
                    <Route path={''} element={ProtectedRoute(AppointmentsPage)} />
                    <Route path={':appointmentId'} element={ProtectedRoute(AppointmentDetailsPage)} />
                </Route>
                <Route path={'clients'}>
                    <Route path={''} element={ProtectedRoute(ClientsPage)} />
                    <Route path={':clientId'} element={ProtectedRoute(ClientPage)} />
                </Route>
                <Route path={'feedback'} element={ProtectedRoute(FeedbackPage)} />

                <Route path={'sales'}>
                    <Route path={''} element={<Navigate to={'/sales/overview'} replace />} />
                    <Route path={'overview'} element={ProtectedRoute(SalesOverviewPage)} />
                    <Route path={'activity'} element={ProtectedRoute(SalesActivityPage)} />
                </Route>
                <Route path={'settings'}>
                    <Route path={''} element={<Navigate to={'/settings/business/general'} replace />} />
                    <Route path={'business'}>
                        <Route path={''} element={<Navigate to={'/settings/business/general'} replace />} />
                        <Route path={'categories'} element={ProtectedRoute(BusinessSettingsCategoriesPage)} />
                        <Route path={'gallery'} element={ProtectedRoute(BusinessSettingsGalleryPage)} />
                        <Route path={'general'} element={ProtectedRoute(BusinessSettingsGeneralPage)} />
                        <Route path={'working-hours'} element={ProtectedRoute(BusinessSettingsWorkingHoursPage)} />
                        <Route path={'staff'} element={ProtectedRoute(BusinessSettingsStaffPage)} />
                        <Route path={'virtual-tour'} element={ProtectedRoute(BusinessSettingsVirtualTourPage)} />
                        <Route path={'notifications'} element={ProtectedRoute(BusinessSettingsNotificationsPage)} />
                        <Route path={'integrations'} element={ProtectedRoute(BusinessSettingsIntegrationsPage)} />
                    </Route>
                    <Route path={'profile'}>
                        <Route path={''} element={<Navigate to={'/settings/profile/general'} replace />} />
                        <Route path={'general'} element={ProtectedRoute(ProfileSettingsGeneralPage)} />
                        <Route path={'services'} element={ProtectedRoute(ProfileSettingsServicesPage)} />
                        <Route path={'payments'} element={ProtectedRoute(ProfileSettingsPaymentsPage)} />
                        <Route path={'working-hours'} element={ProtectedRoute(ProfileSettingsWorkingHoursPage)} />
                    </Route>
                    <Route path={'application'}>
                        <Route path={''} element={<Navigate to={'/settings/application/general'} replace />} />
                        <Route path={'general'} element={ProtectedRoute(ApplicationSettingsGeneralPage)} />
                        <Route path={'subscription'} element={ProtectedRoute(ApplicationSettingsSubscriptionPage)} />
                        <Route path={'messages'} element={ProtectedRoute(ApplicationSettingsMessagesPage)} />
                        <Route path={'others'} element={ProtectedRoute(ApplicationSettingsOthersPage)} />
                    </Route>
                </Route>
            </Route>

            <Route path={'/subscription/plans/select'} element={<Layout noSidebar noHeader />}>
                <Route path={''} element={ProtectedRoute(SubscriptionsPlanSelectPage)} />
            </Route>

            <Route
                path={'/exports'}
                element={
                    <Suspense fallback={<PageLoader />}>
                        <Outlet />
                    </Suspense>
                }
            >
                <Route path={'appointments'} element={ProtectedRoute(AppointmentsPrintPage)} />
            </Route>

            <Route path={'/auth'} element={<Layout noSidebar noHeader />}>
                <Route path={'confirmation'} element={<AuthConfirmationPage />} />
                <Route path={'logout'} element={<LogoutPage />} />
                <Route path={'sign-in'} element={<SignInPage />} />
                <Route path={'sign-up'} element={<SignUpPage />} />
                <Route path={'sign-up/verification'} element={<SignUpVerificationPage />} />
                <Route path={'sign-up/business'} element={<SignUpBusinessPage />} />
                <Route path={'sign-up/business/type'} element={<SignUpBusinessTypePage />} />
                <Route path={'sign-up/business/location'} element={<SignUpBusinessLocationPage />} />
                <Route path={'sign-up/business/program'} element={<SignUpBusinessProgramPage />} />
                <Route path={'sign-up/business/identity'} element={<SignUpBusinessIdentityPage />} />
            </Route>

            <Route path={'/calendar'} element={<Layout ContentComponent={CalendarPage} noHeader />} />
        </Routes>
    );
}
