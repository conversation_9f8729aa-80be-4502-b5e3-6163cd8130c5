@import url('https://cdn.jsdelivr.net/npm/@xz/fonts@1/serve/plus-jakarta-display.min.css');

body {
    background-color: unset;
}
.DateRangePicker,
.DateRangePickerInput {
    display: block;
}

.DateRangePickerInput,
.DateRangePicker_picker,
.DayPicker {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(5, 16, 55, 0.1);
}

.DayPicker {
    overflow: hidden;
}

.DateInput {
    width: 130px;
    background-color: transparent;
}

.DateInput_input {
    padding: 20px 0 19px;
    border: none !important;
    text-align: center;
    background-color: transparent;
    font-family: inherit;
    font-size: 16px;
    font-weight: 500;
    line-height: 25px;
}

.DateRangePicker_picker,
.DateInput_fang {
    margin-top: 10px;
}

.DayPickerNavigation_button {
    border-radius: 8px;
}

.DayPicker_weekHeader_li {
    font-weight: 500;
}

.CalendarDay {
    border: none !important;
    border-radius: 20px;
    position: relative;
    z-index: 1;
    font-size: 14px;
    font-weight: 500;
}

.CalendarDay:not(.CalendarDay__blocked_out_of_range):hover,
.CalendarDay__hovered,
.CalendarDay__hovered_span,
.CalendarDay__selected_span,
.CalendarDay__selected {
    background-color: #eee;
    color: #222;
}

.CalendarDay__hovered,
.CalendarDay__selected_span,
.CalendarDay__hovered_span {
    border-radius: 0;
}

.CalendarDay__selected,
.CalendarDay__hovered_span:hover {
    color: #fff !important;
}

.CalendarDay__selected:before,
.CalendarDay__hovered_span:hover:before {
    content: '';
    display: block;
    background-color: #222;
    position: absolute;
    z-index: -1;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    border-radius: 20px;
}

.CalendarDay__selected_start {
    border-radius: 20px 0 0 20px;
}

.CalendarDay__selected_end {
    border-radius: 0 20px 20px 0;
}

.DayPickerKeyboardShortcuts_show:before {
    border-right-color: #222;
}

.DayPickerKeyboardShortcuts_show:hover:before {
    border-right-color: #444;
}

.DateRangePickerInput_arrow {
    display: inline-flex;
}

.recharts-layer.recharts-active-dot {
    fill: rgba(1, 166, 186, 0.1);
}
.recharts-layer.recharts-active-dot .recharts-dot {
    fill: #fff;
    stroke-width: 4;
    stroke: #2f80fb;
    r: 6px;
    box-shadow: 0 0 0 4px rgba(1, 166, 186, 0.1);
}

@media print {
    .ReactQueryDevtools {
        display: none;
    }
}
