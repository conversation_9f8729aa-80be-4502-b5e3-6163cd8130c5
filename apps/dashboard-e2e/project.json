{"$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/dashboard-e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "@nrwl/cypress:cypress", "options": {"cypressConfig": "apps/dashboard-e2e/cypress.json", "devServerTarget": "dashboard:serve"}, "configurations": {"production": {"devServerTarget": "dashboard:serve:production"}}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/dashboard-e2e/**/*.{js,ts}"]}}}, "tags": [], "implicitDependencies": ["dashboard"]}