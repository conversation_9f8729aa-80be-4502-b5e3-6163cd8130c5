<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="start:client" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/package.json" />
    <command value="run" />
    <scripts>
      <script value="start" />
    </scripts>
    <arguments value="client" />
    <node-interpreter value="project" />
    <envs />
    <method v="2" />
  </configuration>
</component>