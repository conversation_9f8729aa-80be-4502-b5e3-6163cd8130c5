<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="start:dashboard" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/package.json" />
    <command value="run" />
    <scripts>
      <script value="start" />
    </scripts>
    <arguments value="dashboard" />
    <node-interpreter value="project" />
    <envs />
    <method v="2" />
  </configuration>
</component>